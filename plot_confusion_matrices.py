#!/usr/bin/env python3
"""
Plot confusion matrices for RNA-FM and SpliceBERT-MS1024 models
"""

import os
import sys
import torch
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import confusion_matrix, classification_report
import json

sys.path.append('.')
from downstream.train_lncrna_function import load_fasta_data
from tokenizer.tokenization_opensource import OpenRnaLMTokenizer
from model.rnafm.modeling_rnafm import RnaFmForSequenceClassification
from model.splicebert.modeling_splicebert import SpliceBertForSequenceClassification

def load_model_and_predict(model_type, model_path, test_data_path):
    """Load model and get predictions on test set"""
    
    print(f"\n🔄 Loading {model_type.upper()} model...")
    
    # Load tokenizer
    if model_type == 'rna-fm':
        tokenizer = OpenRnaLMTokenizer.from_pretrained(
            './checkpoint/opensource/rna-fm/',
            model_max_length=1024,
            padding_side="right",
            use_fast=True,
            trust_remote_code=True,
        )
        model = RnaFmForSequenceClassification.from_pretrained(
            model_path,
            num_labels=2,
            problem_type="single_label_classification",
            trust_remote_code=True,
        )
    elif model_type == 'splicebert-ms1024':
        tokenizer = OpenRnaLMTokenizer.from_pretrained(
            './checkpoint/opensource/splicebert-ms1024/',
            model_max_length=1024,
            padding_side="right",
            use_fast=True,
            trust_remote_code=True,
        )
        model = SpliceBertForSequenceClassification.from_pretrained(
            model_path,
            num_labels=2,
            problem_type="single_label_classification",
            trust_remote_code=True,
        )
    
    model.eval()
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model.to(device)
    
    # Load test data
    sequences, labels = load_fasta_data(test_data_path)
    print(f"✅ Loaded {len(sequences)} test samples")
    
    # Get predictions
    predictions = []
    probabilities = []
    
    with torch.no_grad():
        for i, (seq, label) in enumerate(zip(sequences, labels)):
            if i % 100 == 0:
                print(f"   Processing sample {i}/{len(sequences)}")
            
            # Tokenize (simplified: use first 1024 characters)
            inputs = tokenizer(
                seq[:1024],
                padding='max_length',
                max_length=1024,
                truncation=True,
                return_tensors='pt'
            )
            
            inputs = {k: v.to(device) for k, v in inputs.items()}
            
            # Forward pass
            outputs = model(**inputs)
            logits = outputs.logits
            probs = torch.softmax(logits, dim=-1)
            
            predictions.append(torch.argmax(logits, dim=-1).cpu().item())
            probabilities.append(probs[0, 1].cpu().item())
    
    return np.array(labels), np.array(predictions), np.array(probabilities)

def plot_confusion_matrix(y_true, y_pred, model_name, save_path):
    """Plot confusion matrix with custom styling"""

    # Calculate confusion matrix
    cm = confusion_matrix(y_true, y_pred)

    # Calculate percentages
    cm_percent = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis] * 100

    # Create figure with proper size
    plt.figure(figsize=(8, 6))

    # Create heatmap with custom annotations
    ax = sns.heatmap(cm,
                     annot=False,  # We'll add custom annotations
                     fmt='d',
                     cmap='Blues',
                     square=True,
                     linewidths=2,
                     linecolor='white',
                     cbar_kws={"shrink": .8})

    # Add custom annotations with both count and percentage
    for i in range(2):
        for j in range(2):
            # Determine text color based on cell value (darker cells need white text)
            # True positives (1,1) and True negatives (0,0) are usually darker
            if (i == 0 and j == 0) or (i == 1 and j == 1):  # Diagonal elements (correct predictions)
                main_color = 'white'
                percent_color = 'lightgray'
            else:  # Off-diagonal elements (incorrect predictions)
                main_color = 'black'
                percent_color = 'gray'

            # Main count number (larger, bold)
            ax.text(j + 0.5, i + 0.35, str(cm[i, j]),
                   ha='center', va='center',
                   fontsize=20, fontweight='bold', color=main_color)
            # Percentage (smaller, below the count)
            ax.text(j + 0.5, i + 0.65, f'({cm_percent[i, j]:.1f}%)',
                   ha='center', va='center',
                   fontsize=12, color=percent_color)

    # Customize plot
    plt.title(f'{model_name} - Confusion Matrix\nlncRNA Function Prediction',
              fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('Predicted Label', fontsize=14, fontweight='bold')
    plt.ylabel('True Label', fontsize=14, fontweight='bold')

    # Set tick labels
    class_names = ['Non-functional (0)', 'Functional (1)']
    ax.set_xticklabels(class_names, fontsize=12)
    ax.set_yticklabels(class_names, fontsize=12, rotation=0)

    # Remove the metrics text box (as requested)

    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"✅ Confusion matrix saved: {save_path}")

    # Calculate metrics for return
    from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
    accuracy = accuracy_score(y_true, y_pred)
    precision = precision_score(y_true, y_pred, average='weighted')
    recall = recall_score(y_true, y_pred, average='weighted')
    f1 = f1_score(y_true, y_pred, average='weighted')

    return cm, accuracy, precision, recall, f1

def create_comparison_plot(results_dict, save_path='confusion_matrices_comparison.png'):
    """Create side-by-side comparison of confusion matrices"""

    fig, axes = plt.subplots(1, 2, figsize=(16, 6))

    models = list(results_dict.keys())

    for idx, (model_name, data) in enumerate(results_dict.items()):
        y_true, y_pred = data['y_true'], data['y_pred']
        cm = confusion_matrix(y_true, y_pred)
        cm_percent = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis] * 100

        # Plot heatmap without default annotations
        sns.heatmap(cm,
                    annot=False,  # We'll add custom annotations
                    fmt='d',
                    cmap='Blues',
                    square=True,
                    linewidths=2,
                    linecolor='white',
                    cbar=True,
                    ax=axes[idx])

        # Add custom annotations with both count and percentage
        for i in range(2):
            for j in range(2):
                # Determine text color based on cell value (darker cells need white text)
                # True positives (1,1) and True negatives (0,0) are usually darker
                if (i == 0 and j == 0) or (i == 1 and j == 1):  # Diagonal elements (correct predictions)
                    main_color = 'white'
                    percent_color = 'lightgray'
                else:  # Off-diagonal elements (incorrect predictions)
                    main_color = 'black'
                    percent_color = 'gray'

                # Main count number (larger, bold)
                axes[idx].text(j + 0.5, i + 0.35, str(cm[i, j]),
                              ha='center', va='center',
                              fontsize=18, fontweight='bold', color=main_color)
                # Percentage (smaller, below the count)
                axes[idx].text(j + 0.5, i + 0.65, f'({cm_percent[i, j]:.1f}%)',
                              ha='center', va='center',
                              fontsize=10, color=percent_color)

        # Customize subplot
        axes[idx].set_title(f'{model_name}\nF1-Score: {data["f1"]:.3f}',
                           fontsize=14, fontweight='bold')
        axes[idx].set_xlabel('Predicted Label', fontsize=12, fontweight='bold')
        if idx == 0:
            axes[idx].set_ylabel('True Label', fontsize=12, fontweight='bold')

        # Set tick labels
        class_names = ['Non-functional', 'Functional']
        axes[idx].set_xticklabels(class_names, fontsize=11)
        axes[idx].set_yticklabels(class_names, fontsize=11, rotation=0)

    plt.suptitle('lncRNA Function Prediction - Model Comparison',
                 fontsize=16, fontweight='bold', y=1.02)
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"✅ Comparison plot saved: {save_path}")

def main():
    """Main function to generate confusion matrices"""
    
    print("🧬 Generating Confusion Matrices for lncRNA Function Prediction")
    print("=" * 70)
    
    # Model configurations
    models = {
        'RNA-FM': {
            'type': 'rna-fm',
            'path': './outputs/ft/lncrna-function/lncRNA_function/rna-fm/seed_666/checkpoint-500'
        },
        'SpliceBERT-MS1024': {
            'type': 'splicebert-ms1024',
            'path': './outputs/ft/lncrna-function/lncRNA_function/splicebert-ms1024/seed_666/checkpoint-1500'
        }
    }
    
    test_data_path = 'data/test.fa'
    results = {}
    
    # Generate predictions and confusion matrices for each model
    for model_name, model_info in models.items():
        try:
            print(f"\n📊 Processing {model_name}...")
            
            # Get predictions
            y_true, y_pred, y_prob = load_model_and_predict(
                model_info['type'],
                model_info['path'],
                test_data_path
            )
            
            # Plot individual confusion matrix
            save_path = f'confusion_matrix_{model_info["type"].replace("-", "_")}.png'
            cm, accuracy, precision, recall, f1 = plot_confusion_matrix(
                y_true, y_pred, model_name, save_path
            )
            
            # Store results
            results[model_name] = {
                'y_true': y_true,
                'y_pred': y_pred,
                'y_prob': y_prob,
                'confusion_matrix': cm,
                'accuracy': accuracy,
                'precision': precision,
                'recall': recall,
                'f1': f1
            }
            
            print(f"✅ {model_name} completed!")
            print(f"   Accuracy: {accuracy:.3f}, F1-Score: {f1:.3f}")
            
        except Exception as e:
            print(f"❌ Error processing {model_name}: {e}")
            continue
    
    # Create comparison plot
    if len(results) == 2:
        print(f"\n📈 Creating comparison plot...")
        create_comparison_plot(results)
        
        # Print summary
        print(f"\n{'='*70}")
        print("📋 CONFUSION MATRIX SUMMARY")
        print(f"{'='*70}")
        
        for model_name, data in results.items():
            cm = data['confusion_matrix']
            print(f"\n🔹 {model_name}:")
            print(f"   True Negatives (TN):  {cm[0,0]:3d}   False Positives (FP): {cm[0,1]:3d}")
            print(f"   False Negatives (FN): {cm[1,0]:3d}   True Positives (TP):  {cm[1,1]:3d}")
            print(f"   Accuracy: {data['accuracy']:.3f}   F1-Score: {data['f1']:.3f}")
        
        # Calculate differences
        rna_fm_data = results['RNA-FM']
        splicebert_data = results['SpliceBERT-MS1024']
        
        print(f"\n🏆 PERFORMANCE COMPARISON:")
        print(f"   Accuracy difference: {splicebert_data['accuracy'] - rna_fm_data['accuracy']:+.3f}")
        print(f"   F1-Score difference: {splicebert_data['f1'] - rna_fm_data['f1']:+.3f}")
        
        winner = 'SpliceBERT-MS1024' if splicebert_data['f1'] > rna_fm_data['f1'] else 'RNA-FM'
        print(f"   🥇 Winner: {winner}")
        
        print(f"\n📁 Generated Files:")
        print(f"   • confusion_matrix_rna_fm.png")
        print(f"   • confusion_matrix_splicebert_ms1024.png") 
        print(f"   • confusion_matrices_comparison.png")
        print(f"{'='*70}")

if __name__ == "__main__":
    main()
