#!/bin/bash

# Complete pipeline for 565_hum_label.fa lncRNA function prediction
# Full fine-tuning comparison between RNA-FM and SpliceBERT

set -e

echo "🧬 lncRNA Function Prediction Pipeline - 565_hum_label.fa"
echo "=========================================================="
echo "Start time: $(date)"
echo ""

# Configuration
DATA_FILE="data/565_hum_label.fa"
CV_SPLITS_DIR="data/cv_splits_565"
OUTPUT_DIR="outputs/full_finetune_565"
COMPARISON_DIR="outputs/comparison_565"

# Model paths (update these according to your setup)
RNA_FM_PATH="./checkpoint/opensource/rna-fm"
SPLICEBERT_PATH="./checkpoint/opensource/splicebert-ms1024"

# Training parameters
BATCH_SIZE=1
LEARNING_RATE=3e-5
NUM_EPOCHS=20
PATIENCE=10
WINDOW_SIZE=1024
WINDOW_STRIDE=512
MAX_SEQ_LENGTH=9216

echo "📋 Configuration:"
echo "  Data file: $DATA_FILE"
echo "  CV splits: $CV_SPLITS_DIR"
echo "  Output directory: $OUTPUT_DIR"
echo "  Window size: $WINDOW_SIZE"
echo "  Window stride: $WINDOW_STRIDE"
echo "  Max sequence length: $MAX_SEQ_LENGTH"
echo ""

# Step 1: Check data file
echo "📁 Step 1: Checking data file"
echo "-----------------------------"
if [ ! -f "$DATA_FILE" ]; then
    echo "❌ Error: Data file not found: $DATA_FILE"
    exit 1
fi

echo "✅ Data file found: $DATA_FILE"
python -c "
import os
from collections import Counter

# Load and analyze data
sequences = []
labels = []
with open('$DATA_FILE', 'r') as f:
    lines = f.readlines()

i = 0
while i < len(lines):
    line = lines[i].strip()
    if line.startswith('label='):
        label = int(line.split('=')[1])
        labels.append(label)
        if i + 1 < len(lines):
            sequence = lines[i + 1].strip()
            sequences.append(sequence)
        i += 2
    else:
        i += 1

print(f'   Total sequences: {len(sequences)}')
print(f'   Label distribution: {Counter(labels)}')
seq_lengths = [len(seq) for seq in sequences]
print(f'   Length range: {min(seq_lengths)}-{max(seq_lengths)} bp')
print(f'   Mean length: {sum(seq_lengths)/len(seq_lengths):.1f} bp')
print(f'   Sequences > 1024: {sum(1 for l in seq_lengths if l > 1024)}')
print(f'   Sequences > 9216: {sum(1 for l in seq_lengths if l > 9216)}')
"
echo ""

# Step 2: Create CV splits
echo "🔄 Step 2: Creating cross-validation splits"
echo "-------------------------------------------"
if [ ! -d "$CV_SPLITS_DIR" ]; then
    echo "Creating CV splits..."
    python data_split_565.py --output_dir "$CV_SPLITS_DIR"
    
    if [ $? -ne 0 ]; then
        echo "❌ Error: Failed to create CV splits"
        exit 1
    fi
else
    echo "✅ CV splits already exist: $CV_SPLITS_DIR"
fi

# Check CV splits
echo "Checking CV split files..."
for fold in {0..4}; do
    train_file="$CV_SPLITS_DIR/fold_${fold}_train.fa"
    val_file="$CV_SPLITS_DIR/fold_${fold}_val.fa"
    
    if [ -f "$train_file" ] && [ -f "$val_file" ]; then
        echo "  ✅ Fold $fold: train and validation files exist"
    else
        echo "  ❌ Fold $fold: missing files"
        exit 1
    fi
done

test_file="$CV_SPLITS_DIR/final_test.fa"
if [ -f "$test_file" ]; then
    echo "  ✅ Final test file exists"
else
    echo "  ❌ Final test file missing"
    exit 1
fi
echo ""

# Step 3: Create configuration file
echo "⚙️  Step 3: Creating configuration"
echo "---------------------------------"
CONFIG_FILE="cv_config_565.json"
cat > "$CONFIG_FILE" << EOF
{
    "window_size": $WINDOW_SIZE,
    "window_stride": $WINDOW_STRIDE,
    "max_sequence_length": $MAX_SEQ_LENGTH,
    "pooling_strategy": "mean",
    "batch_size": $BATCH_SIZE,
    "gradient_accumulation_steps": 4,
    "learning_rate": $LEARNING_RATE,
    "num_epochs": $NUM_EPOCHS,
    "patience": $PATIENCE,
    "warmup_steps": 100,
    "logging_steps": 50,
    "eval_steps": 100,
    "save_steps": 500,
    "seed": 42
}
EOF

echo "✅ Configuration saved: $CONFIG_FILE"
echo ""

# Step 4: Training function
train_model() {
    local model_type=$1
    local model_path=$2
    
    echo "🚀 Training $model_type"
    echo "Model path: $model_path"
    
    if [ ! -d "$model_path" ]; then
        echo "⚠️  Model path not found: $model_path"
        echo "   Skipping $model_type training"
        return 1
    fi
    
    for fold in {0..4}; do
        echo "  Training fold $fold..."

        output_dir="$OUTPUT_DIR/${model_type}/fold_${fold}"
        mkdir -p "$output_dir"

        # Set CUDA memory allocation strategy to reduce fragmentation
        export PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True

        python train_full_finetune_565.py \
            --model_name_or_path "$model_path" \
            --data_path "$CV_SPLITS_DIR" \
            --data_train_path "fold_${fold}_train.fa" \
            --data_val_path "fold_${fold}_val.fa" \
            --output_dir "$output_dir" \
            --model_type "$model_type" \
            --model_max_length $WINDOW_SIZE \
            --window_size $WINDOW_SIZE \
            --window_stride $WINDOW_STRIDE \
            --max_sequence_length $MAX_SEQ_LENGTH \
            --pooling_strategy "mean" \
            --per_device_train_batch_size $BATCH_SIZE \
            --per_device_eval_batch_size $BATCH_SIZE \
            --gradient_accumulation_steps 4 \
            --learning_rate $LEARNING_RATE \
            --num_train_epochs $NUM_EPOCHS \
            --patience $PATIENCE \
            --warmup_steps 50 \
            --lr_scheduler_type linear \
            --logging_steps 20 \
            --eval_steps 200 \
            --save_steps 400 \
            --evaluation_strategy steps \
            --save_strategy steps \
            --load_best_model_at_end True \
            --metric_for_best_model eval_f1 \
            --greater_is_better True \
            --max_grad_norm 1.0 \
            --gradient_checkpointing true \
            --dataloader_pin_memory false \
            --dataloader_num_workers 0 \
            --seed 42 \
            --overwrite_output_dir
        
        if [ $? -ne 0 ]; then
            echo "❌ Error: Training failed for $model_type fold $fold"
            return 1
        fi
        
        echo "  ✅ Completed fold $fold"
    done
    
    echo "✅ Completed training for $model_type"
    return 0
}

# Step 4: Train models
echo "🤖 Step 4: Training models"
echo "--------------------------"

# Train RNA-FM
echo "Training RNA-FM..."
train_model "rna-fm" "$RNA_FM_PATH"
RNA_FM_SUCCESS=$?

# Train SpliceBERT
echo "Training SpliceBERT..."
train_model "splicebert-ms1024" "$SPLICEBERT_PATH"
SPLICEBERT_SUCCESS=$?

echo ""

# Step 5: Collect results and compare
echo "📊 Step 5: Collecting results and comparison"
echo "--------------------------------------------"

if [ $RNA_FM_SUCCESS -eq 0 ] || [ $SPLICEBERT_SUCCESS -eq 0 ]; then
    echo "Running cross-validation analysis..."
    python run_cv_full_finetune_565.py \
        --data_dir "$CV_SPLITS_DIR" \
        --output_dir "$OUTPUT_DIR" \
        --config_file "$CONFIG_FILE" \
        --models rna-fm splicebert-ms1024 \
        --num_folds 5
    
    if [ $? -eq 0 ]; then
        echo "✅ CV analysis completed"
        
        # Create comparison plots
        echo "Creating model comparison..."
        mkdir -p "$COMPARISON_DIR"
        python compare_models_565.py \
            --results_dir "$OUTPUT_DIR" \
            --output_dir "$COMPARISON_DIR"
        
        if [ $? -eq 0 ]; then
            echo "✅ Model comparison completed"
        else
            echo "⚠️  Model comparison failed"
        fi
    else
        echo "⚠️  CV analysis failed"
    fi
else
    echo "⚠️  No models were successfully trained"
fi

# Step 6: Summary
echo ""
echo "🏆 EXPERIMENT SUMMARY"
echo "===================="
echo "End time: $(date)"
echo ""

if [ $RNA_FM_SUCCESS -eq 0 ]; then
    echo "✅ RNA-FM training: SUCCESS"
else
    echo "❌ RNA-FM training: FAILED"
fi

if [ $SPLICEBERT_SUCCESS -eq 0 ]; then
    echo "✅ SpliceBERT training: SUCCESS"
else
    echo "❌ SpliceBERT training: FAILED"
fi

echo ""
echo "📁 Output directories:"
echo "  Training results: $OUTPUT_DIR"
echo "  Comparison plots: $COMPARISON_DIR"
echo "  CV splits: $CV_SPLITS_DIR"
echo ""

if [ -f "$OUTPUT_DIR/cv_results.json" ]; then
    echo "📊 Results summary available in: $OUTPUT_DIR/cv_results.json"
fi

if [ -f "$COMPARISON_DIR/comparison_report.md" ]; then
    echo "📄 Comparison report available in: $COMPARISON_DIR/comparison_report.md"
fi

echo ""
echo "🎉 Experiment completed!"
