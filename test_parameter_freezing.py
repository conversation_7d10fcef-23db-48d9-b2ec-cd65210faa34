#!/usr/bin/env python3
"""
Test script to verify parameter freezing works correctly
"""

import os
import sys
import torch

# Add paths
current_path = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_path)

# Import tokenizers
from tokenizer.tokenization_opensource import OpenRnaLMTokenizer

# Import models
try:
    from model.rnafm.modeling_rnafm import RnaFmForSequenceClassification
    RNAFM_AVAILABLE = True
except ImportError:
    RNAFM_AVAILABLE = False

try:
    from model.splicebert.modeling_splicebert import SpliceBertForSequenceClassification
    SPLICEBERT_AVAILABLE = True
except ImportError:
    SPLICEBERT_AVAILABLE = False

class SimpleArgs:
    def __init__(self, **kwargs):
        for k, v in kwargs.items():
            setattr(self, k, v)

def freeze_model_parameters(model, training_args):
    """Freeze pre-trained model parameters according to training arguments."""
    
    print(f"\n🧊 Freezing model parameters...")
    print(f"   Freeze pretrained: {training_args.freeze_pretrained}")
    print(f"   Freeze embeddings: {training_args.freeze_embeddings}")
    print(f"   Freeze encoder: {training_args.freeze_encoder}")
    print(f"   Trainable layers: {training_args.trainable_layers}")
    
    total_params = sum(p.numel() for p in model.parameters())
    
    if training_args.freeze_pretrained:
        # For RNA-FM: model.rnafm contains the main model
        # For SpliceBERT: model.splicebert contains the main model
        main_model = None
        if hasattr(model, 'rnafm'):
            main_model = model.rnafm
            print(f"   Found RNA-FM main model")
        elif hasattr(model, 'splicebert'):
            main_model = model.splicebert
            print(f"   Found SpliceBERT main model")
        else:
            print(f"   Warning: Could not find main model (rnafm/splicebert)")
            return model
        
        # Freeze embeddings
        if training_args.freeze_embeddings and hasattr(main_model, 'embeddings'):
            for param in main_model.embeddings.parameters():
                param.requires_grad = False
            print(f"   ✅ Frozen embeddings")
        
        # Freeze encoder layers
        if training_args.freeze_encoder and hasattr(main_model, 'encoder'):
            encoder_layers = main_model.encoder.layer if hasattr(main_model.encoder, 'layer') else []
            
            # Determine which layers to freeze
            num_layers = len(encoder_layers)
            layers_to_freeze = num_layers - training_args.trainable_layers
            
            for i, layer in enumerate(encoder_layers):
                if i < layers_to_freeze:
                    for param in layer.parameters():
                        param.requires_grad = False
            
            print(f"   ✅ Frozen {layers_to_freeze}/{num_layers} encoder layers")
        
        # Freeze pooler if exists
        if hasattr(main_model, 'pooler') and main_model.pooler is not None:
            for param in main_model.pooler.parameters():
                param.requires_grad = False
            print(f"   ✅ Frozen pooler")
        
        # Keep classification head trainable
        if hasattr(model, 'classifier'):
            for param in model.classifier.parameters():
                param.requires_grad = True
            print(f"   ✅ Classification head kept trainable")
    
    # Count final trainable parameters
    final_trainable = sum(p.numel() for p in model.parameters() if p.requires_grad)
    final_frozen = total_params - final_trainable
    
    print(f"✅ Parameter freezing completed:")
    print(f"   Total parameters: {total_params:,}")
    print(f"   Frozen parameters: {final_frozen:,} ({final_frozen/total_params:.1%})")
    print(f"   Trainable parameters: {final_trainable:,} ({final_trainable/total_params:.1%})")
    
    return model

def test_rnafm_freezing():
    """Test RNA-FM parameter freezing."""
    
    if not RNAFM_AVAILABLE:
        print("❌ RNA-FM not available")
        return False
    
    print("🧪 Testing RNA-FM parameter freezing...")
    
    try:
        # Load model
        model_path = "checkpoint/opensource/rna-fm"
        model = RnaFmForSequenceClassification.from_pretrained(
            model_path,
            num_labels=2,
            attn_implementation="eager"
        )
        
        print(f"✅ RNA-FM model loaded")
        
        # Count parameters before freezing
        total_before = sum(p.numel() for p in model.parameters())
        trainable_before = sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        print(f"📊 Before freezing:")
        print(f"   Total: {total_before:,}")
        print(f"   Trainable: {trainable_before:,}")
        
        # Create training args
        training_args = SimpleArgs(
            freeze_pretrained=True,
            freeze_embeddings=True,
            freeze_encoder=True,
            trainable_layers=0
        )
        
        # Freeze parameters
        model = freeze_model_parameters(model, training_args)
        
        # Count parameters after freezing
        total_after = sum(p.numel() for p in model.parameters())
        trainable_after = sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        print(f"📊 After freezing:")
        print(f"   Total: {total_after:,}")
        print(f"   Trainable: {trainable_after:,}")
        print(f"   Reduction: {(trainable_before - trainable_after) / trainable_before:.1%}")
        
        # Check if freezing worked
        if trainable_after < trainable_before * 0.1:  # Should be much less than 10%
            print("✅ RNA-FM parameter freezing successful!")
            return True
        else:
            print("❌ RNA-FM parameter freezing failed!")
            return False
            
    except Exception as e:
        print(f"❌ Error testing RNA-FM: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_splicebert_freezing():
    """Test SpliceBERT parameter freezing."""
    
    if not SPLICEBERT_AVAILABLE:
        print("❌ SpliceBERT not available")
        return False
    
    print("\n🧪 Testing SpliceBERT parameter freezing...")
    
    try:
        # Load model
        model_path = "checkpoint/opensource/splicebert-ms1024"
        model = SpliceBertForSequenceClassification.from_pretrained(
            model_path,
            num_labels=2,
            attn_implementation="eager"
        )
        
        print(f"✅ SpliceBERT model loaded")
        
        # Count parameters before freezing
        total_before = sum(p.numel() for p in model.parameters())
        trainable_before = sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        print(f"📊 Before freezing:")
        print(f"   Total: {total_before:,}")
        print(f"   Trainable: {trainable_before:,}")
        
        # Create training args
        training_args = SimpleArgs(
            freeze_pretrained=True,
            freeze_embeddings=True,
            freeze_encoder=True,
            trainable_layers=0
        )
        
        # Freeze parameters
        model = freeze_model_parameters(model, training_args)
        
        # Count parameters after freezing
        total_after = sum(p.numel() for p in model.parameters())
        trainable_after = sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        print(f"📊 After freezing:")
        print(f"   Total: {total_after:,}")
        print(f"   Trainable: {trainable_after:,}")
        print(f"   Reduction: {(trainable_before - trainable_after) / trainable_before:.1%}")
        
        # Check if freezing worked
        if trainable_after < trainable_before * 0.1:  # Should be much less than 10%
            print("✅ SpliceBERT parameter freezing successful!")
            return True
        else:
            print("❌ SpliceBERT parameter freezing failed!")
            return False
            
    except Exception as e:
        print(f"❌ Error testing SpliceBERT: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    
    print("🧪 Parameter Freezing Test")
    print("=" * 50)
    
    # Test RNA-FM
    rnafm_success = test_rnafm_freezing()
    
    # Test SpliceBERT
    splicebert_success = test_splicebert_freezing()
    
    print(f"\n🏁 Test Results:")
    print(f"   RNA-FM: {'✅ PASS' if rnafm_success else '❌ FAIL'}")
    print(f"   SpliceBERT: {'✅ PASS' if splicebert_success else '❌ FAIL'}")
    
    if rnafm_success and splicebert_success:
        print("\n🎉 All parameter freezing tests passed!")
        return True
    else:
        print("\n❌ Some parameter freezing tests failed!")
        return False

if __name__ == "__main__":
    main()
