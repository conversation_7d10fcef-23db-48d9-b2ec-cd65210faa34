{"XGBoost_Optimized": {"accuracy": {"mean": 0.6569399161620866, "std": 0.04057674898427489, "scores": [0.6491228070175439, 0.6403508771929824, 0.6578947368421053, 0.7105263157894737, 0.6371681415929203, 0.6548672566371682, 0.6106194690265486, 0.6814159292035398, 0.5929203539823009, 0.7345132743362832]}, "f1": {"mean": 0.6560757569965574, "std": 0.04115975132087511, "scores": [0.6486902927580895, 0.6401016401016403, 0.6565999845524059, 0.7094307561597281, 0.6365991274654098, 0.6543259992963656, 0.6105584758356066, 0.6812661247815353, 0.588671894678511, 0.7345132743362832]}, "precision": {"mean": 0.658458758168126, "std": 0.04009353731966961, "scores": [0.6498608103928241, 0.6407407407407408, 0.6603125000000001, 0.7137500000000001, 0.6385351917776116, 0.6563598522469842, 0.6108802396870752, 0.6815437005927482, 0.598091271906993, 0.7345132743362832]}, "recall": {"mean": 0.6569399161620866, "std": 0.04057674898427489, "scores": [0.6491228070175439, 0.6403508771929824, 0.6578947368421053, 0.7105263157894737, 0.6371681415929203, 0.6548672566371682, 0.6106194690265486, 0.6814159292035398, 0.5929203539823009, 0.7345132743362832]}, "roc_auc": {"mean": 0.7587741283032143, "std": 0.03755953206423723, "scores": [0.7300707910126193, 0.7605417051400432, 0.7823945829485996, 0.7959372114496768, 0.7293233082706767, 0.7324561403508772, 0.7265037593984962, 0.824874686716792, 0.706453634085213, 0.7991854636591479]}}, "LightGBM_Optimized": {"accuracy": {"mean": 0.631392640894271, "std": 0.04886383563565139, "scores": [0.6140350877192983, 0.6052631578947368, 0.6491228070175439, 0.6578947368421053, 0.6017699115044248, 0.6194690265486725, 0.5752212389380531, 0.6991150442477876, 0.5663716814159292, 0.7256637168141593]}, "f1": {"mean": 0.6301326109953853, "std": 0.04947813901156199, "scores": [0.6129629629629629, 0.605232781839169, 0.6486902927580895, 0.6546796116504854, 0.6004563433180733, 0.6188722556344542, 0.575154700911571, 0.698642845635496, 0.5610135810041181, 0.7256207342394339]}, "precision": {"mean": 0.6331042278697522, "std": 0.048536322611914866, "scores": [0.61531279178338, 0.605295566502463, 0.6498608103928241, 0.664002557544757, 0.603795476892822, 0.6207105313082392, 0.575448719727023, 0.6999296945619301, 0.5709946716102932, 0.7256914583737898]}, "recall": {"mean": 0.631392640894271, "std": 0.04886383563565139, "scores": [0.6140350877192983, 0.6052631578947368, 0.6491228070175439, 0.6578947368421053, 0.6017699115044248, 0.6194690265486725, 0.5752212389380531, 0.6991150442477876, 0.5663716814159292, 0.7256637168141593]}, "roc_auc": {"mean": 0.7343501297102405, "std": 0.04327063577287077, "scores": [0.6999076638965835, 0.7423822714681441, 0.76269621421976, 0.7694675284702985, 0.7073934837092732, 0.706453634085213, 0.7052005012531328, 0.8085839598997493, 0.6607142857142858, 0.780701754385965]}}, "Random_Forest_Optimized": {"accuracy": {"mean": 0.6199115044247787, "std": 0.03497194632590295, "scores": [0.6140350877192983, 0.631578947368421, 0.6140350877192983, 0.6403508771929824, 0.6017699115044248, 0.6106194690265486, 0.6106194690265486, 0.6637168141592921, 0.5398230088495575, 0.672566371681416]}, "f1": {"mean": 0.618129288409478, "std": 0.03704594959904108, "scores": [0.6129629629629629, 0.6311248073959939, 0.608125, 0.6396576979415619, 0.6015827293241683, 0.6105584758356066, 0.6105584758356066, 0.6631890627690837, 0.531018602131118, 0.6725150698986789]}, "precision": {"mean": 0.6215766859381239, "std": 0.03393398652936635, "scores": [0.61531279178338, 0.6322301268171977, 0.6213560432361611, 0.641439205955335, 0.6022448877556122, 0.6108802396870752, 0.6108802396870752, 0.664330591793235, 0.5445235868287888, 0.672569145837379]}, "recall": {"mean": 0.6199115044247787, "std": 0.03497194632590295, "scores": [0.6140350877192983, 0.631578947368421, 0.6140350877192983, 0.6403508771929824, 0.6017699115044248, 0.6106194690265486, 0.6106194690265486, 0.6637168141592921, 0.5398230088495575, 0.672566371681416]}, "roc_auc": {"mean": 0.7062425801345469, "std": 0.02903907167894391, "scores": [0.6817482302246844, 0.7109879963065558, 0.708833487226839, 0.7359187442289935, 0.6854636591478697, 0.68796992481203, 0.7030075187969924, 0.7553258145363408, 0.6544486215538847, 0.7387218045112781]}}, "Extra_Trees_Optimized": {"accuracy": {"mean": 0.5776044092532215, "std": 0.028276629141006468, "scores": [0.5877192982456141, 0.5789473684210527, 0.5701754385964912, 0.5701754385964912, 0.5663716814159292, 0.5486725663716814, 0.6017699115044248, 0.6283185840707964, 0.5221238938053098, 0.6017699115044248]}, "f1": {"mean": 0.5761701250411374, "std": 0.029173077175351603, "scores": [0.586924678128132, 0.5788177339901477, 0.5626027719050974, 0.5698775698775699, 0.5656916401415873, 0.5479647683106318, 0.6017699115044248, 0.6277352799026715, 0.5187341673269426, 0.6015827293241683]}, "precision": {"mean": 0.5786307752063606, "std": 0.027915831649796115, "scores": [0.5883995037220844, 0.5790446841294298, 0.5753968253968255, 0.5703703703703703, 0.5672365499001215, 0.5494118894307491, 0.6018641737086077, 0.6287314890245399, 0.523607378625266, 0.6022448877556122]}, "recall": {"mean": 0.5776044092532215, "std": 0.028276629141006468, "scores": [0.5877192982456141, 0.5789473684210527, 0.5701754385964912, 0.5701754385964912, 0.5663716814159292, 0.5486725663716814, 0.6017699115044248, 0.6283185840707964, 0.5221238938053098, 0.6017699115044248]}, "roc_auc": {"mean": 0.6388471177944863, "std": 0.03298016374326097, "scores": [0.6180363188673439, 0.6552785472453062, 0.65466297322253, 0.6509695290858726, 0.6105889724310777, 0.6165413533834586, 0.6478696741854637, 0.7052005012531329, 0.5770676691729323, 0.6522556390977443]}}, "SVM_RBF_Optimized": {"accuracy": {"mean": 0.6860192516689955, "std": 0.02953606992213025, "scores": [0.7105263157894737, 0.6929824561403509, 0.6666666666666666, 0.7280701754385965, 0.6283185840707964, 0.7168141592920354, 0.672566371681416, 0.7079646017699115, 0.6548672566371682, 0.6814159292035398]}, "f1": {"mean": 0.685561924893989, "std": 0.029785850550530298, "scores": [0.7105040400153905, 0.6929588303193535, 0.6640818858560794, 0.7278817278817279, 0.6282603632976247, 0.7165478403195827, 0.6724124663332051, 0.7078273348377234, 0.6537288308756635, 0.6814159292035398]}, "precision": {"mean": 0.6871948497587459, "std": 0.029134107508441705, "scores": [0.710591133004926, 0.6930418719211823, 0.6719593521752937, 0.7287037037037037, 0.6285959996671013, 0.7180831524461513, 0.6732080062663535, 0.7086895655217239, 0.6576597836774829, 0.6814159292035398]}, "recall": {"mean": 0.6860192516689955, "std": 0.02953606992213025, "scores": [0.7105263157894737, 0.6929824561403509, 0.6666666666666666, 0.7280701754385965, 0.6283185840707964, 0.7168141592920354, 0.672566371681416, 0.7079646017699115, 0.6548672566371682, 0.6814159292035398]}, "roc_auc": {"mean": 0.7778662665435517, "std": 0.03475557663713933, "scores": [0.7790089258233301, 0.801477377654663, 0.7802400738688827, 0.8236380424746076, 0.7293233082706767, 0.806077694235589, 0.7788220551378446, 0.8114035087719298, 0.7083333333333334, 0.7603383458646616]}}, "MLP_Deep_Optimized": {"accuracy": {"mean": 0.7442788386896445, "std": 0.033905905084367985, "scores": [0.7631578947368421, 0.7105263157894737, 0.7456140350877193, 0.7456140350877193, 0.7345132743362832, 0.7964601769911505, 0.6991150442477876, 0.8053097345132744, 0.7079646017699115, 0.7345132743362832]}, "f1": {"mean": 0.7426304034220867, "std": 0.03475792380641131, "scores": [0.7622615277670503, 0.7039427087432124, 0.7451237375684219, 0.7398284410167624, 0.7340966285019082, 0.7964282866937734, 0.6991150442477876, 0.8053097345132744, 0.7063096195839558, 0.7338883055847207]}, "precision": {"mean": 0.7503076597918448, "std": 0.03245610667596166, "scores": [0.7671874999999999, 0.7310810810810812, 0.7475186104218362, 0.7695945945945946, 0.7355287973306254, 0.7965212084223375, 0.6991150442477876, 0.8053097345132744, 0.7137848876786931, 0.7374351396282178]}, "recall": {"mean": 0.7442788386896445, "std": 0.033905905084367985, "scores": [0.7631578947368421, 0.7105263157894737, 0.7456140350877193, 0.7456140350877193, 0.7345132743362832, 0.7964601769911505, 0.6991150442477876, 0.8053097345132744, 0.7079646017699115, 0.7345132743362832]}, "roc_auc": {"mean": 0.8270913028184497, "std": 0.02835821781947519, "scores": [0.8316405047706987, 0.814404432132964, 0.8433364112034472, 0.8658048630347799, 0.7860275689223057, 0.8063909774436091, 0.81234335839599, 0.8812656641604011, 0.7985588972431078, 0.8311403508771931]}}}