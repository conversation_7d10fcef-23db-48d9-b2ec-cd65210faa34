# Full Parameter Fine-tuning for lncRNA Function Prediction

这个目录包含了用于lncRNA功能预测的全参数微调脚本，与LoRA微调相比，全参数微调会更新模型的所有参数。

## 📁 文件说明

- `train_lncrna_function_full.py` - 主要的全参数微调训练脚本
- `run_full_finetune.py` - 运行全参数微调的便捷脚本
- `example_full_finetune.py` - 示例脚本，展示不同的配置选项
- `README_full_finetune.md` - 本说明文件

## 🆚 全参数微调 vs LoRA微调

| 特性 | 全参数微调 | LoRA微调 |
|------|------------|----------|
| **参数更新** | 所有参数 | 只有LoRA参数 |
| **内存需求** | 高 | 低 |
| **训练时间** | 长 | 短 |
| **性能潜力** | 更高 | 较高 |
| **过拟合风险** | 高 | 低 |
| **存储需求** | 完整模型 | 只需LoRA权重 |

## 🚀 快速开始

### 基本用法

```bash
# 单个模型全参数微调
python run_full_finetune.py \
    --model_type splicebert-ms1024 \
    --data_path data \
    --output_dir outputs/ft_full \
    --num_train_epochs 3 \
    --learning_rate 2e-5 \
    --batch_size 4

# 多个模型全参数微调
python run_full_finetune.py \
    --models rna-fm splicebert-ms1024 \
    --data_path data \
    --output_dir outputs/ft_full \
    --num_train_epochs 3
```

### 内存优化配置

```bash
# 内存受限环境下的配置
python run_full_finetune.py \
    --model_type splicebert-ms1024 \
    --freeze_embeddings \
    --freeze_encoder_layers 6 \
    --gradient_checkpointing \
    --batch_size 2 \
    --gradient_accumulation_steps 8 \
    --learning_rate 1e-5
```

## ⚙️ 配置参数

### 模型配置
- `--model_type`: 模型类型 (`rna-fm`, `splicebert-ms1024`)
- `--freeze_embeddings`: 是否冻结嵌入层
- `--freeze_encoder_layers`: 冻结的编码器层数
- `--gradient_checkpointing`: 启用梯度检查点以节省内存

### 训练配置
- `--num_train_epochs`: 训练轮数 (默认: 3)
- `--learning_rate`: 学习率 (默认: 2e-5)
- `--batch_size`: 批次大小 (默认: 4)
- `--gradient_accumulation_steps`: 梯度累积步数 (默认: 4)
- `--warmup_ratio`: 预热比例 (默认: 0.1)
- `--weight_decay`: 权重衰减 (默认: 0.01)

### 滑动窗口配置
- `--window_size`: 窗口大小 (默认: 1024)
- `--window_stride`: 窗口步长 (默认: 512)
- `--max_sequence_length`: 最大序列长度 (默认: 9216)
- `--pooling_strategy`: 池化策略 (`mean`, `max`, `attention`)

## 💡 推荐配置

### 1. 保守配置 (内存友好)
```bash
python run_full_finetune.py \
    --model_type splicebert-ms1024 \
    --learning_rate 1e-5 \
    --freeze_embeddings \
    --freeze_encoder_layers 4 \
    --num_train_epochs 5 \
    --batch_size 2 \
    --gradient_accumulation_steps 8 \
    --gradient_checkpointing
```

### 2. 平衡配置 (推荐)
```bash
python run_full_finetune.py \
    --model_type splicebert-ms1024 \
    --learning_rate 2e-5 \
    --freeze_encoder_layers 2 \
    --num_train_epochs 3 \
    --batch_size 4 \
    --gradient_accumulation_steps 4 \
    --gradient_checkpointing
```

### 3. 激进配置 (高性能GPU)
```bash
python run_full_finetune.py \
    --model_type splicebert-ms1024 \
    --learning_rate 5e-5 \
    --num_train_epochs 2 \
    --batch_size 8 \
    --gradient_accumulation_steps 2 \
    --warmup_ratio 0.2 \
    --weight_decay 0.05
```

## 📊 性能优化建议

### 内存优化
1. **启用梯度检查点**: `--gradient_checkpointing`
2. **冻结部分层**: `--freeze_embeddings --freeze_encoder_layers N`
3. **减小批次大小**: `--batch_size 2`
4. **增加梯度累积**: `--gradient_accumulation_steps 8`
5. **使用混合精度**: 自动启用 `--fp16`

### 训练稳定性
1. **降低学习率**: `--learning_rate 1e-5`
2. **增加预热**: `--warmup_ratio 0.2`
3. **适当的权重衰减**: `--weight_decay 0.01`
4. **早停机制**: `--patience 8`

### 性能提升
1. **适当的学习率**: `2e-5` 到 `5e-5`
2. **足够的训练轮数**: `3-5` 轮
3. **合适的批次大小**: `4-8`
4. **有效批次大小**: `batch_size × gradient_accumulation_steps = 16-32`

## 🔍 监控和调试

### 训练监控
- 训练过程会自动记录损失和指标
- 每100步评估一次验证集
- 每500步保存一次检查点
- 使用早停机制防止过拟合

### 常见问题
1. **内存不足**: 减小批次大小，启用梯度检查点，冻结部分层
2. **训练不稳定**: 降低学习率，增加预热比例
3. **过拟合**: 增加权重衰减，减少训练轮数，使用早停
4. **欠拟合**: 增加学习率，增加训练轮数，减少正则化

## 📈 与LoRA微调的比较

### 何时使用全参数微调
- 有充足的计算资源和时间
- 追求最佳性能
- 数据集较大且多样化
- 任务与预训练任务差异较大

### 何时使用LoRA微调
- 计算资源有限
- 需要快速实验和迭代
- 数据集较小
- 任务与预训练任务相似

## 🎯 实验建议

1. **从保守配置开始**: 确保训练稳定
2. **逐步调整参数**: 一次只改变一个参数
3. **监控验证指标**: 关注F1分数和准确率
4. **比较不同配置**: 使用相同的随机种子
5. **记录实验结果**: 便于后续分析和复现

## 📝 示例运行

```bash
# 运行示例脚本
python example_full_finetune.py

# 直接使用训练脚本
python train_lncrna_function_full.py \
    --model_type splicebert-ms1024 \
    --data_path data \
    --data_train_path train.fa \
    --data_val_path val.fa \
    --output_dir outputs/ft_full/splicebert \
    --num_train_epochs 3 \
    --learning_rate 2e-5 \
    --per_device_train_batch_size 4 \
    --gradient_accumulation_steps 4 \
    --gradient_checkpointing True \
    --fp16 True
```

## 🔧 故障排除

如果遇到问题，请检查：
1. 数据文件是否存在且格式正确
2. GPU内存是否足够
3. 依赖包是否正确安装
4. 模型检查点是否可访问

更多详细信息，请参考训练脚本中的注释和错误信息。
