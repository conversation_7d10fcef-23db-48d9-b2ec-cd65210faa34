#!/usr/bin/env python3
"""
Test script for lncRNA function prediction with sliding windows
"""

import sys
import os
sys.path.append('.')

# Import only the functions we need for testing
from downstream.train_lncrna_function import create_sliding_windows
import torch

def test_sliding_windows():
    """Test sliding window creation"""
    print("Testing sliding window creation...")
    
    # Test case 1: Short sequence (should return as is)
    short_seq = "ATCGATCGATCG"
    windows = create_sliding_windows(short_seq, window_size=20, stride=10, max_length=8192)
    assert len(windows) == 1
    assert windows[0] == short_seq
    print("✓ Short sequence test passed")
    
    # Test case 2: Long sequence (should create multiple windows)
    long_seq = "A" * 2000
    windows = create_sliding_windows(long_seq, window_size=1024, stride=512, max_length=8192)
    expected_windows = (2000 - 1024) // 512 + 1
    print(f"Long sequence: {len(long_seq)} bp -> {len(windows)} windows (expected ~{expected_windows})")
    assert len(windows) > 1
    print("✓ Long sequence test passed")
    
    # Test case 3: Very long sequence (should be truncated)
    very_long_seq = "T" * 10000
    windows = create_sliding_windows(very_long_seq, window_size=1024, stride=512, max_length=8192)
    # Should be truncated to 8192, then windowed
    print(f"Very long sequence: {len(very_long_seq)} bp -> truncated -> {len(windows)} windows")
    print("✓ Truncation test passed")

def test_basic_functionality():
    """Test basic functionality without model dependencies"""
    print("\nTesting basic functionality...")

    try:
        # Test FASTA reading
        from downstream.train_lncrna_function import load_fasta_data
        sequences, labels = load_fasta_data("data/train.fa")

        print(f"✓ Successfully read FASTA data: {len(sequences)} samples")

        # Test sequence processing on first 2 samples
        for i in range(min(2, len(sequences))):
            seq = sequences[i]
            label = labels[i]
            windows = create_sliding_windows(seq, 1024, 512, 8192)
            print(f"✓ Sample {i}: {len(seq)} bp -> {len(windows)} windows, label: {label}")

        return True

    except Exception as e:
        print(f"✗ Basic functionality test failed: {e}")
        return False

def test_script_syntax():
    """Test that the training script has valid syntax"""
    print("\nTesting script syntax...")

    try:
        import py_compile
        py_compile.compile('downstream/train_lncrna_function.py', doraise=True)
        print("✓ Training script syntax is valid")
        return True
    except Exception as e:
        print(f"✗ Script syntax error: {e}")
        return False

def main():
    """Run all tests"""
    print("=" * 50)
    print("Testing lncRNA Function Prediction Implementation")
    print("=" * 50)

    # Test sliding windows
    test_sliding_windows()

    # Test basic functionality
    basic_ok = test_basic_functionality()

    # Test script syntax
    syntax_ok = test_script_syntax()

    print("\n" + "=" * 50)
    if basic_ok and syntax_ok:
        print("✓ All tests passed! Implementation looks good.")
        print("\nNext steps:")
        print("1. Ensure model checkpoints are downloaded to:")
        print("   - ./checkpoint/opensource/rna-fm/")
        print("   - ./checkpoint/opensource/splicebert-ms1024/")
        print("2. Run: bash scripts/lncrna_function/rna_fm.sh")
        print("3. Run: bash scripts/lncrna_function/splicebert_ms1024.sh")
        print("4. Or run both: bash scripts/lncrna_function/compare_models.sh")
        print("\nNote: If you encounter flash_attn import errors, you may need to:")
        print("- Install flash-attn: pip install flash-attn")
        print("- Or modify the model files to use standard attention")
    else:
        print("✗ Some tests failed. Please check the implementation.")
    print("=" * 50)

if __name__ == "__main__":
    main()
