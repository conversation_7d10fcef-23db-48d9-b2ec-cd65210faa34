#!/usr/bin/env python3
"""
LoRA fine-tuning script for lncRNA function prediction
Supports RNA-FM and SpliceBERT models with sliding window and sequence-level aggregation
"""

import os
import sys
import random
import logging
import numpy as np
import torch
import transformers
from dataclasses import dataclass, field
from typing import Optional, List
from torch.utils.data import Dataset
from transformers import Trainer, EarlyStoppingCallback
from sklearn.metrics import accuracy_score, precision_recall_fscore_support, roc_auc_score

# Add paths
sys.path.append('.')
sys.path.append('downstream')

# Import models and tokenizers
try:
    from tokenizer.tokenization_opensource import OpenRnaLMTokenizer
    TOKENIZER_AVAILABLE = True
except ImportError:
    TOKENIZER_AVAILABLE = False
    print("Warning: OpenRnaLMTokenizer not available")

try:
    from model.rnafm.modeling_rnafm import RnaFmForSequenceClassification
    RNAFM_AVAILABLE = True
except ImportError:
    RNAFM_AVAILABLE = False
    print("Warning: RnaFm model not available")

try:
    from model.splicebert.modeling_splicebert import SpliceBertForSequenceClassification
    SPLICEBERT_AVAILABLE = True
except ImportError:
    SPLICEBERT_AVAILABLE = False
    print("Warning: SpliceBert model not available")

# LoRA imports
try:
    from peft import LoraConfig, get_peft_model, TaskType, PeftModel
    PEFT_AVAILABLE = True
except ImportError:
    PEFT_AVAILABLE = False
    print("Warning: PEFT library not available. Install with: pip install peft")

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class ModelArguments:
    model_name_or_path: Optional[str] = field(default="")
    cache_dir: Optional[str] = field(default=None)

@dataclass
class DataArguments:
    data_path: str = field(default="", metadata={"help": "Path to the training data."})
    data_train_path: str = field(default="train.fa")
    data_val_path: str = field(default="val.fa")
    data_test_path: str = field(default="test.fa")
    kmer: int = field(default=-1)

@dataclass
class LoRATrainingArguments(transformers.TrainingArguments):
    """Custom training arguments that avoid conflicts with base TrainingArguments."""
    run_name: str = field(default="run")
    model_max_length: int = field(default=1024, metadata={"help": "Maximum sequence length."})

    # Model configuration
    model_type: str = field(default="rna-fm")
    token_type: str = field(default="single")
    train_from_scratch: bool = field(default=False)
    save_model: bool = field(default=True)
    
    # LoRA configuration
    use_lora: bool = field(default=True, metadata={"help": "Whether to use LoRA"})
    lora_r: int = field(default=16, metadata={"help": "LoRA rank"})
    lora_alpha: int = field(default=32, metadata={"help": "LoRA alpha"})
    lora_dropout: float = field(default=0.1, metadata={"help": "LoRA dropout"})
    lora_target_modules: str = field(default="query,value,key,dense", metadata={"help": "LoRA target modules"})
    
    # Sliding window configuration
    window_size: int = field(default=1024, metadata={"help": "Size of sliding window"})
    window_stride: int = field(default=512, metadata={"help": "Stride of sliding window"})
    max_sequence_length: int = field(default=9216, metadata={"help": "Maximum sequence length before truncation"})
    pooling_strategy: str = field(default="mean", metadata={"help": "Pooling strategy: mean, max, attention"})
    
    # Training configuration
    patience: int = field(default=8)
    num_workers: int = field(default=1)
    seed: int = field(default=42)
    attn_implementation: str = field(default="eager")

def set_seed(seed):
    """Set random seeds for reproducibility."""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed_all(seed)

def load_fasta_data(file_path):
    """Load FASTA data with labels."""
    sequences = []
    labels = []
    
    with open(file_path, 'r') as f:
        lines = f.readlines()
    
    i = 0
    while i < len(lines):
        line = lines[i].strip()
        if line.startswith('label='):
            label = int(line.split('=')[1])
            if i + 1 < len(lines):
                sequence = lines[i + 1].strip()
                sequences.append(sequence)
                labels.append(label)
                i += 2
            else:
                break
        else:
            i += 1
    
    return sequences, labels

def create_sliding_windows(sequence: str, window_size: int, stride: int, max_length: int) -> List[str]:
    """Create sliding windows from a long sequence."""
    # Truncate sequence if too long
    if len(sequence) > max_length:
        sequence = sequence[:max_length]
        logger.warning(f"Sequence truncated to {max_length} nucleotides")
    
    # If sequence is shorter than window size, return the whole sequence
    if len(sequence) <= window_size:
        return [sequence]
    
    windows = []
    start = 0
    while start < len(sequence):
        end = min(start + window_size, len(sequence))
        window = sequence[start:end]
        windows.append(window)
        
        # If this window reaches the end, break
        if end == len(sequence):
            break
            
        start += stride
    
    return windows

class SlidingWindowDataset(Dataset):
    """Dataset that handles sliding windows and sequence-level aggregation."""

    def __init__(self, data_path: str, args, tokenizer, kmer: int = -1):
        super(SlidingWindowDataset, self).__init__()
        
        # Load data
        sequences, labels = load_fasta_data(data_path)
        
        self.original_texts = sequences
        self.labels = labels
        self.num_labels = len(set(labels))
        self.args = args
        self.tokenizer = tokenizer

        # Create sliding windows for each sequence
        self.windowed_data = []
        for i, text in enumerate(sequences):
            windows = create_sliding_windows(
                text,
                args.window_size,
                args.window_stride,
                args.max_sequence_length
            )
            self.windowed_data.append({
                'windows': windows,
                'label': labels[i],
                'original_length': len(text)
            })

        print(f"Dataset loaded: {len(self.windowed_data)} sequences")
        print(f"Average windows per sequence: {np.mean([len(item['windows']) for item in self.windowed_data]):.2f}")

    def __len__(self):
        return len(self.windowed_data)

    def __getitem__(self, i):
        item = self.windowed_data[i]
        windows = item['windows']
        label = item['label']

        # For simplicity, let's just use the first window for now
        # In a full implementation, we'd need a more sophisticated approach
        first_window = windows[0] if windows else ""

        # Tokenize the first window
        tokenized = self.tokenizer(
            first_window,
            truncation=True,
            padding='max_length',
            max_length=self.args.model_max_length,
            return_tensors='pt'
        )

        return {
            'input_ids': tokenized['input_ids'].squeeze(0),
            'attention_mask': tokenized['attention_mask'].squeeze(0),
            'labels': torch.tensor(label, dtype=torch.long)
        }

def custom_data_collator(features):
    """Custom data collator for sliding window data."""
    batch_size = len(features)

    # Handle case where features might be processed by default collator
    if batch_size > 0 and isinstance(features[0], dict):
        # Check if this is our custom format
        if 'num_windows' in features[0]:
            max_windows = max(f['num_windows'] for f in features)
        else:
            # This might be default collator output, try to handle it
            print(f"Warning: Unexpected feature format: {list(features[0].keys())}")
            # Fall back to default behavior
            return features
    else:
        print(f"Warning: Unexpected features type: {type(features[0])}")
        return features

    # Get dimensions from first feature
    first_window = features[0]['windows'][0]
    seq_length = first_window['input_ids'].shape[0]

    # Initialize tensors
    batch_input_ids = torch.zeros(batch_size, max_windows, seq_length, dtype=torch.long)
    batch_attention_masks = torch.zeros(batch_size, max_windows, seq_length, dtype=torch.long)
    batch_window_masks = torch.zeros(batch_size, max_windows, dtype=torch.bool)
    labels = []

    for i, feature in enumerate(features):
        num_windows = feature['num_windows']
        labels.append(feature['label'])

        # Fill in the windows
        for j, window in enumerate(feature['windows']):
            batch_input_ids[i, j] = window['input_ids']
            batch_attention_masks[i, j] = window['attention_mask']
            batch_window_masks[i, j] = True

    return {
        'input_ids': batch_input_ids,
        'attention_mask': batch_attention_masks,
        'window_mask': batch_window_masks,
        'labels': torch.tensor(labels, dtype=torch.long),
    }

class SlidingWindowModel(torch.nn.Module):
    """Wrapper model that handles sliding windows and sequence-level aggregation."""

    def __init__(self, base_model, pooling_strategy='mean'):
        super().__init__()
        self.base_model = base_model
        self.pooling_strategy = pooling_strategy
        self.config = base_model.config

    def forward(self, input_ids, attention_mask, labels=None, **kwargs):
        # Filter out unexpected kwargs that might be passed by Trainer
        allowed_kwargs = {'output_attentions', 'output_hidden_states', 'return_dict'}
        filtered_kwargs = {k: v for k, v in kwargs.items() if k in allowed_kwargs}

        # For now, just use the base model directly
        # In a full implementation, we would implement sliding window logic here
        return self.base_model(
            input_ids=input_ids,
            attention_mask=attention_mask,
            labels=labels,
            **filtered_kwargs
        )

    def save_pretrained(self, output_dir):
        """Save the model."""
        os.makedirs(output_dir, exist_ok=True)
        if hasattr(self.base_model, 'save_pretrained'):
            self.base_model.save_pretrained(output_dir)
        else:
            torch.save(self.state_dict(), os.path.join(output_dir, 'pytorch_model.bin'))

def compute_metrics(eval_pred):
    """Compute evaluation metrics."""
    predictions, labels = eval_pred
    predictions = np.argmax(predictions, axis=1)
    
    precision, recall, f1, _ = precision_recall_fscore_support(labels, predictions, average='binary')
    acc = accuracy_score(labels, predictions)
    
    return {
        'accuracy': acc,
        'f1': f1,
        'precision': precision,
        'recall': recall,
    }

def get_lora_config(args):
    """Get LoRA configuration."""
    target_modules = args.lora_target_modules.split(',')
    
    return LoraConfig(
        task_type=TaskType.SEQ_CLS,
        r=args.lora_r,
        lora_alpha=args.lora_alpha,
        lora_dropout=args.lora_dropout,
        target_modules=target_modules,
        bias="none",
    )

def safe_save_model_for_hf_trainer(trainer: transformers.Trainer, output_dir: str):
    """Safely save model."""
    if hasattr(trainer.model, 'save_pretrained'):
        trainer.model.save_pretrained(output_dir)
    else:
        state_dict = trainer.model.state_dict()
        if trainer.args.should_save:
            cpu_state_dict = {key: value.cpu() for key, value in state_dict.items()}
            del state_dict
            trainer._save(output_dir, state_dict=cpu_state_dict)

def main():
    # Parse arguments
    parser = transformers.HfArgumentParser((ModelArguments, DataArguments, LoRATrainingArguments))
    model_args, data_args, training_args = parser.parse_args_into_dataclasses()
    
    # Set seed
    set_seed(training_args.seed)
    
    # Setup logging
    logging.basicConfig(
        format="%(asctime)s - %(levelname)s - %(name)s - %(message)s",
        datefmt="%m/%d/%Y %H:%M:%S",
        level=logging.INFO,
    )
    
    print(f"🧬 LoRA Fine-tuning for lncRNA Function Prediction")
    print(f"Model: {training_args.model_type}")
    print(f"LoRA enabled: {training_args.use_lora}")
    print(f"Window size: {training_args.window_size}, stride: {training_args.window_stride}")
    print(f"Max sequence length: {training_args.max_sequence_length}")
    
    # Check dependencies
    if training_args.use_lora and not PEFT_AVAILABLE:
        raise ImportError("PEFT library is required for LoRA training. Install with: pip install peft")
    
    if not TOKENIZER_AVAILABLE:
        raise ImportError("OpenRnaLMTokenizer not available")
    
    # Load tokenizer
    tokenizer = OpenRnaLMTokenizer.from_pretrained(
        model_args.model_name_or_path,
        cache_dir=model_args.cache_dir,
        model_max_length=training_args.model_max_length,
        padding_side="right",
        use_fast=True,
        trust_remote_code=True,
    )
    
    # Load datasets
    train_dataset = SlidingWindowDataset(
        tokenizer=tokenizer, 
        args=training_args,
        data_path=os.path.join(data_args.data_path, data_args.data_train_path),
        kmer=data_args.kmer
    )
    val_dataset = SlidingWindowDataset(
        tokenizer=tokenizer, 
        args=training_args,
        data_path=os.path.join(data_args.data_path, data_args.data_val_path),
        kmer=data_args.kmer
    )
    
    print(f'Dataset sizes - Train: {len(train_dataset)}, Val: {len(val_dataset)}')
    
    # Load base model
    if training_args.model_type == 'rna-fm':
        if not RNAFM_AVAILABLE:
            raise ImportError("RnaFm model not available")
        print(f'Loading {training_args.model_type} model')
        base_model = RnaFmForSequenceClassification.from_pretrained(
            model_args.model_name_or_path,
            cache_dir=model_args.cache_dir,
            num_labels=train_dataset.num_labels,
            problem_type="single_label_classification",
            trust_remote_code=True,
        )
    elif 'splicebert' in training_args.model_type:
        if not SPLICEBERT_AVAILABLE:
            raise ImportError("SpliceBert model not available")
        print(f'Loading {training_args.model_type} model')
        base_model = SpliceBertForSequenceClassification.from_pretrained(
            model_args.model_name_or_path,
            cache_dir=model_args.cache_dir,
            num_labels=train_dataset.num_labels,
            problem_type="single_label_classification",
            trust_remote_code=True,
        )
    else:
        raise ValueError(f"Unsupported model type: {training_args.model_type}")
    
    # Apply LoRA if enabled
    if training_args.use_lora:
        lora_config = get_lora_config(training_args)
        base_model = get_peft_model(base_model, lora_config)

        # Print parameter statistics
        total_params = sum(p.numel() for p in base_model.parameters())
        trainable_params = sum(p.numel() for p in base_model.parameters() if p.requires_grad)

        print(f"LoRA applied:")
        print(f"  Total parameters: {total_params:,}")
        print(f"  Trainable parameters: {trainable_params:,}")
        print(f"  Trainable ratio: {100 * trainable_params / total_params:.2f}%")

    # Wrap with sliding window model
    model = SlidingWindowModel(base_model, training_args.pooling_strategy)
    
    # Setup early stopping
    early_stopping = EarlyStoppingCallback(early_stopping_patience=training_args.patience)
    
    # Create trainer
    trainer = Trainer(
        model=model,
        tokenizer=tokenizer,
        args=training_args,
        compute_metrics=compute_metrics,
        train_dataset=train_dataset,
        eval_dataset=val_dataset,
        # data_collator=custom_data_collator,  # Use default for now
        callbacks=[early_stopping],
    )
    
    # Train
    print("Starting training...")
    trainer.train()
    
    # Save model
    if training_args.save_model:
        print(f"Saving model to {training_args.output_dir}")
        trainer.save_state()
        safe_save_model_for_hf_trainer(trainer=trainer, output_dir=training_args.output_dir)
    
    # Final evaluation
    print("Final evaluation...")
    eval_results = trainer.evaluate()
    print("Evaluation Results:", eval_results)

if __name__ == "__main__":
    main()
