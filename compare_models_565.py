#!/usr/bin/env python3
"""
Model performance comparison and visualization for 565_hum_label.fa lncRNA function prediction
Compares RNA-FM and SpliceBERT models across 5-fold cross-validation
"""

import os
import json
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import argparse
from datetime import datetime

def load_cv_results(results_dir: str):
    """Load cross-validation results."""
    results_file = os.path.join(results_dir, "cv_results.json")
    
    if not os.path.exists(results_file):
        raise FileNotFoundError(f"Results file not found: {results_file}")
    
    with open(results_file, 'r') as f:
        data = json.load(f)
    
    return data

def create_comparison_plots(results_data: dict, output_dir: str):
    """Create comparison plots for model performance."""
    detailed_results = results_data["detailed_results"]
    
    # Convert to DataFrame
    all_results = []
    for model_type, fold_results in detailed_results.items():
        for result in fold_results:
            all_results.append(result)
    
    df = pd.DataFrame(all_results)
    
    if df.empty:
        print("⚠️  No results data available for plotting")
        return
    
    # Set style
    plt.style.use('default')
    sns.set_palette("husl")
    
    # Metrics to plot
    metrics = ['accuracy', 'precision', 'recall', 'f1', 'auc']
    available_metrics = [m for m in metrics if m in df.columns]
    
    if not available_metrics:
        print("⚠️  No metrics available for plotting")
        return
    
    # 1. Box plots for each metric
    n_metrics = len(available_metrics)
    fig, axes = plt.subplots(1, n_metrics, figsize=(4*n_metrics, 6))
    if n_metrics == 1:
        axes = [axes]
    
    for i, metric in enumerate(available_metrics):
        sns.boxplot(data=df, x='model', y=metric, ax=axes[i])
        axes[i].set_title(f'{metric.capitalize()} Comparison')
        axes[i].set_ylabel(metric.capitalize())
        axes[i].tick_params(axis='x', rotation=45)
        
        # Add mean values as text
        for j, model in enumerate(df['model'].unique()):
            model_data = df[df['model'] == model][metric].dropna()
            if len(model_data) > 0:
                mean_val = model_data.mean()
                axes[i].text(j, mean_val, f'{mean_val:.3f}', 
                           ha='center', va='bottom', fontweight='bold')
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'model_comparison_boxplots.png'), 
                dpi=300, bbox_inches='tight')
    plt.close()
    
    # 2. Radar chart comparison
    if len(available_metrics) >= 3:
        create_radar_chart(df, available_metrics, output_dir)
    
    # 3. Fold-wise performance comparison
    create_fold_comparison(df, available_metrics, output_dir)
    
    # 4. Statistical significance tests
    perform_statistical_tests(df, available_metrics, output_dir)
    
    print(f"📊 Comparison plots saved to: {output_dir}")

def create_radar_chart(df: pd.DataFrame, metrics: list, output_dir: str):
    """Create radar chart for model comparison."""
    from math import pi
    
    # Calculate mean values for each model
    model_means = df.groupby('model')[metrics].mean()
    
    if len(model_means) < 2:
        return
    
    # Number of metrics
    N = len(metrics)
    
    # Angles for each metric
    angles = [n / float(N) * 2 * pi for n in range(N)]
    angles += angles[:1]  # Complete the circle
    
    # Create plot
    fig, ax = plt.subplots(figsize=(8, 8), subplot_kw=dict(projection='polar'))
    
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7']
    
    for i, (model, values) in enumerate(model_means.iterrows()):
        values_list = values.tolist()
        values_list += values_list[:1]  # Complete the circle
        
        ax.plot(angles, values_list, 'o-', linewidth=2, 
                label=model, color=colors[i % len(colors)])
        ax.fill(angles, values_list, alpha=0.25, color=colors[i % len(colors)])
    
    # Add metric labels
    ax.set_xticks(angles[:-1])
    ax.set_xticklabels([m.capitalize() for m in metrics])
    
    # Set y-axis limits
    ax.set_ylim(0, 1)
    ax.set_yticks([0.2, 0.4, 0.6, 0.8, 1.0])
    ax.set_yticklabels(['0.2', '0.4', '0.6', '0.8', '1.0'])
    
    # Add legend and title
    ax.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
    ax.set_title('Model Performance Comparison\n(Radar Chart)', 
                 size=16, fontweight='bold', pad=20)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'model_comparison_radar.png'), 
                dpi=300, bbox_inches='tight')
    plt.close()

def create_fold_comparison(df: pd.DataFrame, metrics: list, output_dir: str):
    """Create fold-wise performance comparison."""
    n_metrics = len(metrics)
    fig, axes = plt.subplots(n_metrics, 1, figsize=(10, 4*n_metrics))
    if n_metrics == 1:
        axes = [axes]
    
    for i, metric in enumerate(metrics):
        # Line plot showing performance across folds
        for model in df['model'].unique():
            model_data = df[df['model'] == model].sort_values('fold')
            if len(model_data) > 0:
                axes[i].plot(model_data['fold'], model_data[metric], 
                           'o-', label=model, linewidth=2, markersize=6)
        
        axes[i].set_xlabel('Fold')
        axes[i].set_ylabel(metric.capitalize())
        axes[i].set_title(f'{metric.capitalize()} Across Folds')
        axes[i].legend()
        axes[i].grid(True, alpha=0.3)
        axes[i].set_xticks(range(5))
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'fold_wise_comparison.png'), 
                dpi=300, bbox_inches='tight')
    plt.close()

def perform_statistical_tests(df: pd.DataFrame, metrics: list, output_dir: str):
    """Perform statistical significance tests between models."""
    models = df['model'].unique()
    
    if len(models) < 2:
        return
    
    results = []
    
    for metric in metrics:
        for i in range(len(models)):
            for j in range(i+1, len(models)):
                model1, model2 = models[i], models[j]
                
                data1 = df[df['model'] == model1][metric].dropna()
                data2 = df[df['model'] == model2][metric].dropna()
                
                if len(data1) > 1 and len(data2) > 1:
                    # Paired t-test (since we have same folds)
                    if len(data1) == len(data2):
                        t_stat, p_value = stats.ttest_rel(data1, data2)
                        test_type = "Paired t-test"
                    else:
                        t_stat, p_value = stats.ttest_ind(data1, data2)
                        test_type = "Independent t-test"
                    
                    # Wilcoxon signed-rank test (non-parametric alternative)
                    if len(data1) == len(data2):
                        w_stat, w_p_value = stats.wilcoxon(data1, data2)
                    else:
                        w_stat, w_p_value = stats.mannwhitneyu(data1, data2)
                    
                    results.append({
                        'metric': metric,
                        'model1': model1,
                        'model2': model2,
                        'model1_mean': data1.mean(),
                        'model2_mean': data2.mean(),
                        'difference': data1.mean() - data2.mean(),
                        't_statistic': t_stat,
                        't_p_value': p_value,
                        'w_statistic': w_stat,
                        'w_p_value': w_p_value,
                        'significant_t': p_value < 0.05,
                        'significant_w': w_p_value < 0.05
                    })
    
    # Save statistical test results
    if results:
        stats_df = pd.DataFrame(results)
        stats_df.to_csv(os.path.join(output_dir, 'statistical_tests.csv'), index=False)
        
        # Create summary table
        create_significance_table(stats_df, output_dir)

def create_significance_table(stats_df: pd.DataFrame, output_dir: str):
    """Create a visual significance table."""
    fig, ax = plt.subplots(figsize=(12, 8))
    
    # Prepare data for heatmap
    metrics = stats_df['metric'].unique()
    comparisons = []
    significance_matrix = []
    
    for _, row in stats_df.iterrows():
        comparison = f"{row['model1']} vs {row['model2']}"
        comparisons.append(comparison)
        
        # Use t-test p-values for significance
        sig_level = 'p < 0.001' if row['t_p_value'] < 0.001 else \
                   'p < 0.01' if row['t_p_value'] < 0.01 else \
                   'p < 0.05' if row['t_p_value'] < 0.05 else \
                   'n.s.'
        
        significance_matrix.append([
            row['metric'],
            comparison,
            f"{row['difference']:.4f}",
            f"{row['t_p_value']:.4f}",
            sig_level
        ])
    
    # Create table
    table_data = pd.DataFrame(significance_matrix, 
                             columns=['Metric', 'Comparison', 'Difference', 'P-value', 'Significance'])
    
    # Plot table
    ax.axis('tight')
    ax.axis('off')
    
    table = ax.table(cellText=table_data.values,
                    colLabels=table_data.columns,
                    cellLoc='center',
                    loc='center')
    
    table.auto_set_font_size(False)
    table.set_fontsize(10)
    table.scale(1.2, 1.5)
    
    # Color code significance levels
    for i in range(len(table_data)):
        sig_level = table_data.iloc[i]['Significance']
        if sig_level == 'p < 0.001':
            color = '#FF6B6B'  # Red
        elif sig_level == 'p < 0.01':
            color = '#FFA07A'  # Light red
        elif sig_level == 'p < 0.05':
            color = '#FFE4B5'  # Light orange
        else:
            color = '#F0F0F0'  # Light gray
        
        table[(i+1, 4)].set_facecolor(color)
    
    plt.title('Statistical Significance Tests\n(Model Comparisons)', 
              fontsize=16, fontweight='bold', pad=20)
    plt.savefig(os.path.join(output_dir, 'significance_table.png'), 
                dpi=300, bbox_inches='tight')
    plt.close()

def create_summary_report(results_data: dict, output_dir: str):
    """Create a comprehensive summary report."""
    summary_stats = results_data["summary_statistics"]
    
    report_content = f"""
# lncRNA Function Prediction - Model Comparison Report
## Dataset: 565_hum_label.fa

Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## Summary Statistics

"""
    
    for model_type, stats in summary_stats.items():
        report_content += f"### {model_type.upper()}\n\n"
        
        metrics = ['accuracy', 'precision', 'recall', 'f1', 'auc']
        for metric in metrics:
            mean_key = f'{metric}_mean'
            std_key = f'{metric}_std'
            
            if mean_key in stats and std_key in stats:
                mean_val = stats[mean_key]
                std_val = stats[std_key]
                report_content += f"- **{metric.capitalize()}**: {mean_val:.4f} ± {std_val:.4f}\n"
        
        report_content += f"- **Number of folds**: {stats.get('num_folds', 0)}\n\n"
    
    # Best performing model
    best_models = {}
    for metric in ['accuracy', 'precision', 'recall', 'f1', 'auc']:
        best_score = 0
        best_model = None
        
        for model_type, stats in summary_stats.items():
            mean_key = f'{metric}_mean'
            if mean_key in stats and stats[mean_key] > best_score:
                best_score = stats[mean_key]
                best_model = model_type
        
        if best_model:
            best_models[metric] = (best_model, best_score)
    
    report_content += "## Best Performing Models\n\n"
    for metric, (model, score) in best_models.items():
        report_content += f"- **{metric.capitalize()}**: {model} ({score:.4f})\n"
    
    report_content += f"""

## Files Generated

- `model_comparison_boxplots.png`: Box plots comparing model performance
- `model_comparison_radar.png`: Radar chart comparison
- `fold_wise_comparison.png`: Performance across folds
- `significance_table.png`: Statistical significance tests
- `statistical_tests.csv`: Detailed statistical test results
- `cv_detailed_results.csv`: Detailed cross-validation results
- `cv_summary_results.csv`: Summary statistics

## Methodology

- **Dataset**: 565_hum_label.fa (1,130 sequences, balanced labels)
- **Cross-validation**: 5-fold stratified
- **Window size**: 1024 bp
- **Window stride**: 512 bp
- **Max sequence length**: 9216 bp (truncation)
- **Aggregation**: Sequence-level mean pooling
- **Models**: RNA-FM, SpliceBERT-MS1024
"""
    
    # Save report
    with open(os.path.join(output_dir, 'comparison_report.md'), 'w') as f:
        f.write(report_content)
    
    print(f"📄 Summary report saved to: {os.path.join(output_dir, 'comparison_report.md')}")

def main():
    parser = argparse.ArgumentParser(description='Compare model performance for 565_hum_label.fa')
    parser.add_argument('--results_dir', default='outputs/cv_full_finetune_565', 
                       help='Directory containing CV results')
    parser.add_argument('--output_dir', default='outputs/comparison_565', 
                       help='Output directory for comparison plots')
    
    args = parser.parse_args()
    
    print("📊 Model Performance Comparison for 565_hum_label.fa")
    print("=" * 60)
    print(f"Results directory: {args.results_dir}")
    print(f"Output directory: {args.output_dir}")
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    try:
        # Load results
        print("Loading cross-validation results...")
        results_data = load_cv_results(args.results_dir)
        
        # Create comparison plots
        print("Creating comparison plots...")
        create_comparison_plots(results_data, args.output_dir)
        
        # Create summary report
        print("Creating summary report...")
        create_summary_report(results_data, args.output_dir)
        
        print(f"\n🎉 Model comparison completed!")
        print(f"Results saved in: {args.output_dir}")
        
    except Exception as e:
        print(f"❌ Error during comparison: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
