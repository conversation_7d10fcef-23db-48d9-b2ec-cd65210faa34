#!/usr/bin/env python3
"""
Test script to verify environment and dependencies for lncRNA LoRA fine-tuning
"""

import os
import sys
import torch
import numpy as np
from datetime import datetime

def test_basic_imports():
    """Test basic Python imports."""
    print("🐍 Testing basic Python imports...")
    
    try:
        import pandas as pd
        import sklearn
        import matplotlib.pyplot as plt
        import seaborn as sns
        print("✅ Basic imports successful")
        return True
    except ImportError as e:
        print(f"❌ Basic import failed: {e}")
        return False

def test_torch_setup():
    """Test PyTorch setup."""
    print("🔥 Testing PyTorch setup...")
    
    try:
        print(f"  PyTorch version: {torch.__version__}")
        print(f"  CUDA available: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"  CUDA device count: {torch.cuda.device_count()}")
            print(f"  Current CUDA device: {torch.cuda.current_device()}")
            print(f"  CUDA device name: {torch.cuda.get_device_name()}")
        
        # Test basic tensor operations
        x = torch.randn(2, 3)
        y = torch.randn(3, 2)
        z = torch.mm(x, y)
        print(f"  Basic tensor operations: ✅")
        
        return True
    except Exception as e:
        print(f"❌ PyTorch test failed: {e}")
        return False

def test_transformers():
    """Test Transformers library."""
    print("🤗 Testing Transformers library...")
    
    try:
        import transformers
        print(f"  Transformers version: {transformers.__version__}")
        
        # Test basic components
        from transformers import Trainer, TrainingArguments
        print("  Basic components: ✅")
        
        return True
    except ImportError as e:
        print(f"❌ Transformers import failed: {e}")
        return False

def test_peft():
    """Test PEFT library for LoRA."""
    print("🔧 Testing PEFT library...")
    
    try:
        import peft
        from peft import LoraConfig, get_peft_model, TaskType
        print(f"  PEFT version: {peft.__version__}")
        print("  LoRA components: ✅")
        return True
    except ImportError as e:
        print(f"❌ PEFT import failed: {e}")
        print("  Install with: pip install peft")
        return False

def test_custom_models():
    """Test custom model imports."""
    print("🧬 Testing custom model imports...")
    
    # Add paths
    sys.path.append('.')
    sys.path.append('downstream')
    
    success = True
    
    # Test tokenizer
    try:
        from tokenizer.tokenization_opensource import OpenRnaLMTokenizer
        print("  OpenRnaLMTokenizer: ✅")
    except ImportError as e:
        print(f"  OpenRnaLMTokenizer: ❌ ({e})")
        success = False
    
    # Test RNA-FM
    try:
        from model.rnafm.modeling_rnafm import RnaFmForSequenceClassification
        print("  RnaFmForSequenceClassification: ✅")
    except ImportError as e:
        print(f"  RnaFmForSequenceClassification: ❌ ({e})")
        success = False
    
    # Test SpliceBERT
    try:
        from model.splicebert.modeling_splicebert import SpliceBertForSequenceClassification
        print("  SpliceBertForSequenceClassification: ✅")
    except ImportError as e:
        print(f"  SpliceBertForSequenceClassification: ❌ ({e})")
        success = False
    
    return success

def test_data_files():
    """Test data file availability."""
    print("📁 Testing data files...")
    
    success = True
    
    # Check main data file
    data_file = "data/565_hum_label.fa"
    if os.path.exists(data_file):
        print(f"  {data_file}: ✅")
        
        # Check file format
        try:
            with open(data_file, 'r') as f:
                lines = f.readlines()[:10]
            
            label_lines = [line for line in lines if line.startswith('label=')]
            if label_lines:
                print(f"  Data format: ✅ (found {len(label_lines)} label lines in first 10 lines)")
            else:
                print(f"  Data format: ⚠️  (no label lines found in first 10 lines)")
        except Exception as e:
            print(f"  Data format check failed: {e}")
            success = False
    else:
        print(f"  {data_file}: ❌")
        success = False
    
    return success

def test_model_checkpoints():
    """Test model checkpoint availability."""
    print("🏗️  Testing model checkpoints...")
    
    success = True
    
    # Check RNA-FM checkpoint
    rna_fm_path = "./checkpoint/opensource/rna-fm/"
    if os.path.exists(rna_fm_path):
        print(f"  RNA-FM checkpoint: ✅")
    else:
        print(f"  RNA-FM checkpoint: ❌ (not found at {rna_fm_path})")
        success = False
    
    # Check SpliceBERT checkpoint
    splicebert_path = "./checkpoint/opensource/splicebert-ms1024/"
    if os.path.exists(splicebert_path):
        print(f"  SpliceBERT checkpoint: ✅")
    else:
        print(f"  SpliceBERT checkpoint: ❌ (not found at {splicebert_path})")
        success = False
    
    return success

def test_scripts():
    """Test required scripts."""
    print("📜 Testing required scripts...")
    
    required_scripts = [
        "data_split_cv.py",
        "train_lncrna_function_lora.py", 
        "run_cv_training.py",
        "evaluate_final_test.py",
        "cv_config.json"
    ]
    
    success = True
    for script in required_scripts:
        if os.path.exists(script):
            print(f"  {script}: ✅")
        else:
            print(f"  {script}: ❌")
            success = False
    
    return success

def test_data_loading():
    """Test data loading functionality."""
    print("📊 Testing data loading...")
    
    try:
        # Test the data loading function
        sys.path.append('.')
        from train_lncrna_function_lora import load_fasta_data
        
        data_file = "data/565_hum_label.fa"
        if os.path.exists(data_file):
            sequences, labels = load_fasta_data(data_file)
            print(f"  Loaded {len(sequences)} sequences")
            print(f"  Label distribution: {np.bincount(labels)}")
            print(f"  Average sequence length: {np.mean([len(seq) for seq in sequences]):.1f}")
            print("  Data loading: ✅")
            return True
        else:
            print(f"  Data file not found: {data_file}")
            return False
    except Exception as e:
        print(f"  Data loading failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🧪 lncRNA LoRA Fine-tuning Environment Test")
    print("=" * 50)
    print(f"Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    tests = [
        ("Basic Imports", test_basic_imports),
        ("PyTorch Setup", test_torch_setup),
        ("Transformers Library", test_transformers),
        ("PEFT Library", test_peft),
        ("Custom Models", test_custom_models),
        ("Data Files", test_data_files),
        ("Model Checkpoints", test_model_checkpoints),
        ("Required Scripts", test_scripts),
        ("Data Loading", test_data_loading),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * len(test_name))
        results[test_name] = test_func()
        print()
    
    # Summary
    print("📋 Test Summary:")
    print("=" * 20)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! Environment is ready for LoRA fine-tuning.")
        return True
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Please fix the issues before proceeding.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
