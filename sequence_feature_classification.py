#!/usr/bin/env python3
"""
Sequence-based feature extraction + advanced classifiers for 90%+ performance
Focus on comprehensive sequence features without relying on pre-trained model loading
"""

import os
import json
import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, ExtraTreesClassifier
from sklearn.svm import SVC
from sklearn.linear_model import LogisticRegression
from sklearn.neural_network import MLPClassifier
from sklearn.model_selection import StratifiedKFold, cross_val_score, GridSearchCV
from sklearn.metrics import accuracy_score, f1_score, precision_score, recall_score, roc_auc_score
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.feature_selection import SelectKBest, f_classif, RFE, SelectFromModel
import xgboost as xgb
import lightgbm as lgb
from sklearn.ensemble import VotingClassifier, StackingClassifier
from collections import Counter
import sys

# Add paths
sys.path.append('downstream')

class ComprehensiveSequenceFeatureExtractor:
    """Extract comprehensive features from RNA sequences."""
    
    def __init__(self):
        self.feature_names = []
    
    def extract_all_features(self, sequences):
        """Extract all types of sequence features."""
        print("🔄 Extracting comprehensive sequence features...")
        
        all_features = []
        
        for seq in sequences:
            features = []
            
            # Basic composition features
            basic_features = self._extract_basic_features(seq)
            features.extend(basic_features)
            
            # K-mer features
            kmer_features = self._extract_kmer_features(seq)
            features.extend(kmer_features)
            
            # Structural features
            structural_features = self._extract_structural_features(seq)
            features.extend(structural_features)
            
            # Complexity features
            complexity_features = self._extract_complexity_features(seq)
            features.extend(complexity_features)
            
            # Position-specific features
            position_features = self._extract_position_features(seq)
            features.extend(position_features)
            
            all_features.append(features)
        
        feature_matrix = np.array(all_features)
        print(f"✅ Extracted {feature_matrix.shape[1]} features from {feature_matrix.shape[0]} sequences")
        
        return feature_matrix
    
    def _extract_basic_features(self, seq):
        """Basic nucleotide composition features."""
        seq_len = len(seq)
        if seq_len == 0:
            return [0] * 10
        
        # Nucleotide frequencies
        a_freq = seq.count('A') / seq_len
        t_freq = seq.count('T') / seq_len
        g_freq = seq.count('G') / seq_len
        c_freq = seq.count('C') / seq_len
        n_freq = seq.count('N') / seq_len
        
        # Derived features
        gc_content = g_freq + c_freq
        at_content = a_freq + t_freq
        purine_content = a_freq + g_freq  # A, G
        pyrimidine_content = t_freq + c_freq  # T, C
        
        return [seq_len, a_freq, t_freq, g_freq, c_freq, n_freq, 
                gc_content, at_content, purine_content, pyrimidine_content]
    
    def _extract_kmer_features(self, seq):
        """K-mer frequency features."""
        features = []
        
        # Dinucleotide frequencies
        dinucleotides = ['AA', 'AT', 'AG', 'AC', 'TA', 'TT', 'TG', 'TC',
                        'GA', 'GT', 'GG', 'GC', 'CA', 'CT', 'CG', 'CC']
        
        for dinuc in dinucleotides:
            count = seq.count(dinuc) / max(1, len(seq) - 1)
            features.append(count)
        
        # Trinucleotide frequencies (selected important ones)
        trinucleotides = ['ATG', 'TAA', 'TAG', 'TGA', 'AAA', 'TTT', 'GGG', 'CCC',
                         'CAG', 'CTG', 'GAG', 'GAA', 'TCT', 'AGC', 'TCC', 'CCT']
        
        for trinuc in trinucleotides:
            count = seq.count(trinuc) / max(1, len(seq) - 2)
            features.append(count)
        
        return features
    
    def _extract_structural_features(self, seq):
        """Structural and secondary structure related features."""
        features = []
        
        # CpG dinucleotides (important for methylation)
        cpg_count = seq.count('CG') / max(1, len(seq) - 1)
        features.append(cpg_count)
        
        # Tandem repeats (simple approximation)
        max_tandem = self._find_max_tandem_repeat(seq)
        features.append(max_tandem / len(seq) if len(seq) > 0 else 0)
        
        # Palindromes (simple approximation)
        palindrome_count = self._count_palindromes(seq)
        features.append(palindrome_count / max(1, len(seq) - 3))
        
        # Stem-loop potential (GC content in windows)
        stem_loop_potential = self._estimate_stem_loop_potential(seq)
        features.append(stem_loop_potential)
        
        return features
    
    def _extract_complexity_features(self, seq):
        """Sequence complexity features."""
        features = []
        
        # Shannon entropy
        entropy = self._calculate_entropy(seq)
        features.append(entropy)
        
        # Linguistic complexity
        linguistic_complexity = self._calculate_linguistic_complexity(seq)
        features.append(linguistic_complexity)
        
        # Longest homopolymer run
        max_homopolymer = self._find_longest_homopolymer(seq)
        features.append(max_homopolymer / len(seq) if len(seq) > 0 else 0)
        
        # Repetitive content
        repetitive_content = self._estimate_repetitive_content(seq)
        features.append(repetitive_content)
        
        return features
    
    def _extract_position_features(self, seq):
        """Position-specific features."""
        features = []
        
        # 5' and 3' end composition (first and last 50 nucleotides)
        start_region = seq[:50] if len(seq) >= 50 else seq
        end_region = seq[-50:] if len(seq) >= 50 else seq
        
        # GC content at ends
        start_gc = (start_region.count('G') + start_region.count('C')) / len(start_region) if start_region else 0
        end_gc = (end_region.count('G') + end_region.count('C')) / len(end_region) if end_region else 0
        
        features.extend([start_gc, end_gc])
        
        # Middle region GC content
        if len(seq) >= 100:
            middle_start = len(seq) // 4
            middle_end = 3 * len(seq) // 4
            middle_region = seq[middle_start:middle_end]
            middle_gc = (middle_region.count('G') + middle_region.count('C')) / len(middle_region)
        else:
            middle_gc = start_gc
        
        features.append(middle_gc)
        
        return features
    
    def _find_max_tandem_repeat(self, seq):
        """Find maximum tandem repeat length."""
        max_repeat = 1
        for i in range(1, min(len(seq) // 2, 10)):  # Check repeats up to length 10
            pattern = seq[:i]
            count = 1
            pos = i
            while pos + i <= len(seq) and seq[pos:pos+i] == pattern:
                count += 1
                pos += i
            max_repeat = max(max_repeat, count * i)
        return max_repeat
    
    def _count_palindromes(self, seq):
        """Count palindromic sequences."""
        count = 0
        for i in range(len(seq) - 3):
            for j in range(i + 4, min(i + 20, len(seq) + 1)):  # Check palindromes 4-20 bp
                subseq = seq[i:j]
                if subseq == subseq[::-1]:
                    count += 1
        return count
    
    def _estimate_stem_loop_potential(self, seq):
        """Estimate stem-loop forming potential."""
        if len(seq) < 20:
            return 0
        
        # Simple approximation: look for regions with high GC content
        # that could form stems
        window_size = 20
        max_gc_variance = 0
        
        for i in range(len(seq) - window_size + 1):
            window = seq[i:i + window_size]
            gc_content = (window.count('G') + window.count('C')) / window_size
            max_gc_variance = max(max_gc_variance, abs(gc_content - 0.5))
        
        return max_gc_variance
    
    def _calculate_entropy(self, seq):
        """Calculate Shannon entropy."""
        if not seq:
            return 0
        
        counts = Counter(seq)
        total = len(seq)
        entropy = -sum((count / total) * np.log2(count / total) for count in counts.values())
        return entropy
    
    def _calculate_linguistic_complexity(self, seq):
        """Calculate linguistic complexity."""
        if len(seq) < 2:
            return 0
        
        # Count unique substrings of length 2
        substrings = set()
        for i in range(len(seq) - 1):
            substrings.add(seq[i:i+2])
        
        return len(substrings) / max(1, len(seq) - 1)
    
    def _find_longest_homopolymer(self, seq):
        """Find longest homopolymer run."""
        if not seq:
            return 0
        
        max_run = 1
        current_run = 1
        
        for i in range(1, len(seq)):
            if seq[i] == seq[i-1]:
                current_run += 1
                max_run = max(max_run, current_run)
            else:
                current_run = 1
        
        return max_run
    
    def _estimate_repetitive_content(self, seq):
        """Estimate repetitive content."""
        if len(seq) < 10:
            return 0
        
        # Count overlapping 4-mers
        kmers = {}
        for i in range(len(seq) - 3):
            kmer = seq[i:i+4]
            kmers[kmer] = kmers.get(kmer, 0) + 1
        
        # Calculate fraction of sequence in repeated 4-mers
        repeated_bases = sum(count * 4 for count in kmers.values() if count > 1)
        return min(1.0, repeated_bases / len(seq))

class AdvancedClassifierEvaluator:
    """Evaluate advanced classifiers for 90%+ performance."""
    
    def __init__(self):
        self.classifiers = self._create_optimized_classifiers()
        self.scaler = StandardScaler()
    
    def _create_optimized_classifiers(self):
        """Create optimized classifiers for high performance."""
        return {
            'XGBoost_Optimized': xgb.XGBClassifier(
                n_estimators=500,
                learning_rate=0.05,
                max_depth=8,
                min_child_weight=3,
                subsample=0.8,
                colsample_bytree=0.8,
                reg_alpha=0.1,
                reg_lambda=1.0,
                random_state=42,
                eval_metric='logloss',
                n_jobs=-1
            ),
            
            'LightGBM_Optimized': lgb.LGBMClassifier(
                n_estimators=500,
                learning_rate=0.05,
                max_depth=8,
                min_child_samples=10,
                subsample=0.8,
                colsample_bytree=0.8,
                reg_alpha=0.1,
                reg_lambda=1.0,
                random_state=42,
                verbose=-1,
                n_jobs=-1
            ),
            
            'Random_Forest_Optimized': RandomForestClassifier(
                n_estimators=1000,
                max_depth=20,
                min_samples_split=5,
                min_samples_leaf=2,
                max_features='sqrt',
                bootstrap=True,
                oob_score=True,
                random_state=42,
                n_jobs=-1
            ),
            
            'Extra_Trees_Optimized': ExtraTreesClassifier(
                n_estimators=1000,
                max_depth=20,
                min_samples_split=5,
                min_samples_leaf=2,
                max_features='sqrt',
                bootstrap=True,
                oob_score=True,
                random_state=42,
                n_jobs=-1
            ),
            
            'SVM_RBF_Optimized': SVC(
                kernel='rbf',
                C=10.0,
                gamma='scale',
                probability=True,
                random_state=42
            ),
            
            'MLP_Deep_Optimized': MLPClassifier(
                hidden_layer_sizes=(512, 256, 128, 64),
                activation='relu',
                solver='adam',
                alpha=0.001,
                learning_rate='adaptive',
                learning_rate_init=0.001,
                max_iter=2000,
                early_stopping=True,
                validation_fraction=0.1,
                n_iter_no_change=20,
                random_state=42
            ),
        }
    
    def evaluate_all_classifiers(self, features, labels, cv_folds=10):
        """Evaluate all classifiers with comprehensive cross-validation."""
        print(f"🔄 Evaluating {len(self.classifiers)} optimized classifiers...")
        
        # Feature preprocessing
        features_scaled = self.scaler.fit_transform(features)
        
        # Feature selection
        selector = SelectKBest(f_classif, k=min(100, features.shape[1]))
        features_selected = selector.fit_transform(features_scaled, labels)
        
        print(f"📊 Feature selection: {features.shape[1]} → {features_selected.shape[1]} features")
        
        results = {}
        cv = StratifiedKFold(n_splits=cv_folds, shuffle=True, random_state=42)
        
        for name, classifier in self.classifiers.items():
            print(f"\n📊 Evaluating {name}...")
            
            try:
                # Cross-validation
                cv_scores = {
                    'accuracy': cross_val_score(classifier, features_selected, labels, cv=cv, scoring='accuracy'),
                    'f1': cross_val_score(classifier, features_selected, labels, cv=cv, scoring='f1_weighted'),
                    'precision': cross_val_score(classifier, features_selected, labels, cv=cv, scoring='precision_weighted'),
                    'recall': cross_val_score(classifier, features_selected, labels, cv=cv, scoring='recall_weighted'),
                    'roc_auc': cross_val_score(classifier, features_selected, labels, cv=cv, scoring='roc_auc')
                }
                
                # Store results
                results[name] = {}
                for metric, scores in cv_scores.items():
                    results[name][metric] = {
                        'mean': scores.mean(),
                        'std': scores.std(),
                        'scores': scores.tolist()
                    }
                
                # Print key metrics
                print(f"   Accuracy: {results[name]['accuracy']['mean']:.4f} ± {results[name]['accuracy']['std']:.4f}")
                print(f"   F1 Score: {results[name]['f1']['mean']:.4f} ± {results[name]['f1']['std']:.4f}")
                print(f"   ROC AUC:  {results[name]['roc_auc']['mean']:.4f} ± {results[name]['roc_auc']['std']:.4f}")
                
                # Check if target achieved
                if results[name]['f1']['mean'] >= 0.90:
                    print(f"   🎯 TARGET ACHIEVED: {results[name]['f1']['mean']:.1%} ≥ 90%")
                elif results[name]['f1']['mean'] >= 0.85:
                    print(f"   🔥 EXCELLENT: {results[name]['f1']['mean']:.1%} ≥ 85%")
                elif results[name]['f1']['mean'] >= 0.80:
                    print(f"   ✅ VERY GOOD: {results[name]['f1']['mean']:.1%} ≥ 80%")
                
            except Exception as e:
                print(f"   ❌ Error: {e}")
                continue
        
        return results, features_selected

def main():
    """Main execution function."""
    print("🚀 Comprehensive Sequence Feature Classification for 90%+ Performance")
    print("=" * 80)
    
    # Load data
    from downstream.train_lncrna_function_cv import load_fasta_data
    sequences, labels = load_fasta_data("data/567_hum_label.fa")
    
    print(f"📊 Dataset loaded:")
    print(f"   Sequences: {len(sequences)}")
    print(f"   Labels: {dict(zip(*np.unique(labels, return_counts=True)))}")
    
    # Extract comprehensive features
    feature_extractor = ComprehensiveSequenceFeatureExtractor()
    features = feature_extractor.extract_all_features(sequences)
    
    # Evaluate classifiers
    evaluator = AdvancedClassifierEvaluator()
    results, features_selected = evaluator.evaluate_all_classifiers(features, labels)
    
    # Find best classifier
    if results:
        best_classifier = max(results.items(), key=lambda x: x[1]['f1']['mean'])
        
        print(f"\n🏆 BEST CLASSIFIER:")
        print(f"   Method: {best_classifier[0]}")
        print(f"   F1 Score: {best_classifier[1]['f1']['mean']:.4f} ± {best_classifier[1]['f1']['std']:.4f}")
        print(f"   Accuracy: {best_classifier[1]['accuracy']['mean']:.4f} ± {best_classifier[1]['accuracy']['std']:.4f}")
        print(f"   ROC AUC: {best_classifier[1]['roc_auc']['mean']:.4f} ± {best_classifier[1]['roc_auc']['std']:.4f}")
        
        if best_classifier[1]['f1']['mean'] >= 0.90:
            print(f"   🎯 SUCCESS: {best_classifier[1]['f1']['mean']:.1%} ≥ 90% TARGET ACHIEVED!")
        else:
            print(f"   📈 Progress: {best_classifier[1]['f1']['mean']:.1%} (need {0.90 - best_classifier[1]['f1']['mean']:.1%} more)")
        
        # Save results
        with open('sequence_feature_results.json', 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"\n💾 Results saved to: sequence_feature_results.json")
        
        # Summary of top performers
        sorted_results = sorted(results.items(), key=lambda x: x[1]['f1']['mean'], reverse=True)
        print(f"\n📊 Top 5 Performers:")
        for i, (name, metrics) in enumerate(sorted_results[:5], 1):
            print(f"   {i}. {name}: F1 = {metrics['f1']['mean']:.4f}")

if __name__ == "__main__":
    main()
