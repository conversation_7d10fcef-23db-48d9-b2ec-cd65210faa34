#!/usr/bin/env python3
"""
Sliding window dataset implementation for lncRNA function prediction
Handles sequences with 1024 window size, 512 stride, and 9216 truncation
"""

import torch
from torch.utils.data import Dataset
import numpy as np
from typing import List, Tuple, Optional

class SlidingWindowDataset(Dataset):
    """
    Dataset that handles sliding windows for long sequences.
    
    Features:
    - Window size: 1024
    - Stride: 512
    - Max sequence length: 9216 (truncation)
    - Sequence-level aggregation training
    """
    
    def __init__(
        self,
        sequences: List[str],
        labels: List[int],
        tokenizer,
        window_size: int = 1024,
        window_stride: int = 512,
        max_length: int = 9216,
        return_windows: bool = False
    ):
        """
        Initialize sliding window dataset.
        
        Args:
            sequences: List of RNA sequences
            labels: List of labels (0 or 1)
            tokenizer: Tokenizer for encoding sequences
            window_size: Size of sliding window (default: 1024)
            window_stride: Stride of sliding window (default: 512)
            max_length: Maximum sequence length before truncation (default: 9216)
            return_windows: If True, return all windows; if False, return aggregated
        """
        self.sequences = sequences
        self.labels = labels
        self.tokenizer = tokenizer
        self.window_size = window_size
        self.window_stride = window_stride
        self.max_length = max_length
        self.return_windows = return_windows
        
        # Preprocess sequences and create window mappings
        self.processed_data = self._preprocess_sequences()
        
    def _preprocess_sequences(self):
        """Preprocess sequences and create window information."""
        processed_data = []
        
        for seq_idx, (sequence, label) in enumerate(zip(self.sequences, self.labels)):
            # Truncate if too long
            if len(sequence) > self.max_length:
                sequence = sequence[:self.max_length]
            
            # Create windows for this sequence
            windows = self._create_windows(sequence)
            
            processed_data.append({
                'sequence_id': seq_idx,
                'original_sequence': sequence,
                'label': label,
                'windows': windows,
                'num_windows': len(windows)
            })
        
        return processed_data
    
    def _create_windows(self, sequence: str) -> List[str]:
        """Create sliding windows for a sequence."""
        if len(sequence) <= self.window_size:
            # If sequence is shorter than window size, return as is
            return [sequence]
        
        windows = []
        start = 0
        
        while start < len(sequence):
            end = min(start + self.window_size, len(sequence))
            window = sequence[start:end]
            
            # Only add window if it has reasonable length
            if len(window) >= self.window_size // 2:  # At least half window size
                windows.append(window)
            
            # Move to next position
            start += self.window_stride
            
            # Stop if we've covered the sequence
            if end == len(sequence):
                break
        
        return windows
    
    def __len__(self):
        """Return total number of items."""
        if self.return_windows:
            # Return total number of windows across all sequences
            return sum(item['num_windows'] for item in self.processed_data)
        else:
            # Return number of sequences
            return len(self.processed_data)
    
    def __getitem__(self, idx):
        """Get item by index."""
        if self.return_windows:
            return self._get_window_item(idx)
        else:
            return self._get_sequence_item(idx)
    
    def _get_sequence_item(self, idx):
        """Get sequence-level item (all windows for a sequence)."""
        item = self.processed_data[idx]
        
        # Tokenize all windows
        tokenized_windows = []
        for window in item['windows']:
            tokenized = self.tokenizer(
                window,
                truncation=True,
                padding='max_length',
                max_length=self.window_size,
                return_tensors='pt'
            )
            tokenized_windows.append(tokenized)
        
        # Stack windows
        if len(tokenized_windows) == 1:
            # Single window case
            input_ids = tokenized_windows[0]['input_ids'].squeeze(0)
            attention_mask = tokenized_windows[0]['attention_mask'].squeeze(0)
        else:
            # Multiple windows case
            input_ids = torch.stack([w['input_ids'].squeeze(0) for w in tokenized_windows])
            attention_mask = torch.stack([w['attention_mask'].squeeze(0) for w in tokenized_windows])
        
        return {
            'input_ids': input_ids,
            'attention_mask': attention_mask,
            'labels': torch.tensor(item['label'], dtype=torch.long),
            'num_windows': torch.tensor(item['num_windows'], dtype=torch.long),
            'sequence_id': torch.tensor(item['sequence_id'], dtype=torch.long)
        }
    
    def _get_window_item(self, idx):
        """Get individual window item."""
        # Find which sequence and window this index corresponds to
        current_idx = 0
        for seq_item in self.processed_data:
            if current_idx + seq_item['num_windows'] > idx:
                # Found the sequence
                window_idx = idx - current_idx
                window = seq_item['windows'][window_idx]
                
                tokenized = self.tokenizer(
                    window,
                    truncation=True,
                    padding='max_length',
                    max_length=self.window_size,
                    return_tensors='pt'
                )
                
                return {
                    'input_ids': tokenized['input_ids'].squeeze(0),
                    'attention_mask': tokenized['attention_mask'].squeeze(0),
                    'labels': torch.tensor(seq_item['label'], dtype=torch.long),
                    'sequence_id': torch.tensor(seq_item['sequence_id'], dtype=torch.long),
                    'window_id': torch.tensor(window_idx, dtype=torch.long)
                }
            
            current_idx += seq_item['num_windows']
        
        raise IndexError(f"Index {idx} out of range")
    
    def get_sequence_info(self, seq_idx: int) -> dict:
        """Get information about a specific sequence."""
        if seq_idx >= len(self.processed_data):
            raise IndexError(f"Sequence index {seq_idx} out of range")
        
        item = self.processed_data[seq_idx]
        return {
            'sequence_id': seq_idx,
            'original_length': len(item['original_sequence']),
            'num_windows': item['num_windows'],
            'label': item['label'],
            'windows_info': [
                {
                    'window_id': i,
                    'length': len(window),
                    'start_pos': i * self.window_stride,
                    'end_pos': min(i * self.window_stride + len(window), len(item['original_sequence']))
                }
                for i, window in enumerate(item['windows'])
            ]
        }
    
    def get_dataset_stats(self) -> dict:
        """Get dataset statistics."""
        total_sequences = len(self.processed_data)
        total_windows = sum(item['num_windows'] for item in self.processed_data)
        
        sequence_lengths = [len(item['original_sequence']) for item in self.processed_data]
        window_counts = [item['num_windows'] for item in self.processed_data]
        
        label_counts = {}
        for item in self.processed_data:
            label = item['label']
            label_counts[label] = label_counts.get(label, 0) + 1
        
        return {
            'total_sequences': total_sequences,
            'total_windows': total_windows,
            'avg_windows_per_sequence': total_windows / total_sequences,
            'sequence_length_stats': {
                'min': min(sequence_lengths),
                'max': max(sequence_lengths),
                'mean': np.mean(sequence_lengths),
                'median': np.median(sequence_lengths)
            },
            'window_count_stats': {
                'min': min(window_counts),
                'max': max(window_counts),
                'mean': np.mean(window_counts),
                'median': np.median(window_counts)
            },
            'label_distribution': label_counts,
            'sequences_needing_windows': sum(1 for item in self.processed_data if item['num_windows'] > 1),
            'sequences_truncated': sum(1 for seq in self.sequences if len(seq) > self.max_length)
        }

def load_fasta_data(fasta_path: str) -> Tuple[List[str], List[int]]:
    """Load sequences and labels from FASTA file."""
    sequences = []
    labels = []
    
    with open(fasta_path, 'r') as f:
        lines = f.readlines()
    
    i = 0
    while i < len(lines):
        line = lines[i].strip()
        if line.startswith('label='):
            # Extract label
            label = int(line.split('=')[1])
            labels.append(label)
            
            # Get sequence (next line)
            if i + 1 < len(lines):
                sequence = lines[i + 1].strip()
                sequences.append(sequence)
            i += 2
        else:
            i += 1
    
    return sequences, labels

# Example usage and testing
if __name__ == "__main__":
    # Test the sliding window dataset
    print("🧪 Testing Sliding Window Dataset")
    print("=" * 50)
    
    # Create dummy tokenizer for testing
    class DummyTokenizer:
        def __call__(self, text, **kwargs):
            # Simple character-level tokenization for testing
            tokens = [ord(c) for c in text[:kwargs.get('max_length', len(text))]]
            max_len = kwargs.get('max_length', len(tokens))
            
            # Pad or truncate
            if len(tokens) < max_len:
                tokens.extend([0] * (max_len - len(tokens)))
            else:
                tokens = tokens[:max_len]
            
            attention_mask = [1 if t != 0 else 0 for t in tokens]
            
            return {
                'input_ids': torch.tensor([tokens]),
                'attention_mask': torch.tensor([attention_mask])
            }
    
    # Test with sample data
    sequences = [
        "ATCG" * 100,  # Short sequence (400 bp)
        "GCTA" * 500,  # Medium sequence (2000 bp)
        "ATGC" * 1000  # Long sequence (4000 bp)
    ]
    labels = [0, 1, 0]
    
    tokenizer = DummyTokenizer()
    dataset = SlidingWindowDataset(sequences, labels, tokenizer)
    
    print(f"Dataset length: {len(dataset)}")
    print("\nDataset statistics:")
    stats = dataset.get_dataset_stats()
    for key, value in stats.items():
        print(f"  {key}: {value}")
    
    print("\nSequence info:")
    for i in range(len(sequences)):
        info = dataset.get_sequence_info(i)
        print(f"  Sequence {i}: {info['num_windows']} windows, length {info['original_length']}")
    
    print("\n✅ Sliding window dataset test completed!")
