#!/usr/bin/env python3
"""
Test script for single fold training with memory optimization
"""

import os
import subprocess
import sys

def test_single_fold():
    """Test training on a single fold with optimized settings."""
    
    print("🧪 Testing Single Fold Training")
    print("=" * 40)
    
    # Test configuration
    model_type = "rna-fm"
    model_path = "./checkpoint/opensource/rna-fm"
    data_dir = "data/cv_splits_565"
    output_dir = "outputs/test_single_fold"
    fold = 0
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Check if model path exists
    if not os.path.exists(model_path):
        print(f"❌ Model path not found: {model_path}")
        return False
    
    # Check if data files exist
    train_file = os.path.join(data_dir, f"fold_{fold}_train.fa")
    val_file = os.path.join(data_dir, f"fold_{fold}_val.fa")
    
    if not os.path.exists(train_file):
        print(f"❌ Training file not found: {train_file}")
        return False
    
    if not os.path.exists(val_file):
        print(f"❌ Validation file not found: {val_file}")
        return False
    
    print(f"✅ Model path: {model_path}")
    print(f"✅ Training file: {train_file}")
    print(f"✅ Validation file: {val_file}")
    
    # Prepare command with memory-optimized settings
    cmd = [
        "python", "train_full_finetune_565.py",
        "--model_name_or_path", model_path,
        "--data_path", data_dir,
        "--data_train_path", f"fold_{fold}_train.fa",
        "--data_val_path", f"fold_{fold}_val.fa",
        "--output_dir", output_dir,
        "--model_type", model_type,
        
        # Model configuration
        "--model_max_length", "1024",
        "--window_size", "1024",
        "--window_stride", "512",
        "--max_sequence_length", "4096",  # Reduced from 9216
        "--pooling_strategy", "mean",
        
        # Training parameters (memory optimized)
        "--per_device_train_batch_size", "1",
        "--per_device_eval_batch_size", "1",
        "--gradient_accumulation_steps", "8",
        "--learning_rate", "2e-5",
        "--num_train_epochs", "3",  # Reduced for testing
        "--patience", "5",
        "--warmup_steps", "50",
        
        # Logging and evaluation
        "--logging_steps", "10",
        "--eval_steps", "50",
        "--save_steps", "100",
        "--evaluation_strategy", "steps",
        "--save_strategy", "steps",
        "--load_best_model_at_end", "True",
        "--metric_for_best_model", "eval_f1",
        "--greater_is_better", "True",
        
        # Memory optimization
        "--fp16",
        "--dataloader_num_workers", "2",
        "--seed", "42",
        "--overwrite_output_dir"
    ]
    
    print(f"\n🚀 Starting test training...")
    print(f"Command: {' '.join(cmd[:10])}...")  # Show first 10 args
    
    try:
        # Run training
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=1800)  # 30 min timeout
        
        if result.returncode == 0:
            print("✅ Test training completed successfully!")
            print("\nLast few lines of output:")
            output_lines = result.stdout.split('\n')
            for line in output_lines[-10:]:
                if line.strip():
                    print(f"  {line}")
            return True
        else:
            print("❌ Test training failed!")
            print(f"Error code: {result.returncode}")
            print("\nError output:")
            error_lines = result.stderr.split('\n')
            for line in error_lines[-20:]:
                if line.strip():
                    print(f"  {line}")
            return False
            
    except subprocess.TimeoutExpired:
        print("⏰ Test training timed out (30 minutes)")
        return False
    except Exception as e:
        print(f"💥 Exception during test training: {e}")
        return False

def check_gpu_memory():
    """Check GPU memory status."""
    try:
        import torch
        if torch.cuda.is_available():
            print(f"🔧 GPU Memory Status:")
            for i in range(torch.cuda.device_count()):
                total = torch.cuda.get_device_properties(i).total_memory / 1e9
                allocated = torch.cuda.memory_allocated(i) / 1e9
                cached = torch.cuda.memory_reserved(i) / 1e9
                free = total - cached
                print(f"  GPU {i}: {free:.1f}GB free / {total:.1f}GB total")
                print(f"    Allocated: {allocated:.1f}GB, Cached: {cached:.1f}GB")
        else:
            print("⚠️  CUDA not available")
    except ImportError:
        print("⚠️  PyTorch not available")

def main():
    """Main test function."""
    print("🧬 Single Fold Training Test")
    print("=" * 50)
    
    # Check GPU memory
    check_gpu_memory()
    
    # Test single fold
    success = test_single_fold()
    
    if success:
        print("\n🎉 Single fold test passed!")
        print("You can now run the full experiment with:")
        print("  ./run_565_experiment.sh")
    else:
        print("\n❌ Single fold test failed!")
        print("Please check the error messages above and:")
        print("1. Ensure sufficient GPU memory")
        print("2. Check model and data paths")
        print("3. Consider further reducing batch size or sequence length")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
