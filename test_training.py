#!/usr/bin/env python3
"""
Test script for LoRA training functionality
"""

import os
import sys
import torch
import tempfile
import shutil

# Add paths
sys.path.append('.')
sys.path.append('downstream')

def test_single_fold_training():
    """Test training on a single fold with minimal epochs."""
    print("🧪 Testing single fold training...")
    
    # Check if data exists
    train_data = "data/cv_splits/fold_0_train.fa"
    val_data = "data/cv_splits/fold_0_val.fa"
    
    if not os.path.exists(train_data) or not os.path.exists(val_data):
        print("❌ CV split data not found. Run data_split_cv.py first.")
        return False
    
    # Create temporary output directory
    temp_dir = tempfile.mkdtemp()
    
    try:
        # Test RNA-FM training
        print("  Testing RNA-FM training...")
        
        cmd = [
            "python", "train_lncrna_function_lora.py",
            "--model_name_or_path", "./checkpoint/opensource/rna-fm/",
            "--data_path", "data/cv_splits",
            "--data_train_path", "fold_0_train.fa",
            "--data_val_path", "fold_0_val.fa",
            "--output_dir", temp_dir,
            "--run_name", "test_rna_fm",
            "--model_type", "rna-fm",
            
            # Minimal training for testing
            "--num_train_epochs", "1",
            "--per_device_train_batch_size", "2",
            "--per_device_eval_batch_size", "2",
            "--gradient_accumulation_steps", "1",
            "--learning_rate", "1e-4",
            "--evaluation_strategy", "steps",
            "--save_strategy", "steps",
            "--eval_steps", "10",
            "--save_steps", "10",  # Must match eval_steps when using load_best_model_at_end
            "--logging_steps", "5",
            
            # LoRA parameters
            "--use_lora", "True",
            "--lora_r", "8",
            "--lora_alpha", "16",
            "--lora_dropout", "0.1",
            "--lora_target_modules", "query,value",
            
            # Other parameters
            "--window_size", "512",  # Smaller for testing
            "--window_stride", "256",
            "--max_sequence_length", "2048",
            "--pooling_strategy", "mean",
            "--patience", "3",
            "--seed", "42",
            "--overwrite_output_dir", "True",
            "--load_best_model_at_end", "True",
            "--metric_for_best_model", "eval_f1",
            "--greater_is_better", "True",
        ]
        
        import subprocess
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)  # 5 min timeout
        
        if result.returncode == 0:
            print("  ✅ RNA-FM training test passed")
            return True
        else:
            print(f"  ❌ RNA-FM training test failed")
            print(f"  Error: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("  ❌ Training test timed out")
        return False
    except Exception as e:
        print(f"  ❌ Training test error: {e}")
        return False
    finally:
        # Clean up
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)

def test_data_loading():
    """Test data loading and processing."""
    print("🧪 Testing data loading and processing...")
    
    try:
        from train_lncrna_function_lora import (
            load_fasta_data, create_sliding_windows, SlidingWindowDataset
        )
        from tokenizer.tokenization_opensource import OpenRnaLMTokenizer
        
        # Test data loading
        train_data = "data/cv_splits/fold_0_train.fa"
        if not os.path.exists(train_data):
            print("❌ Training data not found")
            return False
        
        sequences, labels = load_fasta_data(train_data)
        print(f"  Loaded {len(sequences)} sequences")
        
        # Test sliding windows
        test_seq = sequences[0]
        windows = create_sliding_windows(test_seq, 512, 256, 2048)
        print(f"  Created {len(windows)} windows from sequence of length {len(test_seq)}")
        
        # Test tokenizer
        tokenizer = OpenRnaLMTokenizer.from_pretrained(
            "./checkpoint/opensource/rna-fm/",
            model_max_length=512,
            padding_side="right",
            use_fast=True,
            trust_remote_code=True,
        )
        print("  Tokenizer loaded successfully")
        
        # Test dataset
        class TestArgs:
            def __init__(self):
                self.window_size = 512
                self.window_stride = 256
                self.max_sequence_length = 2048
                self.model_max_length = 512
        
        test_args = TestArgs()
        dataset = SlidingWindowDataset(
            data_path=train_data,
            args=test_args,
            tokenizer=tokenizer
        )
        print(f"  Dataset created with {len(dataset)} samples")
        
        # Test data item
        item = dataset[0]
        print(f"  Sample item keys: {list(item.keys())}")
        print(f"  Input IDs shape: {item['input_ids'].shape}")
        print(f"  Label: {item['labels']}")
        
        print("  ✅ Data loading and processing test passed")
        return True
        
    except Exception as e:
        print(f"  ❌ Data loading test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_loading():
    """Test model loading with LoRA."""
    print("🧪 Testing model loading with LoRA...")
    
    try:
        from model.rnafm.modeling_rnafm import RnaFmForSequenceClassification
        from peft import LoraConfig, get_peft_model, TaskType
        from train_lncrna_function_lora import SlidingWindowModel
        
        # Load base model
        base_model = RnaFmForSequenceClassification.from_pretrained(
            "./checkpoint/opensource/rna-fm/",
            num_labels=2,
            problem_type="single_label_classification",
            trust_remote_code=True,
        )
        print("  Base model loaded")
        
        # Apply LoRA
        lora_config = LoraConfig(
            task_type=TaskType.SEQ_CLS,
            r=8,
            lora_alpha=16,
            lora_dropout=0.1,
            target_modules=["query", "value"],
            bias="none",
        )
        
        lora_model = get_peft_model(base_model, lora_config)
        print("  LoRA applied")
        
        # Wrap with sliding window model
        model = SlidingWindowModel(lora_model, pooling_strategy='mean')
        print("  Sliding window model created")
        
        # Test forward pass
        batch_size, seq_length = 2, 512
        # Use valid token IDs (RNA-FM vocab size is 25)
        dummy_input_ids = torch.randint(0, 25, (batch_size, seq_length))
        dummy_attention_mask = torch.ones(batch_size, seq_length)
        dummy_labels = torch.randint(0, 2, (batch_size,))

        with torch.no_grad():
            outputs = model(
                input_ids=dummy_input_ids,
                attention_mask=dummy_attention_mask,
                labels=dummy_labels
            )
        
        print(f"  Forward pass successful, logits shape: {outputs['logits'].shape}")
        print("  ✅ Model loading test passed")
        return True
        
    except Exception as e:
        print(f"  ❌ Model loading test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("🧪 LoRA Training Functionality Test")
    print("=" * 40)
    
    tests = [
        ("Data Loading and Processing", test_data_loading),
        ("Model Loading with LoRA", test_model_loading),
        ("Single Fold Training", test_single_fold_training),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * len(test_name))
        results[test_name] = test_func()
        print()
    
    # Summary
    print("📋 Test Summary:")
    print("=" * 20)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All training tests passed! Ready for full experiment.")
        return True
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Please fix issues before running full experiment.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
