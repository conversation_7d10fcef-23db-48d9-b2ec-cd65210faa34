#!/usr/bin/env python3
"""
Analyze and compare prediction results from RNA-FM and SpliceBERT-MS1024 models
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import json
from collections import Counter

def load_prediction_results():
    """Load prediction results from both models"""
    
    print("📂 Loading prediction results...")
    
    # Load RNA-FM results
    rna_fm_df = pd.read_csv('predictions/rna_fm_predictions.csv')
    print(f"✅ RNA-FM: {len(rna_fm_df)} sequences loaded")
    
    # Load SpliceBERT results
    splicebert_df = pd.read_csv('predictions/splicebert_predictions.csv')
    print(f"✅ SpliceBERT: {len(splicebert_df)} sequences loaded")
    
    # Load statistics
    with open('predictions/rna_fm_statistics.json', 'r') as f:
        rna_fm_stats = json.load(f)
    
    with open('predictions/splicebert_statistics.json', 'r') as f:
        splicebert_stats = json.load(f)
    
    return rna_fm_df, splicebert_df, rna_fm_stats, splicebert_stats

def compare_predictions(rna_fm_df, splicebert_df):
    """Compare predictions between two models"""
    
    print("\n🔍 Comparing model predictions...")
    
    # Merge dataframes on sequence_id
    merged_df = pd.merge(
        rna_fm_df[['sequence_id', 'rna_fm_prediction', 'rna_fm_probability']], 
        splicebert_df[['sequence_id', 'splicebert_prediction', 'splicebert_probability']], 
        on='sequence_id'
    )
    
    # Agreement analysis
    agreement = (merged_df['rna_fm_prediction'] == merged_df['splicebert_prediction']).sum()
    disagreement = len(merged_df) - agreement
    agreement_pct = (agreement / len(merged_df)) * 100
    
    print(f"📊 Model Agreement Analysis:")
    print(f"   Total sequences: {len(merged_df):,}")
    print(f"   Agreements: {agreement:,} ({agreement_pct:.1f}%)")
    print(f"   Disagreements: {disagreement:,} ({100-agreement_pct:.1f}%)")
    
    # Detailed agreement breakdown
    both_functional = ((merged_df['rna_fm_prediction'] == 1) & 
                      (merged_df['splicebert_prediction'] == 1)).sum()
    both_nonfunctional = ((merged_df['rna_fm_prediction'] == 0) & 
                         (merged_df['splicebert_prediction'] == 0)).sum()
    rna_fm_only = ((merged_df['rna_fm_prediction'] == 1) & 
                   (merged_df['splicebert_prediction'] == 0)).sum()
    splicebert_only = ((merged_df['rna_fm_prediction'] == 0) & 
                       (merged_df['splicebert_prediction'] == 1)).sum()
    
    print(f"\n📈 Detailed Breakdown:")
    print(f"   Both predict Functional: {both_functional:,} ({both_functional/len(merged_df)*100:.1f}%)")
    print(f"   Both predict Non-functional: {both_nonfunctional:,} ({both_nonfunctional/len(merged_df)*100:.1f}%)")
    print(f"   Only RNA-FM predicts Functional: {rna_fm_only:,} ({rna_fm_only/len(merged_df)*100:.1f}%)")
    print(f"   Only SpliceBERT predicts Functional: {splicebert_only:,} ({splicebert_only/len(merged_df)*100:.1f}%)")
    
    return merged_df

def analyze_sequence_lengths(rna_fm_df, splicebert_df):
    """Analyze sequence length distribution and its impact on predictions"""
    
    print("\n📏 Sequence Length Analysis...")
    
    # Basic length statistics
    lengths = rna_fm_df['sequence_length']
    
    print(f"📊 Length Statistics:")
    print(f"   Mean length: {lengths.mean():.0f} bp")
    print(f"   Median length: {lengths.median():.0f} bp")
    print(f"   Min length: {lengths.min()} bp")
    print(f"   Max length: {lengths.max()} bp")
    print(f"   Std deviation: {lengths.std():.0f} bp")
    
    # Length categories
    short_seqs = (lengths <= 1024).sum()
    medium_seqs = ((lengths > 1024) & (lengths <= 5000)).sum()
    long_seqs = (lengths > 5000).sum()
    
    print(f"\n📈 Length Categories:")
    print(f"   Short (≤1024 bp): {short_seqs:,} ({short_seqs/len(lengths)*100:.1f}%)")
    print(f"   Medium (1024-5000 bp): {medium_seqs:,} ({medium_seqs/len(lengths)*100:.1f}%)")
    print(f"   Long (>5000 bp): {long_seqs:,} ({long_seqs/len(lengths)*100:.1f}%)")
    
    return lengths

def create_visualization(rna_fm_stats, splicebert_stats, merged_df, lengths):
    """Create comprehensive visualization of results"""
    
    print("\n📊 Creating visualizations...")
    
    # Set up the plot style
    plt.style.use('default')
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    
    # 1. Model comparison bar chart
    models = ['RNA-FM', 'SpliceBERT-MS1024']
    functional_counts = [rna_fm_stats['functional_count'], splicebert_stats['functional_count']]
    nonfunctional_counts = [rna_fm_stats['nonfunctional_count'], splicebert_stats['nonfunctional_count']]
    
    x = np.arange(len(models))
    width = 0.35
    
    axes[0,0].bar(x - width/2, functional_counts, width, label='Functional', color='skyblue', alpha=0.8)
    axes[0,0].bar(x + width/2, nonfunctional_counts, width, label='Non-functional', color='lightcoral', alpha=0.8)
    
    axes[0,0].set_xlabel('Models', fontweight='bold')
    axes[0,0].set_ylabel('Number of lncRNAs', fontweight='bold')
    axes[0,0].set_title('Functional vs Non-functional Predictions', fontweight='bold')
    axes[0,0].set_xticks(x)
    axes[0,0].set_xticklabels(models)
    axes[0,0].legend()
    axes[0,0].grid(True, alpha=0.3)
    
    # Add value labels on bars
    for i, (func, nonfunc) in enumerate(zip(functional_counts, nonfunctional_counts)):
        axes[0,0].text(i - width/2, func + 1000, f'{func:,}', ha='center', va='bottom', fontweight='bold')
        axes[0,0].text(i + width/2, nonfunc + 1000, f'{nonfunc:,}', ha='center', va='bottom', fontweight='bold')
    
    # 2. Agreement analysis pie chart
    agreement_data = [
        ((merged_df['rna_fm_prediction'] == 1) & (merged_df['splicebert_prediction'] == 1)).sum(),
        ((merged_df['rna_fm_prediction'] == 0) & (merged_df['splicebert_prediction'] == 0)).sum(),
        ((merged_df['rna_fm_prediction'] == 1) & (merged_df['splicebert_prediction'] == 0)).sum(),
        ((merged_df['rna_fm_prediction'] == 0) & (merged_df['splicebert_prediction'] == 1)).sum()
    ]
    
    labels = ['Both Functional', 'Both Non-functional', 'RNA-FM Only', 'SpliceBERT Only']
    colors = ['lightgreen', 'lightcoral', 'gold', 'lightblue']
    
    axes[0,1].pie(agreement_data, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
    axes[0,1].set_title('Model Agreement Analysis', fontweight='bold')
    
    # 3. Sequence length distribution
    axes[1,0].hist(lengths, bins=50, alpha=0.7, color='purple', edgecolor='black')
    axes[1,0].axvline(lengths.mean(), color='red', linestyle='--', linewidth=2, label=f'Mean: {lengths.mean():.0f} bp')
    axes[1,0].axvline(lengths.median(), color='orange', linestyle='--', linewidth=2, label=f'Median: {lengths.median():.0f} bp')
    axes[1,0].set_xlabel('Sequence Length (bp)', fontweight='bold')
    axes[1,0].set_ylabel('Frequency', fontweight='bold')
    axes[1,0].set_title('lncRNA Sequence Length Distribution', fontweight='bold')
    axes[1,0].legend()
    axes[1,0].grid(True, alpha=0.3)
    
    # 4. Probability distribution comparison
    axes[1,1].hist(merged_df['rna_fm_probability'], bins=50, alpha=0.6, label='RNA-FM', color='blue', density=True)
    axes[1,1].hist(merged_df['splicebert_probability'], bins=50, alpha=0.6, label='SpliceBERT', color='red', density=True)
    axes[1,1].set_xlabel('Prediction Probability', fontweight='bold')
    axes[1,1].set_ylabel('Density', fontweight='bold')
    axes[1,1].set_title('Prediction Probability Distributions', fontweight='bold')
    axes[1,1].legend()
    axes[1,1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('lncrna_prediction_analysis.png', dpi=300, bbox_inches='tight')
    print("✅ Visualization saved: lncrna_prediction_analysis.png")

def generate_summary_report(rna_fm_stats, splicebert_stats, merged_df):
    """Generate comprehensive summary report"""
    
    print("\n" + "="*80)
    print("🧬 lncRNA FUNCTION PREDICTION - COMPREHENSIVE ANALYSIS REPORT")
    print("="*80)
    
    print(f"\n📊 DATASET OVERVIEW:")
    print(f"   Total lncRNA sequences analyzed: {rna_fm_stats['total_sequences']:,}")
    print(f"   Data source: human.lncRNA_longest.95243.fa")
    
    print(f"\n🔬 MODEL PREDICTIONS COMPARISON:")
    print(f"{'Metric':<25} {'RNA-FM':<15} {'SpliceBERT-MS1024':<20} {'Difference':<15}")
    print("-" * 80)
    
    # Functional predictions
    rna_fm_func = rna_fm_stats['functional_count']
    splicebert_func = splicebert_stats['functional_count']
    func_diff = splicebert_func - rna_fm_func
    
    print(f"{'Functional lncRNAs':<25} {rna_fm_func:<15,} {splicebert_func:<20,} {func_diff:+<15,}")
    print(f"{'Functional %':<25} {rna_fm_stats['functional_percentage']:<15.1f} {splicebert_stats['functional_percentage']:<20.1f} {splicebert_stats['functional_percentage']-rna_fm_stats['functional_percentage']:+<15.1f}")
    
    # Non-functional predictions
    rna_fm_nonfunc = rna_fm_stats['nonfunctional_count']
    splicebert_nonfunc = splicebert_stats['nonfunctional_count']
    nonfunc_diff = splicebert_nonfunc - rna_fm_nonfunc
    
    print(f"{'Non-functional lncRNAs':<25} {rna_fm_nonfunc:<15,} {splicebert_nonfunc:<20,} {nonfunc_diff:+<15,}")
    print(f"{'Non-functional %':<25} {rna_fm_stats['nonfunctional_percentage']:<15.1f} {splicebert_stats['nonfunctional_percentage']:<20.1f} {splicebert_stats['nonfunctional_percentage']-rna_fm_stats['nonfunctional_percentage']:+<15.1f}")
    
    # Confidence metrics
    print(f"{'Avg Probability':<25} {rna_fm_stats['average_probability']:<15.3f} {splicebert_stats['average_probability']:<20.3f} {splicebert_stats['average_probability']-rna_fm_stats['average_probability']:+<15.3f}")
    print(f"{'High Confidence %':<25} {rna_fm_stats['high_confidence_percentage']:<15.1f} {splicebert_stats['high_confidence_percentage']:<20.1f} {splicebert_stats['high_confidence_percentage']-rna_fm_stats['high_confidence_percentage']:+<15.1f}")
    
    # Agreement analysis
    agreement = (merged_df['rna_fm_prediction'] == merged_df['splicebert_prediction']).sum()
    agreement_pct = (agreement / len(merged_df)) * 100
    
    print(f"\n🤝 MODEL AGREEMENT:")
    print(f"   Models agree on: {agreement:,} sequences ({agreement_pct:.1f}%)")
    print(f"   Models disagree on: {len(merged_df)-agreement:,} sequences ({100-agreement_pct:.1f}%)")
    
    # Key findings
    print(f"\n🔍 KEY FINDINGS:")
    if splicebert_stats['functional_percentage'] > rna_fm_stats['functional_percentage']:
        print(f"   • SpliceBERT predicts {func_diff:,} more functional lncRNAs than RNA-FM")
    else:
        print(f"   • RNA-FM predicts {-func_diff:,} more functional lncRNAs than SpliceBERT")
    
    print(f"   • SpliceBERT shows higher confidence (avg prob: {splicebert_stats['average_probability']:.3f} vs {rna_fm_stats['average_probability']:.3f})")
    print(f"   • Both models predict majority of lncRNAs as functional (>90%)")
    print(f"   • High model agreement ({agreement_pct:.1f}%) suggests consistent predictions")
    
    print(f"\n📁 OUTPUT FILES GENERATED:")
    print(f"   • RNA-FM: rna_fm_predictions.csv, rna_fm_functional_lncrnas.fa, rna_fm_nonfunctional_lncrnas.fa")
    print(f"   • SpliceBERT: splicebert_predictions.csv, splicebert_functional_lncrnas.fa, splicebert_nonfunctional_lncrnas.fa")
    print(f"   • Analysis: lncrna_prediction_analysis.png")
    
    print("="*80)

def main():
    """Main analysis function"""
    
    print("🧬 lncRNA Function Prediction Results Analysis")
    print("="*60)
    
    try:
        # Load results
        rna_fm_df, splicebert_df, rna_fm_stats, splicebert_stats = load_prediction_results()
        
        # Compare predictions
        merged_df = compare_predictions(rna_fm_df, splicebert_df)
        
        # Analyze sequence lengths
        lengths = analyze_sequence_lengths(rna_fm_df, splicebert_df)
        
        # Create visualizations
        create_visualization(rna_fm_stats, splicebert_stats, merged_df, lengths)
        
        # Generate summary report
        generate_summary_report(rna_fm_stats, splicebert_stats, merged_df)
        
        print(f"\n🎉 Analysis completed successfully!")
        
    except Exception as e:
        print(f"❌ Error during analysis: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
