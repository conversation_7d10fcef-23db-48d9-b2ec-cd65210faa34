#!/usr/bin/env python3
"""
Predict lncRNA function using trained RNA-FM model
"""

import os
import sys
import torch
import numpy as np
import pandas as pd
from tqdm import tqdm
import json
from datetime import datetime

sys.path.append('.')
from tokenizer.tokenization_opensource import OpenRnaLMTokenizer
from model.rnafm.modeling_rnafm import RnaFmForSequenceClassification

def load_unlabeled_fasta(fasta_path):
    """Load FASTA file without labels"""
    sequences = []
    sequence_ids = []
    
    print(f"📂 Loading FASTA file: {fasta_path}")
    
    with open(fasta_path, 'r') as f:
        current_id = None
        current_seq = ""
        
        for line in f:
            line = line.strip()
            if line.startswith('>'):
                # Save previous sequence
                if current_id is not None and current_seq:
                    sequences.append(current_seq)
                    sequence_ids.append(current_id)
                
                # Start new sequence
                current_id = line[1:]  # Remove '>' character
                current_seq = ""
            else:
                current_seq += line
        
        # Save last sequence
        if current_id is not None and current_seq:
            sequences.append(current_seq)
            sequence_ids.append(current_id)
    
    print(f"✅ Loaded {len(sequences)} sequences")
    return sequence_ids, sequences

def predict_with_rna_fm(model_path, sequences, batch_size=8):
    """Load RNA-FM model and predict on sequences"""
    
    print(f"\n🔄 Loading RNA-FM model from: {model_path}")
    
    # Load tokenizer
    tokenizer = OpenRnaLMTokenizer.from_pretrained(
        './checkpoint/opensource/rna-fm/',
        model_max_length=1024,
        padding_side="right",
        use_fast=True,
        trust_remote_code=True,
    )
    
    # Load model
    model = RnaFmForSequenceClassification.from_pretrained(
        model_path,
        num_labels=2,
        problem_type="single_label_classification",
        trust_remote_code=True,
    )
    
    model.eval()
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model.to(device)
    print(f"✅ RNA-FM model loaded on device: {device}")
    
    # Predict in batches
    predictions = []
    probabilities = []
    
    print(f"🔮 Starting RNA-FM prediction on {len(sequences)} sequences...")
    
    with torch.no_grad():
        for i in tqdm(range(0, len(sequences), batch_size), desc="RNA-FM Prediction"):
            batch_sequences = sequences[i:i+batch_size]
            batch_predictions = []
            batch_probabilities = []
            
            for seq in batch_sequences:
                # Handle long sequences with sliding window approach
                if len(seq) > 1024:
                    # Use sliding window for long sequences
                    window_size = 1024
                    stride = 512
                    window_predictions = []
                    window_probabilities = []
                    
                    for start in range(0, len(seq), stride):
                        end = min(start + window_size, len(seq))
                        window_seq = seq[start:end]
                        
                        if len(window_seq) < 50:  # Skip very short windows
                            continue
                        
                        # Tokenize window
                        inputs = tokenizer(
                            window_seq,
                            padding='max_length',
                            max_length=1024,
                            truncation=True,
                            return_tensors='pt'
                        )
                        
                        inputs = {k: v.to(device) for k, v in inputs.items()}
                        
                        # Forward pass
                        outputs = model(**inputs)
                        logits = outputs.logits
                        probs = torch.softmax(logits, dim=-1)
                        
                        window_predictions.append(torch.argmax(logits, dim=-1).cpu().item())
                        window_probabilities.append(probs[0, 1].cpu().item())
                    
                    # Aggregate window predictions (mean probability)
                    if window_probabilities:
                        avg_prob = np.mean(window_probabilities)
                        final_pred = 1 if avg_prob > 0.5 else 0
                        batch_predictions.append(final_pred)
                        batch_probabilities.append(avg_prob)
                    else:
                        batch_predictions.append(0)
                        batch_probabilities.append(0.0)
                
                else:
                    # Direct prediction for short sequences
                    inputs = tokenizer(
                        seq,
                        padding='max_length',
                        max_length=1024,
                        truncation=True,
                        return_tensors='pt'
                    )
                    
                    inputs = {k: v.to(device) for k, v in inputs.items()}
                    
                    # Forward pass
                    outputs = model(**inputs)
                    logits = outputs.logits
                    probs = torch.softmax(logits, dim=-1)
                    
                    batch_predictions.append(torch.argmax(logits, dim=-1).cpu().item())
                    batch_probabilities.append(probs[0, 1].cpu().item())
            
            predictions.extend(batch_predictions)
            probabilities.extend(batch_probabilities)
    
    print(f"✅ RNA-FM prediction completed!")
    return np.array(predictions), np.array(probabilities)

def save_rna_fm_results(sequence_ids, sequences, predictions, probabilities, output_dir='predictions'):
    """Save RNA-FM prediction results"""
    
    os.makedirs(output_dir, exist_ok=True)
    
    # Create results DataFrame
    results_data = []
    
    for i, (seq_id, seq) in enumerate(zip(sequence_ids, sequences)):
        row = {
            'sequence_id': seq_id,
            'sequence_length': len(seq),
            'sequence': seq,
            'rna_fm_prediction': predictions[i],
            'rna_fm_probability': probabilities[i],
            'predicted_function': 'Functional' if predictions[i] == 1 else 'Non-functional'
        }
        results_data.append(row)
    
    df = pd.DataFrame(results_data)
    
    # Save comprehensive results
    csv_path = os.path.join(output_dir, 'rna_fm_predictions.csv')
    df.to_csv(csv_path, index=False)
    print(f"✅ RNA-FM results saved: {csv_path}")
    
    # Save FASTA files with predictions
    # Functional lncRNAs
    functional_path = os.path.join(output_dir, 'rna_fm_functional_lncrnas.fa')
    with open(functional_path, 'w') as f:
        for _, row in df.iterrows():
            if row['rna_fm_prediction'] == 1:
                f.write(f">{row['sequence_id']}_functional\n{row['sequence']}\n")
    
    # Non-functional lncRNAs
    nonfunctional_path = os.path.join(output_dir, 'rna_fm_nonfunctional_lncrnas.fa')
    with open(nonfunctional_path, 'w') as f:
        for _, row in df.iterrows():
            if row['rna_fm_prediction'] == 0:
                f.write(f">{row['sequence_id']}_nonfunctional\n{row['sequence']}\n")
    
    print(f"✅ FASTA files saved:")
    print(f"   - Functional: {functional_path}")
    print(f"   - Non-functional: {nonfunctional_path}")
    
    return df

def generate_rna_fm_statistics(df, output_dir='predictions'):
    """Generate RNA-FM prediction statistics"""
    
    print(f"\n📊 RNA-FM PREDICTION STATISTICS")
    print("="*50)
    
    total_sequences = len(df)
    functional_count = (df['rna_fm_prediction'] == 1).sum()
    nonfunctional_count = (df['rna_fm_prediction'] == 0).sum()
    functional_pct = (functional_count / total_sequences) * 100
    nonfunctional_pct = (nonfunctional_count / total_sequences) * 100
    
    # Probability distribution
    avg_prob = df['rna_fm_probability'].mean()
    high_confidence = ((df['rna_fm_probability'] > 0.8) | (df['rna_fm_probability'] < 0.2)).sum()
    high_confidence_pct = (high_confidence / total_sequences) * 100
    
    stats = {
        'model': 'RNA-FM',
        'total_sequences': total_sequences,
        'functional_count': int(functional_count),
        'nonfunctional_count': int(nonfunctional_count),
        'functional_percentage': functional_pct,
        'nonfunctional_percentage': nonfunctional_pct,
        'average_probability': avg_prob,
        'high_confidence_predictions': int(high_confidence),
        'high_confidence_percentage': high_confidence_pct
    }
    
    print(f"Total sequences:        {total_sequences:,}")
    print(f"Functional lncRNAs:     {functional_count:,} ({functional_pct:.1f}%)")
    print(f"Non-functional lncRNAs: {nonfunctional_count:,} ({nonfunctional_pct:.1f}%)")
    print(f"Average probability:    {avg_prob:.3f}")
    print(f"High confidence preds:  {high_confidence:,} ({high_confidence_pct:.1f}%)")
    
    # Save statistics
    stats_path = os.path.join(output_dir, 'rna_fm_statistics.json')
    with open(stats_path, 'w') as f:
        json.dump(stats, f, indent=2)
    
    print(f"\n✅ Statistics saved: {stats_path}")
    
    return stats

def main():
    """Main RNA-FM prediction function"""
    
    print("🧬 RNA-FM lncRNA Function Prediction")
    print("="*50)
    print(f"Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Configuration
    fasta_path = 'data/human.lncRNA_longest.95243.fa'
    model_path = './outputs/ft/lncrna-function/lncRNA_function/rna-fm/seed_666/checkpoint-500'
    
    try:
        # Load sequences
        sequence_ids, sequences = load_unlabeled_fasta(fasta_path)
        
        # Predict with RNA-FM
        predictions, probabilities = predict_with_rna_fm(model_path, sequences, batch_size=4)
        
        # Save results
        df = save_rna_fm_results(sequence_ids, sequences, predictions, probabilities)
        
        # Generate statistics
        stats = generate_rna_fm_statistics(df)
        
        print(f"\n🎉 RNA-FM PREDICTION COMPLETED!")
        print(f"End time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"Total sequences processed: {len(sequences):,}")
        print(f"Results saved in: ./predictions/")
        
    except Exception as e:
        print(f"❌ Error during RNA-FM prediction: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
