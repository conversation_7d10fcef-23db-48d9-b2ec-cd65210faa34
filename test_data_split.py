#!/usr/bin/env python3
"""
Test script to verify data splitting functionality
"""

import os
import sys
import numpy as np

# Add the downstream directory to path
sys.path.append('downstream')
from train_lncrna_function_cv import load_fasta_data, split_train_test

def test_data_loading():
    """Test data loading from the FASTA file."""
    print("🧪 Testing data loading...")
    
    data_file = "data/567_hum_label.fa"
    
    if not os.path.exists(data_file):
        print(f"❌ Data file not found: {data_file}")
        return False, None, None
    
    try:
        sequences, labels = load_fasta_data(data_file)
        
        print(f"✅ Successfully loaded {len(sequences)} sequences")
        print(f"   Label distribution: {dict(zip(*np.unique(labels, return_counts=True)))}")
        
        # Check sequence lengths
        seq_lengths = [len(seq) for seq in sequences]
        print(f"   Sequence lengths: min={min(seq_lengths)}, max={max(seq_lengths)}, mean={np.mean(seq_lengths):.1f}")
        
        return True, sequences, labels
        
    except Exception as e:
        print(f"❌ Error loading data: {e}")
        return False, None, None

def test_data_splitting(sequences, labels):
    """Test data splitting functionality."""
    print("\n🧪 Testing data splitting...")
    
    try:
        # Test different split ratios
        test_ratios = [0.1, 0.15, 0.2]
        
        for test_ratio in test_ratios:
            print(f"\n📊 Testing {test_ratio:.1%} test split:")
            
            train_sequences, train_labels, test_sequences, test_labels = split_train_test(
                sequences, labels,
                test_ratio=test_ratio,
                random_state=123
            )
            
            # Verify split ratios
            total_sequences = len(sequences)
            actual_test_ratio = len(test_sequences) / total_sequences
            actual_train_ratio = len(train_sequences) / total_sequences
            
            print(f"   Expected test ratio: {test_ratio:.1%}")
            print(f"   Actual test ratio: {actual_test_ratio:.1%}")
            print(f"   Actual train ratio: {actual_train_ratio:.1%}")
            
            # Verify label balance
            original_label_ratio = labels.count(1) / len(labels)
            train_label_ratio = train_labels.count(1) / len(train_labels)
            test_label_ratio = test_labels.count(1) / len(test_labels)
            
            print(f"   Original label 1 ratio: {original_label_ratio:.3f}")
            print(f"   Train label 1 ratio: {train_label_ratio:.3f}")
            print(f"   Test label 1 ratio: {test_label_ratio:.3f}")
            
            # Check if ratios are preserved (within 5% tolerance)
            train_diff = abs(train_label_ratio - original_label_ratio)
            test_diff = abs(test_label_ratio - original_label_ratio)
            
            if train_diff < 0.05 and test_diff < 0.05:
                print(f"   ✅ Label balance preserved")
            else:
                print(f"   ⚠️  Label balance deviation: train={train_diff:.3f}, test={test_diff:.3f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in data splitting: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_reproducibility(sequences, labels):
    """Test if data splitting is reproducible with same seed."""
    print("\n🧪 Testing reproducibility...")
    
    try:
        # Split with same seed multiple times
        results = []
        for i in range(3):
            train_seq, train_lab, test_seq, test_lab = split_train_test(
                sequences, labels,
                test_ratio=0.1,
                random_state=123  # Same seed
            )
            results.append((train_seq, train_lab, test_seq, test_lab))
        
        # Check if all splits are identical
        all_identical = True
        for i in range(1, len(results)):
            if (results[i][0] != results[0][0] or 
                results[i][1] != results[0][1] or
                results[i][2] != results[0][2] or
                results[i][3] != results[0][3]):
                all_identical = False
                break
        
        if all_identical:
            print("✅ Data splitting is reproducible with same seed")
        else:
            print("❌ Data splitting is not reproducible")
            
        # Test different seeds produce different splits
        train_seq1, _, test_seq1, _ = split_train_test(sequences, labels, test_ratio=0.1, random_state=123)
        train_seq2, _, test_seq2, _ = split_train_test(sequences, labels, test_ratio=0.1, random_state=456)
        
        if train_seq1 != train_seq2 or test_seq1 != test_seq2:
            print("✅ Different seeds produce different splits")
        else:
            print("❌ Different seeds produce identical splits")
            
        return all_identical
        
    except Exception as e:
        print(f"❌ Error in reproducibility test: {e}")
        return False

def test_cross_validation_compatibility(sequences, labels):
    """Test compatibility with cross-validation."""
    print("\n🧪 Testing cross-validation compatibility...")
    
    try:
        from sklearn.model_selection import StratifiedKFold
        
        # Split data
        train_sequences, train_labels, test_sequences, test_labels = split_train_test(
            sequences, labels,
            test_ratio=0.1,
            random_state=123
        )
        
        # Test cross-validation on training set
        skf = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
        
        fold_info = []
        for fold_idx, (train_indices, val_indices) in enumerate(skf.split(train_sequences, train_labels)):
            fold_train_labels = [train_labels[i] for i in train_indices]
            fold_val_labels = [train_labels[i] for i in val_indices]
            
            fold_info.append({
                'fold': fold_idx + 1,
                'train_size': len(train_indices),
                'val_size': len(val_indices),
                'train_label_1_ratio': fold_train_labels.count(1) / len(fold_train_labels),
                'val_label_1_ratio': fold_val_labels.count(1) / len(fold_val_labels)
            })
        
        print(f"✅ Cross-validation setup successful:")
        print(f"   Training set for CV: {len(train_sequences)} sequences")
        print(f"   Independent test set: {len(test_sequences)} sequences")
        print(f"   CV folds: {len(fold_info)}")
        
        for fold in fold_info:
            print(f"   Fold {fold['fold']}: {fold['train_size']} train, {fold['val_size']} val")
            print(f"     Label 1 ratio: train={fold['train_label_1_ratio']:.3f}, val={fold['val_label_1_ratio']:.3f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in cross-validation compatibility test: {e}")
        return False

def main():
    """Run all tests."""
    print("🧬 Data Splitting Test for lncRNA Function Prediction")
    print("=" * 60)
    
    all_tests_passed = True
    
    # Test data loading
    success, sequences, labels = test_data_loading()
    if not success:
        all_tests_passed = False
        return
    
    # Test data splitting
    if not test_data_splitting(sequences, labels):
        all_tests_passed = False
    
    # Test reproducibility
    if not test_reproducibility(sequences, labels):
        all_tests_passed = False
    
    # Test cross-validation compatibility
    if not test_cross_validation_compatibility(sequences, labels):
        all_tests_passed = False
    
    print("\n" + "=" * 60)
    if all_tests_passed:
        print("🎉 All data splitting tests passed!")
        print("\n📊 Final Data Split Strategy:")
        print("1. Original dataset: 1134 sequences (567 label=0, 567 label=1)")
        print("2. Independent test set: ~113 sequences (10%)")
        print("3. Cross-validation set: ~1021 sequences (90%)")
        print("4. Each CV fold: ~817 train + ~204 validation")
        print("\n🚀 Ready to run LoRA experiments with independent test set:")
        print("   bash scripts/lncrna_function_cv/run_lora_with_independent_test.sh")
    else:
        print("❌ Some tests failed. Please fix the issues before running experiments.")
    
    print("\n💡 Benefits of this approach:")
    print("- Unbiased performance evaluation on independent test set")
    print("- Cross-validation provides robust model selection")
    print("- Stratified splits maintain label balance")
    print("- Reproducible results with fixed seeds")

if __name__ == "__main__":
    main()
