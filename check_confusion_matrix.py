#!/usr/bin/env python3
"""
Quick check to verify confusion matrix display
"""

import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np

def create_test_confusion_matrix():
    """Create a test confusion matrix to verify display"""
    
    # Test data from our results
    cm_rna_fm = np.array([[271, 14], [6, 279]])
    cm_splicebert = np.array([[274, 11], [2, 283]])
    
    # Calculate percentages
    cm_rna_fm_percent = cm_rna_fm.astype('float') / cm_rna_fm.sum(axis=1)[:, np.newaxis] * 100
    cm_splicebert_percent = cm_splicebert.astype('float') / cm_splicebert.sum(axis=1)[:, np.newaxis] * 100
    
    fig, axes = plt.subplots(1, 2, figsize=(16, 6))
    
    models = ['RNA-FM', 'SpliceBERT-MS1024']
    cms = [cm_rna_fm, cm_splicebert]
    cm_percents = [cm_rna_fm_percent, cm_splicebert_percent]
    
    for idx, (model_name, cm, cm_percent) in enumerate(zip(models, cms, cm_percents)):
        
        # Plot heatmap without default annotations
        sns.heatmap(cm, 
                    annot=False,
                    fmt='d', 
                    cmap='Blues',
                    square=True,
                    linewidths=2,
                    linecolor='white',
                    cbar=True,
                    ax=axes[idx])
        
        # Add custom annotations with both count and percentage
        for i in range(2):
            for j in range(2):
                # Main count number (larger, bold)
                axes[idx].text(j + 0.5, i + 0.35, str(cm[i, j]), 
                              ha='center', va='center', 
                              fontsize=18, fontweight='bold', color='black')
                # Percentage (smaller, below the count)
                axes[idx].text(j + 0.5, i + 0.65, f'({cm_percent[i, j]:.1f}%)', 
                              ha='center', va='center', 
                              fontsize=10, color='gray')
        
        # Customize subplot
        f1_scores = [0.965, 0.977]
        axes[idx].set_title(f'{model_name}\nF1-Score: {f1_scores[idx]:.3f}', 
                           fontsize=14, fontweight='bold')
        axes[idx].set_xlabel('Predicted Label', fontsize=12, fontweight='bold')
        if idx == 0:
            axes[idx].set_ylabel('True Label', fontsize=12, fontweight='bold')
        
        # Set tick labels
        class_names = ['Non-functional', 'Functional']
        axes[idx].set_xticklabels(class_names, fontsize=11)
        axes[idx].set_yticklabels(class_names, fontsize=11, rotation=0)
    
    plt.suptitle('lncRNA Function Prediction - Model Comparison (Test)', 
                 fontsize=16, fontweight='bold', y=1.02)
    plt.tight_layout()
    plt.savefig('test_confusion_matrices.png', dpi=300, bbox_inches='tight')
    print("✅ Test confusion matrix saved: test_confusion_matrices.png")
    
    # Print the matrix values for verification
    print("\n📊 Matrix Values Verification:")
    print("RNA-FM Confusion Matrix:")
    print(f"  TN: {cm_rna_fm[0,0]}, FP: {cm_rna_fm[0,1]}")
    print(f"  FN: {cm_rna_fm[1,0]}, TP: {cm_rna_fm[1,1]}")
    
    print("\nSpliceBERT-MS1024 Confusion Matrix:")
    print(f"  TN: {cm_splicebert[0,0]}, FP: {cm_splicebert[0,1]}")
    print(f"  FN: {cm_splicebert[1,0]}, TP: {cm_splicebert[1,1]}")

if __name__ == "__main__":
    create_test_confusion_matrix()
