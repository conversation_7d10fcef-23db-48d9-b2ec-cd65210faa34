{"added_tokens_decoder": {"0": {"content": "<pad>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "1": {"content": "<cls>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "2": {"content": "<eos>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "3": {"content": "<unk>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "4": {"content": "<mask>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}}, "bos_token": "<cls>", "clean_up_tokenization_spaces": true, "cls_token": "<cls>", "eos_token": "<eos>", "extra_special_tokens": {}, "mask_token": "<mask>", "model_max_length": 1024, "pad_token": "<pad>", "padding_side": "right", "sep_token": "<eos>", "tokenizer_class": "OpenRnaLMTokenizer", "unk_token": "<unk>", "use_fast": true}