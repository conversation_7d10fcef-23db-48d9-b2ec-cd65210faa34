{"best_global_step": 200, "best_metric": 0.0, "best_model_checkpoint": null, "epoch": 1.9801980198019802, "eval_steps": 200, "global_step": 400, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.09900990099009901, "grad_norm": NaN, "learning_rate": 1.2e-05, "loss": 1885.2645, "step": 20}, {"epoch": 0.19801980198019803, "grad_norm": NaN, "learning_rate": 2.4e-05, "loss": 0.0, "step": 40}, {"epoch": 0.297029702970297, "grad_norm": NaN, "learning_rate": 2.9924812030075187e-05, "loss": 0.0, "step": 60}, {"epoch": 0.39603960396039606, "grad_norm": NaN, "learning_rate": 2.9774436090225562e-05, "loss": 0.0, "step": 80}, {"epoch": 0.49504950495049505, "grad_norm": NaN, "learning_rate": 2.9624060150375938e-05, "loss": 0.0, "step": 100}, {"epoch": 0.594059405940594, "grad_norm": NaN, "learning_rate": 2.9473684210526314e-05, "loss": 0.0, "step": 120}, {"epoch": 0.693069306930693, "grad_norm": NaN, "learning_rate": 2.932330827067669e-05, "loss": 0.0, "step": 140}, {"epoch": 0.7920792079207921, "grad_norm": NaN, "learning_rate": 2.917293233082707e-05, "loss": 0.0, "step": 160}, {"epoch": 0.8910891089108911, "grad_norm": NaN, "learning_rate": 2.9022556390977444e-05, "loss": 0.0, "step": 180}, {"epoch": 0.9900990099009901, "grad_norm": NaN, "learning_rate": 2.887218045112782e-05, "loss": 0.0, "step": 200}, {"epoch": 0.9900990099009901, "eval_accuracy": 0.5, "eval_auc": 0.0, "eval_f1": 0.0, "eval_loss": NaN, "eval_precision": 0.0, "eval_recall": 0.0, "eval_runtime": 34.8462, "eval_samples_per_second": 6.026, "eval_steps_per_second": 6.026, "eval_unique_labels": 2, "eval_unique_predictions": 1, "step": 200}, {"epoch": 1.0891089108910892, "grad_norm": NaN, "learning_rate": 2.8721804511278195e-05, "loss": 0.0, "step": 220}, {"epoch": 1.188118811881188, "grad_norm": NaN, "learning_rate": 2.857142857142857e-05, "loss": 0.0, "step": 240}, {"epoch": 1.2871287128712872, "grad_norm": NaN, "learning_rate": 2.8421052631578946e-05, "loss": 0.0, "step": 260}, {"epoch": 1.386138613861386, "grad_norm": NaN, "learning_rate": 2.8270676691729322e-05, "loss": 0.0, "step": 280}, {"epoch": 1.4851485148514851, "grad_norm": NaN, "learning_rate": 2.8120300751879698e-05, "loss": 0.0, "step": 300}, {"epoch": 1.5841584158415842, "grad_norm": NaN, "learning_rate": 2.7969924812030073e-05, "loss": 0.0, "step": 320}, {"epoch": 1.6831683168316833, "grad_norm": NaN, "learning_rate": 2.781954887218045e-05, "loss": 0.0, "step": 340}, {"epoch": 1.7821782178217822, "grad_norm": NaN, "learning_rate": 2.7669172932330825e-05, "loss": 0.0, "step": 360}, {"epoch": 1.881188118811881, "grad_norm": NaN, "learning_rate": 2.7518796992481204e-05, "loss": 0.0, "step": 380}, {"epoch": 1.9801980198019802, "grad_norm": NaN, "learning_rate": 2.736842105263158e-05, "loss": 0.0, "step": 400}, {"epoch": 1.9801980198019802, "eval_accuracy": 0.5, "eval_auc": 0.0, "eval_f1": 0.0, "eval_loss": NaN, "eval_precision": 0.0, "eval_recall": 0.0, "eval_runtime": 34.8699, "eval_samples_per_second": 6.022, "eval_steps_per_second": 6.022, "eval_unique_labels": 2, "eval_unique_predictions": 1, "step": 400}], "logging_steps": 20, "max_steps": 4040, "num_input_tokens_seen": 0, "num_train_epochs": 20, "save_steps": 400, "stateful_callbacks": {"EarlyStoppingCallback": {"args": {"early_stopping_patience": 10, "early_stopping_threshold": 0.001}, "attributes": {"early_stopping_patience_counter": 1}}, "TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 0.0, "train_batch_size": 1, "trial_name": null, "trial_params": null}