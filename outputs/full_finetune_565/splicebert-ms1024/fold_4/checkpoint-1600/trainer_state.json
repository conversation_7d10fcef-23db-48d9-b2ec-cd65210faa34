{"best_global_step": 200, "best_metric": 0.0, "best_model_checkpoint": null, "epoch": 7.920792079207921, "eval_steps": 200, "global_step": 1600, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.09900990099009901, "grad_norm": NaN, "learning_rate": 1.2e-05, "loss": 1885.2645, "step": 20}, {"epoch": 0.19801980198019803, "grad_norm": NaN, "learning_rate": 2.4e-05, "loss": 0.0, "step": 40}, {"epoch": 0.297029702970297, "grad_norm": NaN, "learning_rate": 2.9924812030075187e-05, "loss": 0.0, "step": 60}, {"epoch": 0.39603960396039606, "grad_norm": NaN, "learning_rate": 2.9774436090225562e-05, "loss": 0.0, "step": 80}, {"epoch": 0.49504950495049505, "grad_norm": NaN, "learning_rate": 2.9624060150375938e-05, "loss": 0.0, "step": 100}, {"epoch": 0.594059405940594, "grad_norm": NaN, "learning_rate": 2.9473684210526314e-05, "loss": 0.0, "step": 120}, {"epoch": 0.693069306930693, "grad_norm": NaN, "learning_rate": 2.932330827067669e-05, "loss": 0.0, "step": 140}, {"epoch": 0.7920792079207921, "grad_norm": NaN, "learning_rate": 2.917293233082707e-05, "loss": 0.0, "step": 160}, {"epoch": 0.8910891089108911, "grad_norm": NaN, "learning_rate": 2.9022556390977444e-05, "loss": 0.0, "step": 180}, {"epoch": 0.9900990099009901, "grad_norm": NaN, "learning_rate": 2.887218045112782e-05, "loss": 0.0, "step": 200}, {"epoch": 0.9900990099009901, "eval_accuracy": 0.5, "eval_auc": 0.0, "eval_f1": 0.0, "eval_loss": NaN, "eval_precision": 0.0, "eval_recall": 0.0, "eval_runtime": 34.8462, "eval_samples_per_second": 6.026, "eval_steps_per_second": 6.026, "eval_unique_labels": 2, "eval_unique_predictions": 1, "step": 200}, {"epoch": 1.0891089108910892, "grad_norm": NaN, "learning_rate": 2.8721804511278195e-05, "loss": 0.0, "step": 220}, {"epoch": 1.188118811881188, "grad_norm": NaN, "learning_rate": 2.857142857142857e-05, "loss": 0.0, "step": 240}, {"epoch": 1.2871287128712872, "grad_norm": NaN, "learning_rate": 2.8421052631578946e-05, "loss": 0.0, "step": 260}, {"epoch": 1.386138613861386, "grad_norm": NaN, "learning_rate": 2.8270676691729322e-05, "loss": 0.0, "step": 280}, {"epoch": 1.4851485148514851, "grad_norm": NaN, "learning_rate": 2.8120300751879698e-05, "loss": 0.0, "step": 300}, {"epoch": 1.5841584158415842, "grad_norm": NaN, "learning_rate": 2.7969924812030073e-05, "loss": 0.0, "step": 320}, {"epoch": 1.6831683168316833, "grad_norm": NaN, "learning_rate": 2.781954887218045e-05, "loss": 0.0, "step": 340}, {"epoch": 1.7821782178217822, "grad_norm": NaN, "learning_rate": 2.7669172932330825e-05, "loss": 0.0, "step": 360}, {"epoch": 1.881188118811881, "grad_norm": NaN, "learning_rate": 2.7518796992481204e-05, "loss": 0.0, "step": 380}, {"epoch": 1.9801980198019802, "grad_norm": NaN, "learning_rate": 2.736842105263158e-05, "loss": 0.0, "step": 400}, {"epoch": 1.9801980198019802, "eval_accuracy": 0.5, "eval_auc": 0.0, "eval_f1": 0.0, "eval_loss": NaN, "eval_precision": 0.0, "eval_recall": 0.0, "eval_runtime": 34.8699, "eval_samples_per_second": 6.022, "eval_steps_per_second": 6.022, "eval_unique_labels": 2, "eval_unique_predictions": 1, "step": 400}, {"epoch": 2.0792079207920793, "grad_norm": NaN, "learning_rate": 2.7218045112781955e-05, "loss": 0.0, "step": 420}, {"epoch": 2.1782178217821784, "grad_norm": NaN, "learning_rate": 2.706766917293233e-05, "loss": 0.0, "step": 440}, {"epoch": 2.2772277227722775, "grad_norm": NaN, "learning_rate": 2.6917293233082706e-05, "loss": 0.0, "step": 460}, {"epoch": 2.376237623762376, "grad_norm": NaN, "learning_rate": 2.6766917293233082e-05, "loss": 0.0, "step": 480}, {"epoch": 2.4752475247524752, "grad_norm": NaN, "learning_rate": 2.6616541353383457e-05, "loss": 0.0, "step": 500}, {"epoch": 2.5742574257425743, "grad_norm": NaN, "learning_rate": 2.6466165413533833e-05, "loss": 0.0, "step": 520}, {"epoch": 2.6732673267326734, "grad_norm": NaN, "learning_rate": 2.631578947368421e-05, "loss": 0.0, "step": 540}, {"epoch": 2.772277227722772, "grad_norm": NaN, "learning_rate": 2.6165413533834584e-05, "loss": 0.0, "step": 560}, {"epoch": 2.871287128712871, "grad_norm": NaN, "learning_rate": 2.6015037593984963e-05, "loss": 0.0, "step": 580}, {"epoch": 2.9702970297029703, "grad_norm": NaN, "learning_rate": 2.586466165413534e-05, "loss": 0.0, "step": 600}, {"epoch": 2.9702970297029703, "eval_accuracy": 0.5, "eval_auc": 0.0, "eval_f1": 0.0, "eval_loss": NaN, "eval_precision": 0.0, "eval_recall": 0.0, "eval_runtime": 34.8458, "eval_samples_per_second": 6.027, "eval_steps_per_second": 6.027, "eval_unique_labels": 2, "eval_unique_predictions": 1, "step": 600}, {"epoch": 3.0693069306930694, "grad_norm": NaN, "learning_rate": 2.5714285714285714e-05, "loss": 0.0, "step": 620}, {"epoch": 3.1683168316831685, "grad_norm": NaN, "learning_rate": 2.556390977443609e-05, "loss": 0.0, "step": 640}, {"epoch": 3.2673267326732676, "grad_norm": NaN, "learning_rate": 2.5413533834586466e-05, "loss": 0.0, "step": 660}, {"epoch": 3.366336633663366, "grad_norm": NaN, "learning_rate": 2.526315789473684e-05, "loss": 0.0, "step": 680}, {"epoch": 3.4653465346534653, "grad_norm": NaN, "learning_rate": 2.5112781954887217e-05, "loss": 0.0, "step": 700}, {"epoch": 3.5643564356435644, "grad_norm": NaN, "learning_rate": 2.4962406015037593e-05, "loss": 0.0, "step": 720}, {"epoch": 3.6633663366336635, "grad_norm": NaN, "learning_rate": 2.4812030075187968e-05, "loss": 0.0, "step": 740}, {"epoch": 3.762376237623762, "grad_norm": NaN, "learning_rate": 2.4661654135338344e-05, "loss": 0.0, "step": 760}, {"epoch": 3.8613861386138613, "grad_norm": NaN, "learning_rate": 2.451127819548872e-05, "loss": 0.0, "step": 780}, {"epoch": 3.9603960396039604, "grad_norm": NaN, "learning_rate": 2.43609022556391e-05, "loss": 0.0, "step": 800}, {"epoch": 3.9603960396039604, "eval_accuracy": 0.5, "eval_auc": 0.0, "eval_f1": 0.0, "eval_loss": NaN, "eval_precision": 0.0, "eval_recall": 0.0, "eval_runtime": 34.9799, "eval_samples_per_second": 6.003, "eval_steps_per_second": 6.003, "eval_unique_labels": 2, "eval_unique_predictions": 1, "step": 800}, {"epoch": 4.0594059405940595, "grad_norm": NaN, "learning_rate": 2.4210526315789474e-05, "loss": 0.0, "step": 820}, {"epoch": 4.158415841584159, "grad_norm": NaN, "learning_rate": 2.406015037593985e-05, "loss": 0.0, "step": 840}, {"epoch": 4.257425742574258, "grad_norm": NaN, "learning_rate": 2.3909774436090225e-05, "loss": 0.0, "step": 860}, {"epoch": 4.356435643564357, "grad_norm": NaN, "learning_rate": 2.37593984962406e-05, "loss": 0.0, "step": 880}, {"epoch": 4.455445544554456, "grad_norm": NaN, "learning_rate": 2.3609022556390977e-05, "loss": 0.0, "step": 900}, {"epoch": 4.554455445544555, "grad_norm": NaN, "learning_rate": 2.3458646616541352e-05, "loss": 0.0, "step": 920}, {"epoch": 4.653465346534653, "grad_norm": NaN, "learning_rate": 2.3308270676691728e-05, "loss": 0.0, "step": 940}, {"epoch": 4.752475247524752, "grad_norm": NaN, "learning_rate": 2.3157894736842103e-05, "loss": 0.0, "step": 960}, {"epoch": 4.851485148514851, "grad_norm": NaN, "learning_rate": 2.300751879699248e-05, "loss": 0.0, "step": 980}, {"epoch": 4.9504950495049505, "grad_norm": NaN, "learning_rate": 2.2857142857142858e-05, "loss": 0.0, "step": 1000}, {"epoch": 4.9504950495049505, "eval_accuracy": 0.5, "eval_auc": 0.0, "eval_f1": 0.0, "eval_loss": NaN, "eval_precision": 0.0, "eval_recall": 0.0, "eval_runtime": 34.8065, "eval_samples_per_second": 6.033, "eval_steps_per_second": 6.033, "eval_unique_labels": 2, "eval_unique_predictions": 1, "step": 1000}, {"epoch": 5.0495049504950495, "grad_norm": NaN, "learning_rate": 2.2706766917293234e-05, "loss": 0.0, "step": 1020}, {"epoch": 5.148514851485149, "grad_norm": NaN, "learning_rate": 2.255639097744361e-05, "loss": 0.0, "step": 1040}, {"epoch": 5.247524752475248, "grad_norm": NaN, "learning_rate": 2.2406015037593985e-05, "loss": 0.0, "step": 1060}, {"epoch": 5.346534653465347, "grad_norm": NaN, "learning_rate": 2.225563909774436e-05, "loss": 0.0, "step": 1080}, {"epoch": 5.445544554455446, "grad_norm": NaN, "learning_rate": 2.2105263157894736e-05, "loss": 0.0, "step": 1100}, {"epoch": 5.544554455445544, "grad_norm": NaN, "learning_rate": 2.1954887218045112e-05, "loss": 0.0, "step": 1120}, {"epoch": 5.643564356435643, "grad_norm": NaN, "learning_rate": 2.1804511278195487e-05, "loss": 0.0, "step": 1140}, {"epoch": 5.742574257425742, "grad_norm": NaN, "learning_rate": 2.1654135338345863e-05, "loss": 0.0, "step": 1160}, {"epoch": 5.841584158415841, "grad_norm": NaN, "learning_rate": 2.150375939849624e-05, "loss": 0.0, "step": 1180}, {"epoch": 5.9405940594059405, "grad_norm": NaN, "learning_rate": 2.1353383458646614e-05, "loss": 0.0, "step": 1200}, {"epoch": 5.9405940594059405, "eval_accuracy": 0.5, "eval_auc": 0.0, "eval_f1": 0.0, "eval_loss": NaN, "eval_precision": 0.0, "eval_recall": 0.0, "eval_runtime": 34.805, "eval_samples_per_second": 6.034, "eval_steps_per_second": 6.034, "eval_unique_labels": 2, "eval_unique_predictions": 1, "step": 1200}, {"epoch": 6.03960396039604, "grad_norm": NaN, "learning_rate": 2.1203007518796993e-05, "loss": 0.0, "step": 1220}, {"epoch": 6.138613861386139, "grad_norm": NaN, "learning_rate": 2.105263157894737e-05, "loss": 0.0, "step": 1240}, {"epoch": 6.237623762376238, "grad_norm": NaN, "learning_rate": 2.0902255639097745e-05, "loss": 0.0, "step": 1260}, {"epoch": 6.336633663366337, "grad_norm": NaN, "learning_rate": 2.075187969924812e-05, "loss": 0.0, "step": 1280}, {"epoch": 6.435643564356436, "grad_norm": NaN, "learning_rate": 2.0601503759398496e-05, "loss": 0.0, "step": 1300}, {"epoch": 6.534653465346535, "grad_norm": NaN, "learning_rate": 2.045112781954887e-05, "loss": 0.0, "step": 1320}, {"epoch": 6.633663366336633, "grad_norm": NaN, "learning_rate": 2.0300751879699247e-05, "loss": 0.0, "step": 1340}, {"epoch": 6.732673267326732, "grad_norm": NaN, "learning_rate": 2.0150375939849623e-05, "loss": 0.0, "step": 1360}, {"epoch": 6.8316831683168315, "grad_norm": NaN, "learning_rate": 1.9999999999999998e-05, "loss": 0.0, "step": 1380}, {"epoch": 6.930693069306931, "grad_norm": NaN, "learning_rate": 1.9849624060150374e-05, "loss": 0.0, "step": 1400}, {"epoch": 6.930693069306931, "eval_accuracy": 0.5, "eval_auc": 0.0, "eval_f1": 0.0, "eval_loss": NaN, "eval_precision": 0.0, "eval_recall": 0.0, "eval_runtime": 35.0622, "eval_samples_per_second": 5.989, "eval_steps_per_second": 5.989, "eval_unique_labels": 2, "eval_unique_predictions": 1, "step": 1400}, {"epoch": 7.02970297029703, "grad_norm": NaN, "learning_rate": 1.9699248120300753e-05, "loss": 0.0, "step": 1420}, {"epoch": 7.128712871287129, "grad_norm": NaN, "learning_rate": 1.954887218045113e-05, "loss": 0.0, "step": 1440}, {"epoch": 7.227722772277228, "grad_norm": NaN, "learning_rate": 1.9398496240601504e-05, "loss": 0.0, "step": 1460}, {"epoch": 7.326732673267327, "grad_norm": NaN, "learning_rate": 1.924812030075188e-05, "loss": 0.0, "step": 1480}, {"epoch": 7.425742574257426, "grad_norm": NaN, "learning_rate": 1.9097744360902255e-05, "loss": 0.0, "step": 1500}, {"epoch": 7.524752475247524, "grad_norm": NaN, "learning_rate": 1.894736842105263e-05, "loss": 0.0, "step": 1520}, {"epoch": 7.623762376237623, "grad_norm": NaN, "learning_rate": 1.8796992481203007e-05, "loss": 0.0, "step": 1540}, {"epoch": 7.7227722772277225, "grad_norm": NaN, "learning_rate": 1.8646616541353382e-05, "loss": 0.0, "step": 1560}, {"epoch": 7.821782178217822, "grad_norm": NaN, "learning_rate": 1.8496240601503758e-05, "loss": 0.0, "step": 1580}, {"epoch": 7.920792079207921, "grad_norm": NaN, "learning_rate": 1.8345864661654133e-05, "loss": 0.0, "step": 1600}, {"epoch": 7.920792079207921, "eval_accuracy": 0.5, "eval_auc": 0.0, "eval_f1": 0.0, "eval_loss": NaN, "eval_precision": 0.0, "eval_recall": 0.0, "eval_runtime": 34.8922, "eval_samples_per_second": 6.019, "eval_steps_per_second": 6.019, "eval_unique_labels": 2, "eval_unique_predictions": 1, "step": 1600}], "logging_steps": 20, "max_steps": 4040, "num_input_tokens_seen": 0, "num_train_epochs": 20, "save_steps": 400, "stateful_callbacks": {"EarlyStoppingCallback": {"args": {"early_stopping_patience": 10, "early_stopping_threshold": 0.001}, "attributes": {"early_stopping_patience_counter": 7}}, "TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 0.0, "train_batch_size": 1, "trial_name": null, "trial_params": null}