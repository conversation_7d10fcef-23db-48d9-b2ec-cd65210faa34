{"best_global_step": 200, "best_metric": 0.0, "best_model_checkpoint": null, "epoch": 3.9603960396039604, "eval_steps": 200, "global_step": 800, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.09900990099009901, "grad_norm": NaN, "learning_rate": 1.2e-05, "loss": 1885.2645, "step": 20}, {"epoch": 0.19801980198019803, "grad_norm": NaN, "learning_rate": 2.4e-05, "loss": 0.0, "step": 40}, {"epoch": 0.297029702970297, "grad_norm": NaN, "learning_rate": 2.9924812030075187e-05, "loss": 0.0, "step": 60}, {"epoch": 0.39603960396039606, "grad_norm": NaN, "learning_rate": 2.9774436090225562e-05, "loss": 0.0, "step": 80}, {"epoch": 0.49504950495049505, "grad_norm": NaN, "learning_rate": 2.9624060150375938e-05, "loss": 0.0, "step": 100}, {"epoch": 0.594059405940594, "grad_norm": NaN, "learning_rate": 2.9473684210526314e-05, "loss": 0.0, "step": 120}, {"epoch": 0.693069306930693, "grad_norm": NaN, "learning_rate": 2.932330827067669e-05, "loss": 0.0, "step": 140}, {"epoch": 0.7920792079207921, "grad_norm": NaN, "learning_rate": 2.917293233082707e-05, "loss": 0.0, "step": 160}, {"epoch": 0.8910891089108911, "grad_norm": NaN, "learning_rate": 2.9022556390977444e-05, "loss": 0.0, "step": 180}, {"epoch": 0.9900990099009901, "grad_norm": NaN, "learning_rate": 2.887218045112782e-05, "loss": 0.0, "step": 200}, {"epoch": 0.9900990099009901, "eval_accuracy": 0.5, "eval_auc": 0.0, "eval_f1": 0.0, "eval_loss": NaN, "eval_precision": 0.0, "eval_recall": 0.0, "eval_runtime": 34.8462, "eval_samples_per_second": 6.026, "eval_steps_per_second": 6.026, "eval_unique_labels": 2, "eval_unique_predictions": 1, "step": 200}, {"epoch": 1.0891089108910892, "grad_norm": NaN, "learning_rate": 2.8721804511278195e-05, "loss": 0.0, "step": 220}, {"epoch": 1.188118811881188, "grad_norm": NaN, "learning_rate": 2.857142857142857e-05, "loss": 0.0, "step": 240}, {"epoch": 1.2871287128712872, "grad_norm": NaN, "learning_rate": 2.8421052631578946e-05, "loss": 0.0, "step": 260}, {"epoch": 1.386138613861386, "grad_norm": NaN, "learning_rate": 2.8270676691729322e-05, "loss": 0.0, "step": 280}, {"epoch": 1.4851485148514851, "grad_norm": NaN, "learning_rate": 2.8120300751879698e-05, "loss": 0.0, "step": 300}, {"epoch": 1.5841584158415842, "grad_norm": NaN, "learning_rate": 2.7969924812030073e-05, "loss": 0.0, "step": 320}, {"epoch": 1.6831683168316833, "grad_norm": NaN, "learning_rate": 2.781954887218045e-05, "loss": 0.0, "step": 340}, {"epoch": 1.7821782178217822, "grad_norm": NaN, "learning_rate": 2.7669172932330825e-05, "loss": 0.0, "step": 360}, {"epoch": 1.881188118811881, "grad_norm": NaN, "learning_rate": 2.7518796992481204e-05, "loss": 0.0, "step": 380}, {"epoch": 1.9801980198019802, "grad_norm": NaN, "learning_rate": 2.736842105263158e-05, "loss": 0.0, "step": 400}, {"epoch": 1.9801980198019802, "eval_accuracy": 0.5, "eval_auc": 0.0, "eval_f1": 0.0, "eval_loss": NaN, "eval_precision": 0.0, "eval_recall": 0.0, "eval_runtime": 34.8699, "eval_samples_per_second": 6.022, "eval_steps_per_second": 6.022, "eval_unique_labels": 2, "eval_unique_predictions": 1, "step": 400}, {"epoch": 2.0792079207920793, "grad_norm": NaN, "learning_rate": 2.7218045112781955e-05, "loss": 0.0, "step": 420}, {"epoch": 2.1782178217821784, "grad_norm": NaN, "learning_rate": 2.706766917293233e-05, "loss": 0.0, "step": 440}, {"epoch": 2.2772277227722775, "grad_norm": NaN, "learning_rate": 2.6917293233082706e-05, "loss": 0.0, "step": 460}, {"epoch": 2.376237623762376, "grad_norm": NaN, "learning_rate": 2.6766917293233082e-05, "loss": 0.0, "step": 480}, {"epoch": 2.4752475247524752, "grad_norm": NaN, "learning_rate": 2.6616541353383457e-05, "loss": 0.0, "step": 500}, {"epoch": 2.5742574257425743, "grad_norm": NaN, "learning_rate": 2.6466165413533833e-05, "loss": 0.0, "step": 520}, {"epoch": 2.6732673267326734, "grad_norm": NaN, "learning_rate": 2.631578947368421e-05, "loss": 0.0, "step": 540}, {"epoch": 2.772277227722772, "grad_norm": NaN, "learning_rate": 2.6165413533834584e-05, "loss": 0.0, "step": 560}, {"epoch": 2.871287128712871, "grad_norm": NaN, "learning_rate": 2.6015037593984963e-05, "loss": 0.0, "step": 580}, {"epoch": 2.9702970297029703, "grad_norm": NaN, "learning_rate": 2.586466165413534e-05, "loss": 0.0, "step": 600}, {"epoch": 2.9702970297029703, "eval_accuracy": 0.5, "eval_auc": 0.0, "eval_f1": 0.0, "eval_loss": NaN, "eval_precision": 0.0, "eval_recall": 0.0, "eval_runtime": 34.8458, "eval_samples_per_second": 6.027, "eval_steps_per_second": 6.027, "eval_unique_labels": 2, "eval_unique_predictions": 1, "step": 600}, {"epoch": 3.0693069306930694, "grad_norm": NaN, "learning_rate": 2.5714285714285714e-05, "loss": 0.0, "step": 620}, {"epoch": 3.1683168316831685, "grad_norm": NaN, "learning_rate": 2.556390977443609e-05, "loss": 0.0, "step": 640}, {"epoch": 3.2673267326732676, "grad_norm": NaN, "learning_rate": 2.5413533834586466e-05, "loss": 0.0, "step": 660}, {"epoch": 3.366336633663366, "grad_norm": NaN, "learning_rate": 2.526315789473684e-05, "loss": 0.0, "step": 680}, {"epoch": 3.4653465346534653, "grad_norm": NaN, "learning_rate": 2.5112781954887217e-05, "loss": 0.0, "step": 700}, {"epoch": 3.5643564356435644, "grad_norm": NaN, "learning_rate": 2.4962406015037593e-05, "loss": 0.0, "step": 720}, {"epoch": 3.6633663366336635, "grad_norm": NaN, "learning_rate": 2.4812030075187968e-05, "loss": 0.0, "step": 740}, {"epoch": 3.762376237623762, "grad_norm": NaN, "learning_rate": 2.4661654135338344e-05, "loss": 0.0, "step": 760}, {"epoch": 3.8613861386138613, "grad_norm": NaN, "learning_rate": 2.451127819548872e-05, "loss": 0.0, "step": 780}, {"epoch": 3.9603960396039604, "grad_norm": NaN, "learning_rate": 2.43609022556391e-05, "loss": 0.0, "step": 800}, {"epoch": 3.9603960396039604, "eval_accuracy": 0.5, "eval_auc": 0.0, "eval_f1": 0.0, "eval_loss": NaN, "eval_precision": 0.0, "eval_recall": 0.0, "eval_runtime": 34.9799, "eval_samples_per_second": 6.003, "eval_steps_per_second": 6.003, "eval_unique_labels": 2, "eval_unique_predictions": 1, "step": 800}], "logging_steps": 20, "max_steps": 4040, "num_input_tokens_seen": 0, "num_train_epochs": 20, "save_steps": 400, "stateful_callbacks": {"EarlyStoppingCallback": {"args": {"early_stopping_patience": 10, "early_stopping_threshold": 0.001}, "attributes": {"early_stopping_patience_counter": 3}}, "TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 0.0, "train_batch_size": 1, "trial_name": null, "trial_params": null}