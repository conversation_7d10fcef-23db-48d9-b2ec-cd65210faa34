{"best_global_step": 100, "best_metric": 0.6666666666666666, "best_model_checkpoint": null, "epoch": 2.450980392156863, "eval_steps": 100, "global_step": 500, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.24509803921568626, "grad_norm": NaN, "learning_rate": 0.0, "loss": 3.2784, "step": 50}, {"epoch": 0.49019607843137253, "grad_norm": NaN, "learning_rate": 0.0, "loss": 3.212, "step": 100}, {"epoch": 0.49019607843137253, "eval_accuracy": 0.5, "eval_auc": 0.4553965297519851, "eval_f1": 0.6666666666666666, "eval_loss": 0.7705199122428894, "eval_precision": 0.5, "eval_recall": 1.0, "eval_runtime": 29.649, "eval_samples_per_second": 6.813, "eval_steps_per_second": 6.813, "eval_unique_labels": 2, "eval_unique_predictions": 1, "step": 100}, {"epoch": 0.7352941176470589, "grad_norm": NaN, "learning_rate": 0.0, "loss": 3.1788, "step": 150}, {"epoch": 0.9803921568627451, "grad_norm": NaN, "learning_rate": 0.0, "loss": 3.0401, "step": 200}, {"epoch": 0.9803921568627451, "eval_accuracy": 0.5, "eval_auc": 0.4553965297519851, "eval_f1": 0.6666666666666666, "eval_loss": 0.7705199122428894, "eval_precision": 0.5, "eval_recall": 1.0, "eval_runtime": 29.6406, "eval_samples_per_second": 6.815, "eval_steps_per_second": 6.815, "eval_unique_labels": 2, "eval_unique_predictions": 1, "step": 200}, {"epoch": 1.2254901960784315, "grad_norm": NaN, "learning_rate": 0.0, "loss": 3.3821, "step": 250}, {"epoch": 1.4705882352941178, "grad_norm": NaN, "learning_rate": 0.0, "loss": 3.1487, "step": 300}, {"epoch": 1.4705882352941178, "eval_accuracy": 0.5, "eval_auc": 0.4553965297519851, "eval_f1": 0.6666666666666666, "eval_loss": 0.7705199122428894, "eval_precision": 0.5, "eval_recall": 1.0, "eval_runtime": 29.7014, "eval_samples_per_second": 6.801, "eval_steps_per_second": 6.801, "eval_unique_labels": 2, "eval_unique_predictions": 1, "step": 300}, {"epoch": 1.715686274509804, "grad_norm": NaN, "learning_rate": 0.0, "loss": 3.1288, "step": 350}, {"epoch": 1.9607843137254903, "grad_norm": NaN, "learning_rate": 0.0, "loss": 3.2017, "step": 400}, {"epoch": 1.9607843137254903, "eval_accuracy": 0.5, "eval_auc": 0.4553965297519851, "eval_f1": 0.6666666666666666, "eval_loss": 0.7705199122428894, "eval_precision": 0.5, "eval_recall": 1.0, "eval_runtime": 29.5754, "eval_samples_per_second": 6.83, "eval_steps_per_second": 6.83, "eval_unique_labels": 2, "eval_unique_predictions": 1, "step": 400}, {"epoch": 2.2058823529411766, "grad_norm": NaN, "learning_rate": 0.0, "loss": 3.264, "step": 450}, {"epoch": 2.450980392156863, "grad_norm": NaN, "learning_rate": 0.0, "loss": 3.4436, "step": 500}, {"epoch": 2.450980392156863, "eval_accuracy": 0.5, "eval_auc": 0.4553965297519851, "eval_f1": 0.6666666666666666, "eval_loss": 0.7705199122428894, "eval_precision": 0.5, "eval_recall": 1.0, "eval_runtime": 29.8639, "eval_samples_per_second": 6.764, "eval_steps_per_second": 6.764, "eval_unique_labels": 2, "eval_unique_predictions": 1, "step": 500}], "logging_steps": 50, "max_steps": 4080, "num_input_tokens_seen": 0, "num_train_epochs": 20, "save_steps": 500, "stateful_callbacks": {"EarlyStoppingCallback": {"args": {"early_stopping_patience": 10, "early_stopping_threshold": 0.001}, "attributes": {"early_stopping_patience_counter": 4}}, "TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 0.0, "train_batch_size": 1, "trial_name": null, "trial_params": null}