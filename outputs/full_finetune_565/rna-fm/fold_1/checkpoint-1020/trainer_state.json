{"best_global_step": 100, "best_metric": 0.6666666666666666, "best_model_checkpoint": null, "epoch": 20.0, "eval_steps": 100, "global_step": 1020, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.9803921568627451, "grad_norm": NaN, "learning_rate": 0.0, "loss": 12.7093, "step": 50}, {"epoch": 1.9607843137254903, "grad_norm": NaN, "learning_rate": 0.0, "loss": 12.8614, "step": 100}, {"epoch": 1.9607843137254903, "eval_accuracy": 0.5, "eval_auc": 0.4553965297519851, "eval_f1": 0.6666666666666666, "eval_loss": 0.7705199122428894, "eval_precision": 0.5, "eval_recall": 1.0, "eval_runtime": 29.684, "eval_samples_per_second": 6.805, "eval_steps_per_second": 6.805, "step": 100}, {"epoch": 2.9411764705882355, "grad_norm": NaN, "learning_rate": 0.0, "loss": 13.0952, "step": 150}, {"epoch": 3.9215686274509802, "grad_norm": NaN, "learning_rate": 0.0, "loss": 12.7291, "step": 200}, {"epoch": 3.9215686274509802, "eval_accuracy": 0.5, "eval_auc": 0.4553965297519851, "eval_f1": 0.6666666666666666, "eval_loss": 0.7705199122428894, "eval_precision": 0.5, "eval_recall": 1.0, "eval_runtime": 29.7392, "eval_samples_per_second": 6.792, "eval_steps_per_second": 6.792, "step": 200}, {"epoch": 4.901960784313726, "grad_norm": NaN, "learning_rate": 0.0, "loss": 12.697, "step": 250}, {"epoch": 5.882352941176471, "grad_norm": NaN, "learning_rate": 0.0, "loss": 12.6693, "step": 300}, {"epoch": 5.882352941176471, "eval_accuracy": 0.5, "eval_auc": 0.4553965297519851, "eval_f1": 0.6666666666666666, "eval_loss": 0.7705199122428894, "eval_precision": 0.5, "eval_recall": 1.0, "eval_runtime": 29.6766, "eval_samples_per_second": 6.807, "eval_steps_per_second": 6.807, "step": 300}, {"epoch": 6.862745098039216, "grad_norm": NaN, "learning_rate": 0.0, "loss": 12.9603, "step": 350}, {"epoch": 7.8431372549019605, "grad_norm": NaN, "learning_rate": 0.0, "loss": 12.7844, "step": 400}, {"epoch": 7.8431372549019605, "eval_accuracy": 0.5, "eval_auc": 0.4553965297519851, "eval_f1": 0.6666666666666666, "eval_loss": 0.7705199122428894, "eval_precision": 0.5, "eval_recall": 1.0, "eval_runtime": 29.6559, "eval_samples_per_second": 6.811, "eval_steps_per_second": 6.811, "step": 400}, {"epoch": 8.823529411764707, "grad_norm": NaN, "learning_rate": 0.0, "loss": 12.7844, "step": 450}, {"epoch": 9.803921568627452, "grad_norm": NaN, "learning_rate": 0.0, "loss": 12.9758, "step": 500}, {"epoch": 9.803921568627452, "eval_accuracy": 0.5, "eval_auc": 0.4553965297519851, "eval_f1": 0.6666666666666666, "eval_loss": 0.7705199122428894, "eval_precision": 0.5, "eval_recall": 1.0, "eval_runtime": 29.7971, "eval_samples_per_second": 6.779, "eval_steps_per_second": 6.779, "step": 500}, {"epoch": 10.784313725490197, "grad_norm": NaN, "learning_rate": 0.0, "loss": 12.9715, "step": 550}, {"epoch": 11.764705882352942, "grad_norm": NaN, "learning_rate": 0.0, "loss": 12.6061, "step": 600}, {"epoch": 11.764705882352942, "eval_accuracy": 0.5, "eval_auc": 0.4553965297519851, "eval_f1": 0.6666666666666666, "eval_loss": 0.7705199122428894, "eval_precision": 0.5, "eval_recall": 1.0, "eval_runtime": 29.6887, "eval_samples_per_second": 6.804, "eval_steps_per_second": 6.804, "step": 600}, {"epoch": 12.745098039215687, "grad_norm": NaN, "learning_rate": 0.0, "loss": 12.9623, "step": 650}, {"epoch": 13.72549019607843, "grad_norm": NaN, "learning_rate": 0.0, "loss": 12.9899, "step": 700}, {"epoch": 13.72549019607843, "eval_accuracy": 0.5, "eval_auc": 0.4553965297519851, "eval_f1": 0.6666666666666666, "eval_loss": 0.7705199122428894, "eval_precision": 0.5, "eval_recall": 1.0, "eval_runtime": 29.76, "eval_samples_per_second": 6.788, "eval_steps_per_second": 6.788, "step": 700}, {"epoch": 14.705882352941176, "grad_norm": NaN, "learning_rate": 0.0, "loss": 12.8023, "step": 750}, {"epoch": 15.686274509803921, "grad_norm": NaN, "learning_rate": 0.0, "loss": 12.5896, "step": 800}, {"epoch": 15.686274509803921, "eval_accuracy": 0.5, "eval_auc": 0.4553965297519851, "eval_f1": 0.6666666666666666, "eval_loss": 0.7705199122428894, "eval_precision": 0.5, "eval_recall": 1.0, "eval_runtime": 29.7946, "eval_samples_per_second": 6.78, "eval_steps_per_second": 6.78, "step": 800}, {"epoch": 16.666666666666668, "grad_norm": NaN, "learning_rate": 0.0, "loss": 13.1993, "step": 850}, {"epoch": 17.647058823529413, "grad_norm": NaN, "learning_rate": 0.0, "loss": 12.7335, "step": 900}, {"epoch": 17.647058823529413, "eval_accuracy": 0.5, "eval_auc": 0.4553965297519851, "eval_f1": 0.6666666666666666, "eval_loss": 0.7705199122428894, "eval_precision": 0.5, "eval_recall": 1.0, "eval_runtime": 29.7418, "eval_samples_per_second": 6.792, "eval_steps_per_second": 6.792, "step": 900}, {"epoch": 18.627450980392158, "grad_norm": NaN, "learning_rate": 0.0, "loss": 12.7269, "step": 950}, {"epoch": 19.607843137254903, "grad_norm": NaN, "learning_rate": 0.0, "loss": 13.034, "step": 1000}, {"epoch": 19.607843137254903, "eval_accuracy": 0.5, "eval_auc": 0.4553965297519851, "eval_f1": 0.6666666666666666, "eval_loss": 0.7705199122428894, "eval_precision": 0.5, "eval_recall": 1.0, "eval_runtime": 29.9711, "eval_samples_per_second": 6.74, "eval_steps_per_second": 6.74, "step": 1000}], "logging_steps": 50, "max_steps": 1020, "num_input_tokens_seen": 0, "num_train_epochs": 20, "save_steps": 500, "stateful_callbacks": {"EarlyStoppingCallback": {"args": {"early_stopping_patience": 10, "early_stopping_threshold": 0.001}, "attributes": {"early_stopping_patience_counter": 9}}, "TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": true}, "attributes": {}}}, "total_flos": 0.0, "train_batch_size": 1, "trial_name": null, "trial_params": null}