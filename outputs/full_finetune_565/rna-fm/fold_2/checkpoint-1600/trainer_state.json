{"best_global_step": 200, "best_metric": 0.0, "best_model_checkpoint": null, "epoch": 7.8431372549019605, "eval_steps": 200, "global_step": 1600, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.09803921568627451, "grad_norm": NaN, "learning_rate": 1.2e-05, "loss": 910.3544, "step": 20}, {"epoch": 0.19607843137254902, "grad_norm": NaN, "learning_rate": 2.4e-05, "loss": 0.0, "step": 40}, {"epoch": 0.29411764705882354, "grad_norm": NaN, "learning_rate": 2.9925558312655086e-05, "loss": 0.0, "step": 60}, {"epoch": 0.39215686274509803, "grad_norm": NaN, "learning_rate": 2.9776674937965263e-05, "loss": 0.0, "step": 80}, {"epoch": 0.49019607843137253, "grad_norm": NaN, "learning_rate": 2.9627791563275436e-05, "loss": 0.0, "step": 100}, {"epoch": 0.5882352941176471, "grad_norm": NaN, "learning_rate": 2.9478908188585606e-05, "loss": 0.0, "step": 120}, {"epoch": 0.6862745098039216, "grad_norm": NaN, "learning_rate": 2.9330024813895783e-05, "loss": 0.0, "step": 140}, {"epoch": 0.7843137254901961, "grad_norm": NaN, "learning_rate": 2.9181141439205956e-05, "loss": 0.0, "step": 160}, {"epoch": 0.8823529411764706, "grad_norm": NaN, "learning_rate": 2.903225806451613e-05, "loss": 0.0, "step": 180}, {"epoch": 0.9803921568627451, "grad_norm": NaN, "learning_rate": 2.8883374689826303e-05, "loss": 0.0, "step": 200}, {"epoch": 0.9803921568627451, "eval_accuracy": 0.5, "eval_auc": 0.0, "eval_f1": 0.0, "eval_loss": NaN, "eval_precision": 0.0, "eval_recall": 0.0, "eval_runtime": 42.29, "eval_samples_per_second": 4.777, "eval_steps_per_second": 4.777, "eval_unique_labels": 2, "eval_unique_predictions": 1, "step": 200}, {"epoch": 1.0784313725490196, "grad_norm": NaN, "learning_rate": 2.873449131513648e-05, "loss": 0.0, "step": 220}, {"epoch": 1.1764705882352942, "grad_norm": NaN, "learning_rate": 2.858560794044665e-05, "loss": 0.0, "step": 240}, {"epoch": 1.2745098039215685, "grad_norm": NaN, "learning_rate": 2.8436724565756823e-05, "loss": 0.0, "step": 260}, {"epoch": 1.3725490196078431, "grad_norm": NaN, "learning_rate": 2.8287841191067e-05, "loss": 0.0, "step": 280}, {"epoch": 1.4705882352941178, "grad_norm": NaN, "learning_rate": 2.813895781637717e-05, "loss": 0.0, "step": 300}, {"epoch": 1.5686274509803921, "grad_norm": NaN, "learning_rate": 2.7990074441687347e-05, "loss": 0.0, "step": 320}, {"epoch": 1.6666666666666665, "grad_norm": NaN, "learning_rate": 2.784119106699752e-05, "loss": 0.0, "step": 340}, {"epoch": 1.7647058823529411, "grad_norm": NaN, "learning_rate": 2.7692307692307694e-05, "loss": 0.0, "step": 360}, {"epoch": 1.8627450980392157, "grad_norm": NaN, "learning_rate": 2.7543424317617867e-05, "loss": 0.0, "step": 380}, {"epoch": 1.9607843137254903, "grad_norm": NaN, "learning_rate": 2.739454094292804e-05, "loss": 0.0, "step": 400}, {"epoch": 1.9607843137254903, "eval_accuracy": 0.5, "eval_auc": 0.0, "eval_f1": 0.0, "eval_loss": NaN, "eval_precision": 0.0, "eval_recall": 0.0, "eval_runtime": 42.339, "eval_samples_per_second": 4.771, "eval_steps_per_second": 4.771, "eval_unique_labels": 2, "eval_unique_predictions": 1, "step": 400}, {"epoch": 2.0588235294117645, "grad_norm": NaN, "learning_rate": 2.7245657568238214e-05, "loss": 0.0, "step": 420}, {"epoch": 2.156862745098039, "grad_norm": NaN, "learning_rate": 2.7096774193548387e-05, "loss": 0.0, "step": 440}, {"epoch": 2.2549019607843137, "grad_norm": NaN, "learning_rate": 2.6947890818858564e-05, "loss": 0.0, "step": 460}, {"epoch": 2.3529411764705883, "grad_norm": NaN, "learning_rate": 2.6799007444168734e-05, "loss": 0.0, "step": 480}, {"epoch": 2.450980392156863, "grad_norm": NaN, "learning_rate": 2.6650124069478907e-05, "loss": 0.0, "step": 500}, {"epoch": 2.549019607843137, "grad_norm": NaN, "learning_rate": 2.6501240694789084e-05, "loss": 0.0, "step": 520}, {"epoch": 2.6470588235294117, "grad_norm": NaN, "learning_rate": 2.6352357320099254e-05, "loss": 0.0, "step": 540}, {"epoch": 2.7450980392156863, "grad_norm": NaN, "learning_rate": 2.620347394540943e-05, "loss": 0.0, "step": 560}, {"epoch": 2.843137254901961, "grad_norm": NaN, "learning_rate": 2.6054590570719604e-05, "loss": 0.0, "step": 580}, {"epoch": 2.9411764705882355, "grad_norm": NaN, "learning_rate": 2.5905707196029778e-05, "loss": 0.0, "step": 600}, {"epoch": 2.9411764705882355, "eval_accuracy": 0.5, "eval_auc": 0.0, "eval_f1": 0.0, "eval_loss": NaN, "eval_precision": 0.0, "eval_recall": 0.0, "eval_runtime": 42.3195, "eval_samples_per_second": 4.773, "eval_steps_per_second": 4.773, "eval_unique_labels": 2, "eval_unique_predictions": 1, "step": 600}, {"epoch": 3.0392156862745097, "grad_norm": NaN, "learning_rate": 2.575682382133995e-05, "loss": 0.0, "step": 620}, {"epoch": 3.1372549019607843, "grad_norm": NaN, "learning_rate": 2.5607940446650125e-05, "loss": 0.0, "step": 640}, {"epoch": 3.235294117647059, "grad_norm": NaN, "learning_rate": 2.5459057071960298e-05, "loss": 0.0, "step": 660}, {"epoch": 3.3333333333333335, "grad_norm": NaN, "learning_rate": 2.531017369727047e-05, "loss": 0.0, "step": 680}, {"epoch": 3.431372549019608, "grad_norm": NaN, "learning_rate": 2.5161290322580648e-05, "loss": 0.0, "step": 700}, {"epoch": 3.5294117647058822, "grad_norm": NaN, "learning_rate": 2.5012406947890818e-05, "loss": 0.0, "step": 720}, {"epoch": 3.627450980392157, "grad_norm": NaN, "learning_rate": 2.4863523573200995e-05, "loss": 0.0, "step": 740}, {"epoch": 3.7254901960784315, "grad_norm": NaN, "learning_rate": 2.4714640198511168e-05, "loss": 0.0, "step": 760}, {"epoch": 3.8235294117647056, "grad_norm": NaN, "learning_rate": 2.4565756823821338e-05, "loss": 0.0, "step": 780}, {"epoch": 3.9215686274509802, "grad_norm": NaN, "learning_rate": 2.4416873449131515e-05, "loss": 0.0, "step": 800}, {"epoch": 3.9215686274509802, "eval_accuracy": 0.5, "eval_auc": 0.0, "eval_f1": 0.0, "eval_loss": NaN, "eval_precision": 0.0, "eval_recall": 0.0, "eval_runtime": 42.1226, "eval_samples_per_second": 4.796, "eval_steps_per_second": 4.796, "eval_unique_labels": 2, "eval_unique_predictions": 1, "step": 800}, {"epoch": 4.019607843137255, "grad_norm": NaN, "learning_rate": 2.426799007444169e-05, "loss": 0.0, "step": 820}, {"epoch": 4.117647058823529, "grad_norm": NaN, "learning_rate": 2.4119106699751862e-05, "loss": 0.0, "step": 840}, {"epoch": 4.215686274509804, "grad_norm": NaN, "learning_rate": 2.3970223325062035e-05, "loss": 0.0, "step": 860}, {"epoch": 4.313725490196078, "grad_norm": NaN, "learning_rate": 2.3821339950372212e-05, "loss": 0.0, "step": 880}, {"epoch": 4.411764705882353, "grad_norm": NaN, "learning_rate": 2.3672456575682382e-05, "loss": 0.0, "step": 900}, {"epoch": 4.509803921568627, "grad_norm": NaN, "learning_rate": 2.3523573200992555e-05, "loss": 0.0, "step": 920}, {"epoch": 4.607843137254902, "grad_norm": NaN, "learning_rate": 2.3374689826302732e-05, "loss": 0.0, "step": 940}, {"epoch": 4.705882352941177, "grad_norm": NaN, "learning_rate": 2.3225806451612902e-05, "loss": 0.0, "step": 960}, {"epoch": 4.803921568627451, "grad_norm": NaN, "learning_rate": 2.307692307692308e-05, "loss": 0.0, "step": 980}, {"epoch": 4.901960784313726, "grad_norm": NaN, "learning_rate": 2.2928039702233252e-05, "loss": 0.0, "step": 1000}, {"epoch": 4.901960784313726, "eval_accuracy": 0.5, "eval_auc": 0.0, "eval_f1": 0.0, "eval_loss": NaN, "eval_precision": 0.0, "eval_recall": 0.0, "eval_runtime": 42.304, "eval_samples_per_second": 4.775, "eval_steps_per_second": 4.775, "eval_unique_labels": 2, "eval_unique_predictions": 1, "step": 1000}, {"epoch": 5.0, "grad_norm": NaN, "learning_rate": 2.2779156327543422e-05, "loss": 0.0, "step": 1020}, {"epoch": 5.098039215686274, "grad_norm": NaN, "learning_rate": 2.26302729528536e-05, "loss": 0.0, "step": 1040}, {"epoch": 5.196078431372549, "grad_norm": NaN, "learning_rate": 2.2481389578163773e-05, "loss": 0.0, "step": 1060}, {"epoch": 5.294117647058823, "grad_norm": NaN, "learning_rate": 2.2332506203473946e-05, "loss": 0.0, "step": 1080}, {"epoch": 5.392156862745098, "grad_norm": NaN, "learning_rate": 2.218362282878412e-05, "loss": 0.0, "step": 1100}, {"epoch": 5.490196078431373, "grad_norm": NaN, "learning_rate": 2.2034739454094296e-05, "loss": 0.0, "step": 1120}, {"epoch": 5.588235294117647, "grad_norm": NaN, "learning_rate": 2.1885856079404466e-05, "loss": 0.0, "step": 1140}, {"epoch": 5.686274509803922, "grad_norm": NaN, "learning_rate": 2.173697270471464e-05, "loss": 0.0, "step": 1160}, {"epoch": 5.784313725490196, "grad_norm": NaN, "learning_rate": 2.1588089330024816e-05, "loss": 0.0, "step": 1180}, {"epoch": 5.882352941176471, "grad_norm": NaN, "learning_rate": 2.1439205955334986e-05, "loss": 0.0, "step": 1200}, {"epoch": 5.882352941176471, "eval_accuracy": 0.5, "eval_auc": 0.0, "eval_f1": 0.0, "eval_loss": NaN, "eval_precision": 0.0, "eval_recall": 0.0, "eval_runtime": 42.3114, "eval_samples_per_second": 4.774, "eval_steps_per_second": 4.774, "eval_unique_labels": 2, "eval_unique_predictions": 1, "step": 1200}, {"epoch": 5.980392156862745, "grad_norm": NaN, "learning_rate": 2.1290322580645163e-05, "loss": 0.0, "step": 1220}, {"epoch": 6.078431372549019, "grad_norm": NaN, "learning_rate": 2.1141439205955337e-05, "loss": 0.0, "step": 1240}, {"epoch": 6.176470588235294, "grad_norm": NaN, "learning_rate": 2.099255583126551e-05, "loss": 0.0, "step": 1260}, {"epoch": 6.2745098039215685, "grad_norm": NaN, "learning_rate": 2.0843672456575683e-05, "loss": 0.0, "step": 1280}, {"epoch": 6.372549019607844, "grad_norm": NaN, "learning_rate": 2.0694789081885857e-05, "loss": 0.0, "step": 1300}, {"epoch": 6.470588235294118, "grad_norm": NaN, "learning_rate": 2.054590570719603e-05, "loss": 0.0, "step": 1320}, {"epoch": 6.568627450980392, "grad_norm": NaN, "learning_rate": 2.0397022332506204e-05, "loss": 0.0, "step": 1340}, {"epoch": 6.666666666666667, "grad_norm": NaN, "learning_rate": 2.024813895781638e-05, "loss": 0.0, "step": 1360}, {"epoch": 6.764705882352941, "grad_norm": NaN, "learning_rate": 2.009925558312655e-05, "loss": 0.0, "step": 1380}, {"epoch": 6.862745098039216, "grad_norm": NaN, "learning_rate": 1.9950372208436724e-05, "loss": 0.0, "step": 1400}, {"epoch": 6.862745098039216, "eval_accuracy": 0.5, "eval_auc": 0.0, "eval_f1": 0.0, "eval_loss": NaN, "eval_precision": 0.0, "eval_recall": 0.0, "eval_runtime": 42.3364, "eval_samples_per_second": 4.771, "eval_steps_per_second": 4.771, "eval_unique_labels": 2, "eval_unique_predictions": 1, "step": 1400}, {"epoch": 6.96078431372549, "grad_norm": NaN, "learning_rate": 1.98014888337469e-05, "loss": 0.0, "step": 1420}, {"epoch": 7.0588235294117645, "grad_norm": NaN, "learning_rate": 1.965260545905707e-05, "loss": 0.0, "step": 1440}, {"epoch": 7.1568627450980395, "grad_norm": NaN, "learning_rate": 1.9503722084367247e-05, "loss": 0.0, "step": 1460}, {"epoch": 7.254901960784314, "grad_norm": NaN, "learning_rate": 1.935483870967742e-05, "loss": 0.0, "step": 1480}, {"epoch": 7.352941176470588, "grad_norm": NaN, "learning_rate": 1.9205955334987594e-05, "loss": 0.0, "step": 1500}, {"epoch": 7.450980392156863, "grad_norm": NaN, "learning_rate": 1.9057071960297768e-05, "loss": 0.0, "step": 1520}, {"epoch": 7.549019607843137, "grad_norm": NaN, "learning_rate": 1.890818858560794e-05, "loss": 0.0, "step": 1540}, {"epoch": 7.647058823529412, "grad_norm": NaN, "learning_rate": 1.8759305210918114e-05, "loss": 0.0, "step": 1560}, {"epoch": 7.745098039215686, "grad_norm": NaN, "learning_rate": 1.8610421836228288e-05, "loss": 0.0, "step": 1580}, {"epoch": 7.8431372549019605, "grad_norm": NaN, "learning_rate": 1.8461538461538465e-05, "loss": 0.0, "step": 1600}, {"epoch": 7.8431372549019605, "eval_accuracy": 0.5, "eval_auc": 0.0, "eval_f1": 0.0, "eval_loss": NaN, "eval_precision": 0.0, "eval_recall": 0.0, "eval_runtime": 42.3731, "eval_samples_per_second": 4.767, "eval_steps_per_second": 4.767, "eval_unique_labels": 2, "eval_unique_predictions": 1, "step": 1600}], "logging_steps": 20, "max_steps": 4080, "num_input_tokens_seen": 0, "num_train_epochs": 20, "save_steps": 400, "stateful_callbacks": {"EarlyStoppingCallback": {"args": {"early_stopping_patience": 10, "early_stopping_threshold": 0.001}, "attributes": {"early_stopping_patience_counter": 7}}, "TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 0.0, "train_batch_size": 1, "trial_name": null, "trial_params": null}