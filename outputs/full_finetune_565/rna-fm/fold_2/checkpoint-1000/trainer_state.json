{"best_global_step": 100, "best_metric": 0.6666666666666666, "best_model_checkpoint": null, "epoch": 19.607843137254903, "eval_steps": 100, "global_step": 1000, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.9803921568627451, "grad_norm": NaN, "learning_rate": 0.0, "loss": 12.7075, "step": 50}, {"epoch": 1.9607843137254903, "grad_norm": NaN, "learning_rate": 0.0, "loss": 12.8729, "step": 100}, {"epoch": 1.9607843137254903, "eval_accuracy": 0.5, "eval_auc": 0.47113028134496615, "eval_f1": 0.6666666666666666, "eval_loss": 0.7707108855247498, "eval_precision": 0.5, "eval_recall": 1.0, "eval_runtime": 29.5826, "eval_samples_per_second": 6.828, "eval_steps_per_second": 6.828, "step": 100}, {"epoch": 2.9411764705882355, "grad_norm": NaN, "learning_rate": 0.0, "loss": 13.0736, "step": 150}, {"epoch": 3.9215686274509802, "grad_norm": NaN, "learning_rate": 0.0, "loss": 12.7353, "step": 200}, {"epoch": 3.9215686274509802, "eval_accuracy": 0.5, "eval_auc": 0.47113028134496615, "eval_f1": 0.6666666666666666, "eval_loss": 0.7707108855247498, "eval_precision": 0.5, "eval_recall": 1.0, "eval_runtime": 29.9885, "eval_samples_per_second": 6.736, "eval_steps_per_second": 6.736, "step": 200}, {"epoch": 4.901960784313726, "grad_norm": NaN, "learning_rate": 0.0, "loss": 12.6798, "step": 250}, {"epoch": 5.882352941176471, "grad_norm": NaN, "learning_rate": 0.0, "loss": 12.657, "step": 300}, {"epoch": 5.882352941176471, "eval_accuracy": 0.5, "eval_auc": 0.47113028134496615, "eval_f1": 0.6666666666666666, "eval_loss": 0.7707108855247498, "eval_precision": 0.5, "eval_recall": 1.0, "eval_runtime": 29.6582, "eval_samples_per_second": 6.811, "eval_steps_per_second": 6.811, "step": 300}, {"epoch": 6.862745098039216, "grad_norm": NaN, "learning_rate": 0.0, "loss": 12.9584, "step": 350}, {"epoch": 7.8431372549019605, "grad_norm": NaN, "learning_rate": 0.0, "loss": 12.7781, "step": 400}, {"epoch": 7.8431372549019605, "eval_accuracy": 0.5, "eval_auc": 0.47113028134496615, "eval_f1": 0.6666666666666666, "eval_loss": 0.7707108855247498, "eval_precision": 0.5, "eval_recall": 1.0, "eval_runtime": 29.6168, "eval_samples_per_second": 6.82, "eval_steps_per_second": 6.82, "step": 400}, {"epoch": 8.823529411764707, "grad_norm": NaN, "learning_rate": 0.0, "loss": 12.7772, "step": 450}, {"epoch": 9.803921568627452, "grad_norm": NaN, "learning_rate": 0.0, "loss": 12.9989, "step": 500}, {"epoch": 9.803921568627452, "eval_accuracy": 0.5, "eval_auc": 0.47113028134496615, "eval_f1": 0.6666666666666666, "eval_loss": 0.7707108855247498, "eval_precision": 0.5, "eval_recall": 1.0, "eval_runtime": 29.974, "eval_samples_per_second": 6.739, "eval_steps_per_second": 6.739, "step": 500}, {"epoch": 10.784313725490197, "grad_norm": NaN, "learning_rate": 0.0, "loss": 12.9705, "step": 550}, {"epoch": 11.764705882352942, "grad_norm": NaN, "learning_rate": 0.0, "loss": 12.6139, "step": 600}, {"epoch": 11.764705882352942, "eval_accuracy": 0.5, "eval_auc": 0.47113028134496615, "eval_f1": 0.6666666666666666, "eval_loss": 0.7707108855247498, "eval_precision": 0.5, "eval_recall": 1.0, "eval_runtime": 29.9326, "eval_samples_per_second": 6.748, "eval_steps_per_second": 6.748, "step": 600}, {"epoch": 12.745098039215687, "grad_norm": NaN, "learning_rate": 0.0, "loss": 12.969, "step": 650}, {"epoch": 13.72549019607843, "grad_norm": NaN, "learning_rate": 0.0, "loss": 12.9974, "step": 700}, {"epoch": 13.72549019607843, "eval_accuracy": 0.5, "eval_auc": 0.47113028134496615, "eval_f1": 0.6666666666666666, "eval_loss": 0.7707108855247498, "eval_precision": 0.5, "eval_recall": 1.0, "eval_runtime": 29.9257, "eval_samples_per_second": 6.75, "eval_steps_per_second": 6.75, "step": 700}, {"epoch": 14.705882352941176, "grad_norm": NaN, "learning_rate": 0.0, "loss": 12.798, "step": 750}, {"epoch": 15.686274509803921, "grad_norm": NaN, "learning_rate": 0.0, "loss": 12.5807, "step": 800}, {"epoch": 15.686274509803921, "eval_accuracy": 0.5, "eval_auc": 0.47113028134496615, "eval_f1": 0.6666666666666666, "eval_loss": 0.7707108855247498, "eval_precision": 0.5, "eval_recall": 1.0, "eval_runtime": 29.6961, "eval_samples_per_second": 6.802, "eval_steps_per_second": 6.802, "step": 800}, {"epoch": 16.666666666666668, "grad_norm": NaN, "learning_rate": 0.0, "loss": 13.2047, "step": 850}, {"epoch": 17.647058823529413, "grad_norm": NaN, "learning_rate": 0.0, "loss": 12.7236, "step": 900}, {"epoch": 17.647058823529413, "eval_accuracy": 0.5, "eval_auc": 0.47113028134496615, "eval_f1": 0.6666666666666666, "eval_loss": 0.7707108855247498, "eval_precision": 0.5, "eval_recall": 1.0, "eval_runtime": 30.047, "eval_samples_per_second": 6.723, "eval_steps_per_second": 6.723, "step": 900}, {"epoch": 18.627450980392158, "grad_norm": NaN, "learning_rate": 0.0, "loss": 12.7091, "step": 950}, {"epoch": 19.607843137254903, "grad_norm": NaN, "learning_rate": 0.0, "loss": 13.0427, "step": 1000}, {"epoch": 19.607843137254903, "eval_accuracy": 0.5, "eval_auc": 0.47113028134496615, "eval_f1": 0.6666666666666666, "eval_loss": 0.7707108855247498, "eval_precision": 0.5, "eval_recall": 1.0, "eval_runtime": 29.7119, "eval_samples_per_second": 6.799, "eval_steps_per_second": 6.799, "step": 1000}], "logging_steps": 50, "max_steps": 1020, "num_input_tokens_seen": 0, "num_train_epochs": 20, "save_steps": 500, "stateful_callbacks": {"EarlyStoppingCallback": {"args": {"early_stopping_patience": 10, "early_stopping_threshold": 0.001}, "attributes": {"early_stopping_patience_counter": 9}}, "TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 0.0, "train_batch_size": 1, "trial_name": null, "trial_params": null}