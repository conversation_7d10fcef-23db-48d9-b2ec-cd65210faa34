{"best_global_step": 200, "best_metric": 0.0, "best_model_checkpoint": null, "epoch": 1.9607843137254903, "eval_steps": 200, "global_step": 400, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.09803921568627451, "grad_norm": NaN, "learning_rate": 1.2e-05, "loss": 884.389, "step": 20}, {"epoch": 0.19607843137254902, "grad_norm": NaN, "learning_rate": 2.4e-05, "loss": 0.0, "step": 40}, {"epoch": 0.29411764705882354, "grad_norm": NaN, "learning_rate": 2.9925558312655086e-05, "loss": 0.0, "step": 60}, {"epoch": 0.39215686274509803, "grad_norm": NaN, "learning_rate": 2.9776674937965263e-05, "loss": 0.0, "step": 80}, {"epoch": 0.49019607843137253, "grad_norm": NaN, "learning_rate": 2.9627791563275436e-05, "loss": 0.0, "step": 100}, {"epoch": 0.5882352941176471, "grad_norm": NaN, "learning_rate": 2.9478908188585606e-05, "loss": 0.0, "step": 120}, {"epoch": 0.6862745098039216, "grad_norm": NaN, "learning_rate": 2.9330024813895783e-05, "loss": 0.0, "step": 140}, {"epoch": 0.7843137254901961, "grad_norm": NaN, "learning_rate": 2.9181141439205956e-05, "loss": 0.0, "step": 160}, {"epoch": 0.8823529411764706, "grad_norm": NaN, "learning_rate": 2.903225806451613e-05, "loss": 0.0, "step": 180}, {"epoch": 0.9803921568627451, "grad_norm": NaN, "learning_rate": 2.8883374689826303e-05, "loss": 0.0, "step": 200}, {"epoch": 0.9803921568627451, "eval_accuracy": 0.5, "eval_auc": 0.0, "eval_f1": 0.0, "eval_loss": NaN, "eval_precision": 0.0, "eval_recall": 0.0, "eval_runtime": 42.4358, "eval_samples_per_second": 4.76, "eval_steps_per_second": 4.76, "eval_unique_labels": 2, "eval_unique_predictions": 1, "step": 200}, {"epoch": 1.0784313725490196, "grad_norm": NaN, "learning_rate": 2.873449131513648e-05, "loss": 0.0, "step": 220}, {"epoch": 1.1764705882352942, "grad_norm": NaN, "learning_rate": 2.858560794044665e-05, "loss": 0.0, "step": 240}, {"epoch": 1.2745098039215685, "grad_norm": NaN, "learning_rate": 2.8436724565756823e-05, "loss": 0.0, "step": 260}, {"epoch": 1.3725490196078431, "grad_norm": NaN, "learning_rate": 2.8287841191067e-05, "loss": 0.0, "step": 280}, {"epoch": 1.4705882352941178, "grad_norm": NaN, "learning_rate": 2.813895781637717e-05, "loss": 0.0, "step": 300}, {"epoch": 1.5686274509803921, "grad_norm": NaN, "learning_rate": 2.7990074441687347e-05, "loss": 0.0, "step": 320}, {"epoch": 1.6666666666666665, "grad_norm": NaN, "learning_rate": 2.784119106699752e-05, "loss": 0.0, "step": 340}, {"epoch": 1.7647058823529411, "grad_norm": NaN, "learning_rate": 2.7692307692307694e-05, "loss": 0.0, "step": 360}, {"epoch": 1.8627450980392157, "grad_norm": NaN, "learning_rate": 2.7543424317617867e-05, "loss": 0.0, "step": 380}, {"epoch": 1.9607843137254903, "grad_norm": NaN, "learning_rate": 2.739454094292804e-05, "loss": 0.0, "step": 400}, {"epoch": 1.9607843137254903, "eval_accuracy": 0.5, "eval_auc": 0.0, "eval_f1": 0.0, "eval_loss": NaN, "eval_precision": 0.0, "eval_recall": 0.0, "eval_runtime": 42.2345, "eval_samples_per_second": 4.783, "eval_steps_per_second": 4.783, "eval_unique_labels": 2, "eval_unique_predictions": 1, "step": 400}], "logging_steps": 20, "max_steps": 4080, "num_input_tokens_seen": 0, "num_train_epochs": 20, "save_steps": 400, "stateful_callbacks": {"EarlyStoppingCallback": {"args": {"early_stopping_patience": 10, "early_stopping_threshold": 0.001}, "attributes": {"early_stopping_patience_counter": 1}}, "TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 0.0, "train_batch_size": 1, "trial_name": null, "trial_params": null}