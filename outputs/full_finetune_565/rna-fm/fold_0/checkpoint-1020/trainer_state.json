{"best_global_step": 100, "best_metric": 0.6666666666666666, "best_model_checkpoint": null, "epoch": 20.0, "eval_steps": 100, "global_step": 1020, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.9803921568627451, "grad_norm": NaN, "learning_rate": 0.0, "loss": 12.7056, "step": 50}, {"epoch": 1.9607843137254903, "grad_norm": NaN, "learning_rate": 0.0, "loss": 12.851, "step": 100}, {"epoch": 1.9607843137254903, "eval_accuracy": 0.5, "eval_auc": 0.47911969414763256, "eval_f1": 0.6666666666666666, "eval_loss": 0.7704872488975525, "eval_precision": 0.5, "eval_recall": 1.0, "eval_runtime": 29.6581, "eval_samples_per_second": 6.811, "eval_steps_per_second": 6.811, "step": 100}, {"epoch": 2.9411764705882355, "grad_norm": NaN, "learning_rate": 0.0, "loss": 13.0651, "step": 150}, {"epoch": 3.9215686274509802, "grad_norm": NaN, "learning_rate": 0.0, "loss": 12.7169, "step": 200}, {"epoch": 3.9215686274509802, "eval_accuracy": 0.5, "eval_auc": 0.47911969414763256, "eval_f1": 0.6666666666666666, "eval_loss": 0.7704872488975525, "eval_precision": 0.5, "eval_recall": 1.0, "eval_runtime": 29.6568, "eval_samples_per_second": 6.811, "eval_steps_per_second": 6.811, "step": 200}, {"epoch": 4.901960784313726, "grad_norm": NaN, "learning_rate": 0.0, "loss": 12.7003, "step": 250}, {"epoch": 5.882352941176471, "grad_norm": NaN, "learning_rate": 0.0, "loss": 12.669, "step": 300}, {"epoch": 5.882352941176471, "eval_accuracy": 0.5, "eval_auc": 0.47911969414763256, "eval_f1": 0.6666666666666666, "eval_loss": 0.7704872488975525, "eval_precision": 0.5, "eval_recall": 1.0, "eval_runtime": 29.9408, "eval_samples_per_second": 6.747, "eval_steps_per_second": 6.747, "step": 300}, {"epoch": 6.862745098039216, "grad_norm": NaN, "learning_rate": 0.0, "loss": 12.9578, "step": 350}, {"epoch": 7.8431372549019605, "grad_norm": NaN, "learning_rate": 0.0, "loss": 12.7875, "step": 400}, {"epoch": 7.8431372549019605, "eval_accuracy": 0.5, "eval_auc": 0.47911969414763256, "eval_f1": 0.6666666666666666, "eval_loss": 0.7704872488975525, "eval_precision": 0.5, "eval_recall": 1.0, "eval_runtime": 29.9679, "eval_samples_per_second": 6.741, "eval_steps_per_second": 6.741, "step": 400}, {"epoch": 8.823529411764707, "grad_norm": NaN, "learning_rate": 0.0, "loss": 12.7919, "step": 450}, {"epoch": 9.803921568627452, "grad_norm": NaN, "learning_rate": 0.0, "loss": 12.9709, "step": 500}, {"epoch": 9.803921568627452, "eval_accuracy": 0.5, "eval_auc": 0.47911969414763256, "eval_f1": 0.6666666666666666, "eval_loss": 0.7704872488975525, "eval_precision": 0.5, "eval_recall": 1.0, "eval_runtime": 29.8459, "eval_samples_per_second": 6.768, "eval_steps_per_second": 6.768, "step": 500}, {"epoch": 10.784313725490197, "grad_norm": NaN, "learning_rate": 0.0, "loss": 12.957, "step": 550}, {"epoch": 11.764705882352942, "grad_norm": NaN, "learning_rate": 0.0, "loss": 12.5998, "step": 600}, {"epoch": 11.764705882352942, "eval_accuracy": 0.5, "eval_auc": 0.47911969414763256, "eval_f1": 0.6666666666666666, "eval_loss": 0.7704872488975525, "eval_precision": 0.5, "eval_recall": 1.0, "eval_runtime": 29.8402, "eval_samples_per_second": 6.769, "eval_steps_per_second": 6.769, "step": 600}, {"epoch": 12.745098039215687, "grad_norm": NaN, "learning_rate": 0.0, "loss": 12.9568, "step": 650}, {"epoch": 13.72549019607843, "grad_norm": NaN, "learning_rate": 0.0, "loss": 12.9886, "step": 700}, {"epoch": 13.72549019607843, "eval_accuracy": 0.5, "eval_auc": 0.47911969414763256, "eval_f1": 0.6666666666666666, "eval_loss": 0.7704872488975525, "eval_precision": 0.5, "eval_recall": 1.0, "eval_runtime": 29.8079, "eval_samples_per_second": 6.777, "eval_steps_per_second": 6.777, "step": 700}, {"epoch": 14.705882352941176, "grad_norm": NaN, "learning_rate": 0.0, "loss": 12.796, "step": 750}, {"epoch": 15.686274509803921, "grad_norm": NaN, "learning_rate": 0.0, "loss": 12.604, "step": 800}, {"epoch": 15.686274509803921, "eval_accuracy": 0.5, "eval_auc": 0.47911969414763256, "eval_f1": 0.6666666666666666, "eval_loss": 0.7704872488975525, "eval_precision": 0.5, "eval_recall": 1.0, "eval_runtime": 48.7292, "eval_samples_per_second": 4.145, "eval_steps_per_second": 4.145, "step": 800}, {"epoch": 16.666666666666668, "grad_norm": NaN, "learning_rate": 0.0, "loss": 13.2075, "step": 850}, {"epoch": 17.647058823529413, "grad_norm": NaN, "learning_rate": 0.0, "loss": 12.7239, "step": 900}, {"epoch": 17.647058823529413, "eval_accuracy": 0.5, "eval_auc": 0.47911969414763256, "eval_f1": 0.6666666666666666, "eval_loss": 0.7704872488975525, "eval_precision": 0.5, "eval_recall": 1.0, "eval_runtime": 29.8523, "eval_samples_per_second": 6.767, "eval_steps_per_second": 6.767, "step": 900}, {"epoch": 18.627450980392158, "grad_norm": NaN, "learning_rate": 0.0, "loss": 12.729, "step": 950}, {"epoch": 19.607843137254903, "grad_norm": NaN, "learning_rate": 0.0, "loss": 13.0355, "step": 1000}, {"epoch": 19.607843137254903, "eval_accuracy": 0.5, "eval_auc": 0.47911969414763256, "eval_f1": 0.6666666666666666, "eval_loss": 0.7704872488975525, "eval_precision": 0.5, "eval_recall": 1.0, "eval_runtime": 29.7032, "eval_samples_per_second": 6.801, "eval_steps_per_second": 6.801, "step": 1000}], "logging_steps": 50, "max_steps": 1020, "num_input_tokens_seen": 0, "num_train_epochs": 20, "save_steps": 500, "stateful_callbacks": {"EarlyStoppingCallback": {"args": {"early_stopping_patience": 10, "early_stopping_threshold": 0.001}, "attributes": {"early_stopping_patience_counter": 9}}, "TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": true}, "attributes": {}}}, "total_flos": 0.0, "train_batch_size": 1, "trial_name": null, "trial_params": null}