{"best_global_step": 100, "best_metric": 0.6666666666666666, "best_model_checkpoint": null, "epoch": 9.811881188118813, "eval_steps": 100, "global_step": 500, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.9900990099009901, "grad_norm": NaN, "learning_rate": 0.0, "loss": 12.6687, "step": 50}, {"epoch": 1.9702970297029703, "grad_norm": NaN, "learning_rate": 0.0, "loss": 12.5907, "step": 100}, {"epoch": 1.9702970297029703, "eval_accuracy": 0.5, "eval_auc": 0.3784580498866213, "eval_f1": 0.6666666666666666, "eval_loss": 0.7746372818946838, "eval_precision": 0.5, "eval_recall": 1.0, "eval_runtime": 31.1121, "eval_samples_per_second": 6.75, "eval_steps_per_second": 6.75, "step": 100}, {"epoch": 2.9504950495049505, "grad_norm": NaN, "learning_rate": 0.0, "loss": 12.9256, "step": 150}, {"epoch": 3.9306930693069306, "grad_norm": NaN, "learning_rate": 0.0, "loss": 12.7221, "step": 200}, {"epoch": 3.9306930693069306, "eval_accuracy": 0.5, "eval_auc": 0.3784580498866213, "eval_f1": 0.6666666666666666, "eval_loss": 0.7746372818946838, "eval_precision": 0.5, "eval_recall": 1.0, "eval_runtime": 30.7527, "eval_samples_per_second": 6.829, "eval_steps_per_second": 6.829, "step": 200}, {"epoch": 4.910891089108911, "grad_norm": NaN, "learning_rate": 0.0, "loss": 12.5234, "step": 250}, {"epoch": 5.891089108910891, "grad_norm": NaN, "learning_rate": 0.0, "loss": 12.8202, "step": 300}, {"epoch": 5.891089108910891, "eval_accuracy": 0.5, "eval_auc": 0.3784580498866213, "eval_f1": 0.6666666666666666, "eval_loss": 0.7746372818946838, "eval_precision": 0.5, "eval_recall": 1.0, "eval_runtime": 31.1103, "eval_samples_per_second": 6.75, "eval_steps_per_second": 6.75, "step": 300}, {"epoch": 6.871287128712871, "grad_norm": NaN, "learning_rate": 0.0, "loss": 12.7354, "step": 350}, {"epoch": 7.851485148514851, "grad_norm": NaN, "learning_rate": 0.0, "loss": 12.6998, "step": 400}, {"epoch": 7.851485148514851, "eval_accuracy": 0.5, "eval_auc": 0.3784580498866213, "eval_f1": 0.6666666666666666, "eval_loss": 0.7746372818946838, "eval_precision": 0.5, "eval_recall": 1.0, "eval_runtime": 30.9616, "eval_samples_per_second": 6.783, "eval_steps_per_second": 6.783, "step": 400}, {"epoch": 8.831683168316832, "grad_norm": NaN, "learning_rate": 0.0, "loss": 12.8583, "step": 450}, {"epoch": 9.811881188118813, "grad_norm": NaN, "learning_rate": 0.0, "loss": 12.7185, "step": 500}, {"epoch": 9.811881188118813, "eval_accuracy": 0.5, "eval_auc": 0.3784580498866213, "eval_f1": 0.6666666666666666, "eval_loss": 0.7746372818946838, "eval_precision": 0.5, "eval_recall": 1.0, "eval_runtime": 30.9615, "eval_samples_per_second": 6.783, "eval_steps_per_second": 6.783, "step": 500}], "logging_steps": 50, "max_steps": 1000, "num_input_tokens_seen": 0, "num_train_epochs": 20, "save_steps": 500, "stateful_callbacks": {"EarlyStoppingCallback": {"args": {"early_stopping_patience": 10, "early_stopping_threshold": 0.001}, "attributes": {"early_stopping_patience_counter": 4}}, "TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 0.0, "train_batch_size": 1, "trial_name": null, "trial_params": null}