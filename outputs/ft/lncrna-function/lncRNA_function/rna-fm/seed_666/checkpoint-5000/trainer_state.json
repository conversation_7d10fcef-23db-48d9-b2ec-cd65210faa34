{"best_global_step": 500, "best_metric": 0.968411330049261, "best_model_checkpoint": "./outputs/ft/lncrna-function/lncRNA_function/rna-fm/seed_666/checkpoint-500", "epoch": 17.54385964912281, "eval_steps": 250, "global_step": 5000, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.3508771929824561, "grad_norm": 7.558069705963135, "learning_rate": 3e-05, "loss": 0.5481, "step": 100}, {"epoch": 0.7017543859649122, "grad_norm": 1.3010072708129883, "learning_rate": 2.9652071005917163e-05, "loss": 0.2367, "step": 200}, {"epoch": 0.8771929824561403, "eval_accuracy": 0.6228070175438597, "eval_f1": 0.5615870869420565, "eval_loss": 1.4913936853408813, "eval_precision": 0.7781961178045514, "eval_recall": 0.6228070175438597, "eval_runtime": 12.5354, "eval_samples_per_second": 45.471, "eval_steps_per_second": 11.408, "step": 250}, {"epoch": 1.0526315789473684, "grad_norm": 0.07069426774978638, "learning_rate": 2.9297041420118343e-05, "loss": 0.1337, "step": 300}, {"epoch": 1.4035087719298245, "grad_norm": 0.6605066061019897, "learning_rate": 2.8945562130177518e-05, "loss": 0.151, "step": 400}, {"epoch": 1.7543859649122808, "grad_norm": 20.995800018310547, "learning_rate": 2.8590532544378698e-05, "loss": 0.1088, "step": 500}, {"epoch": 1.7543859649122808, "eval_accuracy": 0.968421052631579, "eval_f1": 0.968411330049261, "eval_loss": 0.17265020310878754, "eval_precision": 0.9689984591679508, "eval_recall": 0.968421052631579, "eval_runtime": 13.5579, "eval_samples_per_second": 42.042, "eval_steps_per_second": 10.547, "step": 500}, {"epoch": 2.1052631578947367, "grad_norm": 0.0871230959892273, "learning_rate": 2.823550295857988e-05, "loss": 0.1193, "step": 600}, {"epoch": 2.456140350877193, "grad_norm": 3.3446402549743652, "learning_rate": 2.7880473372781065e-05, "loss": 0.1254, "step": 700}, {"epoch": 2.6315789473684212, "eval_accuracy": 0.8771929824561403, "eval_f1": 0.875524109014675, "eval_loss": 0.6687341332435608, "eval_precision": 0.8985676930882411, "eval_recall": 0.8771929824561403, "eval_runtime": 13.8833, "eval_samples_per_second": 41.057, "eval_steps_per_second": 10.3, "step": 750}, {"epoch": 2.807017543859649, "grad_norm": 9.491959571838379, "learning_rate": 2.752544378698225e-05, "loss": 0.095, "step": 800}, {"epoch": 3.1578947368421053, "grad_norm": 0.01638110540807247, "learning_rate": 2.7170414201183432e-05, "loss": 0.094, "step": 900}, {"epoch": 3.5087719298245617, "grad_norm": 8.328773498535156, "learning_rate": 2.6815384615384615e-05, "loss": 0.1036, "step": 1000}, {"epoch": 3.5087719298245617, "eval_accuracy": 0.9350877192982456, "eval_f1": 0.9348694145659042, "eval_loss": 0.3624432682991028, "eval_precision": 0.9410002994908655, "eval_recall": 0.9350877192982456, "eval_runtime": 13.6305, "eval_samples_per_second": 41.818, "eval_steps_per_second": 10.491, "step": 1000}, {"epoch": 3.8596491228070176, "grad_norm": 7.17315673828125, "learning_rate": 2.6460355029585802e-05, "loss": 0.0915, "step": 1100}, {"epoch": 4.2105263157894735, "grad_norm": 28.17314910888672, "learning_rate": 2.6105325443786982e-05, "loss": 0.0572, "step": 1200}, {"epoch": 4.385964912280702, "eval_accuracy": 0.8526315789473684, "eval_f1": 0.8495172483153978, "eval_loss": 0.8679665923118591, "eval_precision": 0.8844579267392384, "eval_recall": 0.8526315789473684, "eval_runtime": 14.2051, "eval_samples_per_second": 40.126, "eval_steps_per_second": 10.067, "step": 1250}, {"epoch": 4.56140350877193, "grad_norm": 1.2464666366577148, "learning_rate": 2.5750295857988166e-05, "loss": 0.0746, "step": 1300}, {"epoch": 4.912280701754386, "grad_norm": 10.38223934173584, "learning_rate": 2.539526627218935e-05, "loss": 0.0783, "step": 1400}, {"epoch": 5.2631578947368425, "grad_norm": 0.017138497903943062, "learning_rate": 2.5040236686390533e-05, "loss": 0.0716, "step": 1500}, {"epoch": 5.2631578947368425, "eval_accuracy": 0.9438596491228071, "eval_f1": 0.943742597710225, "eval_loss": 0.36908289790153503, "eval_precision": 0.9475846999962757, "eval_recall": 0.9438596491228071, "eval_runtime": 13.1682, "eval_samples_per_second": 43.286, "eval_steps_per_second": 10.859, "step": 1500}, {"epoch": 5.614035087719298, "grad_norm": 0.00829795841127634, "learning_rate": 2.4685207100591716e-05, "loss": 0.0393, "step": 1600}, {"epoch": 5.964912280701754, "grad_norm": 0.12040179967880249, "learning_rate": 2.43301775147929e-05, "loss": 0.0296, "step": 1700}, {"epoch": 6.140350877192983, "eval_accuracy": 0.9122807017543859, "eval_f1": 0.9117548705118836, "eval_loss": 0.6092590689659119, "eval_precision": 0.9223473621813869, "eval_recall": 0.9122807017543859, "eval_runtime": 14.9902, "eval_samples_per_second": 38.025, "eval_steps_per_second": 9.54, "step": 1750}, {"epoch": 6.315789473684211, "grad_norm": 0.7107176184654236, "learning_rate": 2.3975147928994083e-05, "loss": 0.0561, "step": 1800}, {"epoch": 6.666666666666667, "grad_norm": 0.01618235372006893, "learning_rate": 2.3620118343195267e-05, "loss": 0.0341, "step": 1900}, {"epoch": 7.017543859649122, "grad_norm": 0.05208203196525574, "learning_rate": 2.326508875739645e-05, "loss": 0.0414, "step": 2000}, {"epoch": 7.017543859649122, "eval_accuracy": 0.8596491228070176, "eval_f1": 0.8569707919301415, "eval_loss": 0.9688880443572998, "eval_precision": 0.8887691140655568, "eval_recall": 0.8596491228070176, "eval_runtime": 13.5225, "eval_samples_per_second": 42.152, "eval_steps_per_second": 10.575, "step": 2000}, {"epoch": 7.368421052631579, "grad_norm": 0.016512518748641014, "learning_rate": 2.2910059171597634e-05, "loss": 0.0402, "step": 2100}, {"epoch": 7.719298245614035, "grad_norm": 0.22889171540737152, "learning_rate": 2.2555029585798817e-05, "loss": 0.036, "step": 2200}, {"epoch": 7.894736842105263, "eval_accuracy": 0.8789473684210526, "eval_f1": 0.8772514052264124, "eval_loss": 0.780001699924469, "eval_precision": 0.9011155129274395, "eval_recall": 0.8789473684210526, "eval_runtime": 13.2606, "eval_samples_per_second": 42.984, "eval_steps_per_second": 10.784, "step": 2250}, {"epoch": 8.070175438596491, "grad_norm": 0.8874084949493408, "learning_rate": 2.22e-05, "loss": 0.0411, "step": 2300}, {"epoch": 8.421052631578947, "grad_norm": 0.04467375949025154, "learning_rate": 2.1848520710059172e-05, "loss": 0.0267, "step": 2400}, {"epoch": 8.771929824561404, "grad_norm": 0.006562341935932636, "learning_rate": 2.1493491124260356e-05, "loss": 0.0314, "step": 2500}, {"epoch": 8.771929824561404, "eval_accuracy": 0.9543859649122807, "eval_f1": 0.9543297380585516, "eval_loss": 0.34611976146698, "eval_precision": 0.9566347046087227, "eval_recall": 0.9543859649122807, "eval_runtime": 13.9551, "eval_samples_per_second": 40.845, "eval_steps_per_second": 10.247, "step": 2500}, {"epoch": 9.12280701754386, "grad_norm": 0.008815362118184566, "learning_rate": 2.113846153846154e-05, "loss": 0.0159, "step": 2600}, {"epoch": 9.473684210526315, "grad_norm": 15.401994705200195, "learning_rate": 2.0783431952662723e-05, "loss": 0.0304, "step": 2700}, {"epoch": 9.649122807017545, "eval_accuracy": 0.9438596491228071, "eval_f1": 0.9437238519542386, "eval_loss": 0.3878314197063446, "eval_precision": 0.9481856267326365, "eval_recall": 0.9438596491228071, "eval_runtime": 13.839, "eval_samples_per_second": 41.188, "eval_steps_per_second": 10.333, "step": 2750}, {"epoch": 9.824561403508772, "grad_norm": 0.014916175045073032, "learning_rate": 2.0428402366863906e-05, "loss": 0.0117, "step": 2800}, {"epoch": 10.175438596491228, "grad_norm": 0.25804436206817627, "learning_rate": 2.007337278106509e-05, "loss": 0.0097, "step": 2900}, {"epoch": 10.526315789473685, "grad_norm": 0.0026562318671494722, "learning_rate": 1.9718343195266273e-05, "loss": 0.0333, "step": 3000}, {"epoch": 10.526315789473685, "eval_accuracy": 0.9070175438596492, "eval_f1": 0.9062671618590191, "eval_loss": 0.6647441387176514, "eval_precision": 0.9204822954822954, "eval_recall": 0.9070175438596492, "eval_runtime": 13.1826, "eval_samples_per_second": 43.239, "eval_steps_per_second": 10.848, "step": 3000}, {"epoch": 10.87719298245614, "grad_norm": 52.15843963623047, "learning_rate": 1.9363313609467453e-05, "loss": 0.0407, "step": 3100}, {"epoch": 11.228070175438596, "grad_norm": 0.011181669309735298, "learning_rate": 1.900828402366864e-05, "loss": 0.0145, "step": 3200}, {"epoch": 11.403508771929825, "eval_accuracy": 0.9140350877192982, "eval_f1": 0.9135430628600436, "eval_loss": 0.6258182525634766, "eval_precision": 0.9236797016730498, "eval_recall": 0.9140350877192982, "eval_runtime": 13.374, "eval_samples_per_second": 42.62, "eval_steps_per_second": 10.692, "step": 3250}, {"epoch": 11.578947368421053, "grad_norm": 0.006090971641242504, "learning_rate": 1.8653254437869824e-05, "loss": 0.0077, "step": 3300}, {"epoch": 11.929824561403509, "grad_norm": 0.02873513102531433, "learning_rate": 1.8298224852071007e-05, "loss": 0.028, "step": 3400}, {"epoch": 12.280701754385966, "grad_norm": 0.0023238160647451878, "learning_rate": 1.794319526627219e-05, "loss": 0.0088, "step": 3500}, {"epoch": 12.280701754385966, "eval_accuracy": 0.9035087719298246, "eval_f1": 0.9026672586318774, "eval_loss": 0.7996886372566223, "eval_precision": 0.9179631707814733, "eval_recall": 0.9035087719298246, "eval_runtime": 13.5864, "eval_samples_per_second": 41.954, "eval_steps_per_second": 10.525, "step": 3500}, {"epoch": 12.631578947368421, "grad_norm": 2.0599687099456787, "learning_rate": 1.7588165680473374e-05, "loss": 0.0274, "step": 3600}, {"epoch": 12.982456140350877, "grad_norm": 17.750795364379883, "learning_rate": 1.7233136094674558e-05, "loss": 0.0261, "step": 3700}, {"epoch": 13.157894736842104, "eval_accuracy": 0.9649122807017544, "eval_f1": 0.9648911008179758, "eval_loss": 0.29739633202552795, "eval_precision": 0.9660368510039617, "eval_recall": 0.9649122807017544, "eval_runtime": 13.3247, "eval_samples_per_second": 42.778, "eval_steps_per_second": 10.732, "step": 3750}, {"epoch": 13.333333333333334, "grad_norm": 0.003400753252208233, "learning_rate": 1.6878106508875738e-05, "loss": 0.0126, "step": 3800}, {"epoch": 13.68421052631579, "grad_norm": 0.0029629836790263653, "learning_rate": 1.6523076923076925e-05, "loss": 0.0122, "step": 3900}, {"epoch": 14.035087719298245, "grad_norm": 0.0019597485661506653, "learning_rate": 1.6168047337278105e-05, "loss": 0.0076, "step": 4000}, {"epoch": 14.035087719298245, "eval_accuracy": 0.968421052631579, "eval_f1": 0.9684070502161624, "eval_loss": 0.2790271043777466, "eval_precision": 0.9692529692529692, "eval_recall": 0.968421052631579, "eval_runtime": 13.5869, "eval_samples_per_second": 41.952, "eval_steps_per_second": 10.525, "step": 4000}, {"epoch": 14.385964912280702, "grad_norm": 0.0026032228488475084, "learning_rate": 1.581301775147929e-05, "loss": 0.0274, "step": 4100}, {"epoch": 14.736842105263158, "grad_norm": 0.01112072728574276, "learning_rate": 1.5457988165680472e-05, "loss": 0.021, "step": 4200}, {"epoch": 14.912280701754385, "eval_accuracy": 0.9228070175438596, "eval_f1": 0.9223856209150326, "eval_loss": 0.579951822757721, "eval_precision": 0.9321931513572695, "eval_recall": 0.9228070175438596, "eval_runtime": 13.2275, "eval_samples_per_second": 43.092, "eval_steps_per_second": 10.811, "step": 4250}, {"epoch": 15.087719298245615, "grad_norm": 0.007202762644737959, "learning_rate": 1.5102958579881657e-05, "loss": 0.0217, "step": 4300}, {"epoch": 15.43859649122807, "grad_norm": 0.9263416528701782, "learning_rate": 1.474792899408284e-05, "loss": 0.0041, "step": 4400}, {"epoch": 15.789473684210526, "grad_norm": 0.0033661748748272657, "learning_rate": 1.4392899408284024e-05, "loss": 0.0102, "step": 4500}, {"epoch": 15.789473684210526, "eval_accuracy": 0.9315789473684211, "eval_f1": 0.9313488423802774, "eval_loss": 0.5328159332275391, "eval_precision": 0.9374438454627135, "eval_recall": 0.9315789473684211, "eval_runtime": 13.9136, "eval_samples_per_second": 40.967, "eval_steps_per_second": 10.278, "step": 4500}, {"epoch": 16.140350877192983, "grad_norm": 50.584678649902344, "learning_rate": 1.4041420118343195e-05, "loss": 0.0144, "step": 4600}, {"epoch": 16.49122807017544, "grad_norm": 0.0008620317676104605, "learning_rate": 1.3686390532544379e-05, "loss": 0.0064, "step": 4700}, {"epoch": 16.666666666666668, "eval_accuracy": 0.9508771929824561, "eval_f1": 0.9508039060958768, "eval_loss": 0.3820897936820984, "eval_precision": 0.9535799655689179, "eval_recall": 0.9508771929824561, "eval_runtime": 13.3111, "eval_samples_per_second": 42.822, "eval_steps_per_second": 10.743, "step": 4750}, {"epoch": 16.842105263157894, "grad_norm": 0.0013304372550919652, "learning_rate": 1.3331360946745564e-05, "loss": 0.0137, "step": 4800}, {"epoch": 17.19298245614035, "grad_norm": 0.002585162641480565, "learning_rate": 1.2976331360946747e-05, "loss": 0.0018, "step": 4900}, {"epoch": 17.54385964912281, "grad_norm": 0.0007394463173113763, "learning_rate": 1.262130177514793e-05, "loss": 0.0046, "step": 5000}, {"epoch": 17.54385964912281, "eval_accuracy": 0.9350877192982456, "eval_f1": 0.9348420483509693, "eval_loss": 0.57169508934021, "eval_precision": 0.94175, "eval_recall": 0.9350877192982456, "eval_runtime": 13.4883, "eval_samples_per_second": 42.259, "eval_steps_per_second": 10.602, "step": 5000}], "logging_steps": 100, "max_steps": 8550, "num_input_tokens_seen": 0, "num_train_epochs": 30, "save_steps": 500, "stateful_callbacks": {"EarlyStoppingCallback": {"args": {"early_stopping_patience": 20, "early_stopping_threshold": 0.0}, "attributes": {"early_stopping_patience_counter": 18}}, "TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 4.85654366519296e+16, "train_batch_size": 4, "trial_name": null, "trial_params": null}