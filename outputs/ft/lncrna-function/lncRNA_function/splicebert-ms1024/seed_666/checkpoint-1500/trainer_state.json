{"best_global_step": 1500, "best_metric": 0.9859642210758303, "best_model_checkpoint": "./outputs/ft/lncrna-function/lncRNA_function/splicebert-ms1024/seed_666/checkpoint-1500", "epoch": 5.2631578947368425, "eval_steps": 250, "global_step": 1500, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.3508771929824561, "grad_norm": 3.3389978408813477, "learning_rate": 3e-05, "loss": 0.5186, "step": 100}, {"epoch": 0.7017543859649122, "grad_norm": 0.37512487173080444, "learning_rate": 2.9648520710059175e-05, "loss": 0.1647, "step": 200}, {"epoch": 0.8771929824561403, "eval_accuracy": 0.9333333333333333, "eval_f1": 0.9331225530758686, "eval_loss": 0.20295535027980804, "eval_precision": 0.9388660989264472, "eval_recall": 0.9333333333333333, "eval_runtime": 6.2433, "eval_samples_per_second": 91.298, "eval_steps_per_second": 22.905, "step": 250}, {"epoch": 1.0526315789473684, "grad_norm": 0.1161445751786232, "learning_rate": 2.9297041420118343e-05, "loss": 0.0949, "step": 300}, {"epoch": 1.4035087719298245, "grad_norm": 0.19000214338302612, "learning_rate": 2.894201183431953e-05, "loss": 0.0866, "step": 400}, {"epoch": 1.7543859649122808, "grad_norm": 12.602439880371094, "learning_rate": 2.858698224852071e-05, "loss": 0.0928, "step": 500}, {"epoch": 1.7543859649122808, "eval_accuracy": 0.9719298245614035, "eval_f1": 0.9719173779699221, "eval_loss": 0.09560095518827438, "eval_precision": 0.9727679727679729, "eval_recall": 0.9719298245614035, "eval_runtime": 6.6068, "eval_samples_per_second": 86.275, "eval_steps_per_second": 21.644, "step": 500}, {"epoch": 2.1052631578947367, "grad_norm": 0.06775994598865509, "learning_rate": 2.8231952662721894e-05, "loss": 0.0686, "step": 600}, {"epoch": 2.456140350877193, "grad_norm": 0.28925344347953796, "learning_rate": 2.7876923076923077e-05, "loss": 0.0724, "step": 700}, {"epoch": 2.6315789473684212, "eval_accuracy": 0.9754385964912281, "eval_f1": 0.9754277057236818, "eval_loss": 0.08033046871423721, "eval_precision": 0.9762829762829764, "eval_recall": 0.9754385964912281, "eval_runtime": 6.2347, "eval_samples_per_second": 91.424, "eval_steps_per_second": 22.936, "step": 750}, {"epoch": 2.807017543859649, "grad_norm": 0.034019388258457184, "learning_rate": 2.752189349112426e-05, "loss": 0.0594, "step": 800}, {"epoch": 3.1578947368421053, "grad_norm": 0.03789934143424034, "learning_rate": 2.7170414201183432e-05, "loss": 0.0666, "step": 900}, {"epoch": 3.5087719298245617, "grad_norm": 0.02472812496125698, "learning_rate": 2.6815384615384615e-05, "loss": 0.049, "step": 1000}, {"epoch": 3.5087719298245617, "eval_accuracy": 0.9824561403508771, "eval_f1": 0.9824552763447879, "eval_loss": 0.061074621975421906, "eval_precision": 0.9825511950645864, "eval_recall": 0.9824561403508771, "eval_runtime": 6.1224, "eval_samples_per_second": 93.101, "eval_steps_per_second": 23.357, "step": 1000}, {"epoch": 3.8596491228070176, "grad_norm": 10.881855964660645, "learning_rate": 2.6460355029585802e-05, "loss": 0.0467, "step": 1100}, {"epoch": 4.2105263157894735, "grad_norm": 0.07170745730400085, "learning_rate": 2.6105325443786982e-05, "loss": 0.0337, "step": 1200}, {"epoch": 4.385964912280702, "eval_accuracy": 0.9824561403508771, "eval_f1": 0.9824552763447879, "eval_loss": 0.08872972428798676, "eval_precision": 0.9825511950645864, "eval_recall": 0.9824561403508771, "eval_runtime": 6.5743, "eval_samples_per_second": 86.701, "eval_steps_per_second": 21.751, "step": 1250}, {"epoch": 4.56140350877193, "grad_norm": 0.02985038235783577, "learning_rate": 2.5750295857988166e-05, "loss": 0.0284, "step": 1300}, {"epoch": 4.912280701754386, "grad_norm": 0.015634162351489067, "learning_rate": 2.539526627218935e-05, "loss": 0.0314, "step": 1400}, {"epoch": 5.2631578947368425, "grad_norm": 0.0125426659360528, "learning_rate": 2.5040236686390533e-05, "loss": 0.0248, "step": 1500}, {"epoch": 5.2631578947368425, "eval_accuracy": 0.9859649122807017, "eval_f1": 0.9859642210758303, "eval_loss": 0.05325772985816002, "eval_precision": 0.98606065830142, "eval_recall": 0.9859649122807017, "eval_runtime": 6.5455, "eval_samples_per_second": 87.083, "eval_steps_per_second": 21.847, "step": 1500}], "logging_steps": 100, "max_steps": 8550, "num_input_tokens_seen": 0, "num_train_epochs": 30, "save_steps": 500, "stateful_callbacks": {"EarlyStoppingCallback": {"args": {"early_stopping_patience": 20, "early_stopping_threshold": 0.0}, "attributes": {"early_stopping_patience_counter": 0}}, "TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 2826881707540480.0, "train_batch_size": 4, "trial_name": null, "trial_params": null}