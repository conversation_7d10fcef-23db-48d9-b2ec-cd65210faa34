{"best_global_step": 1750, "best_metric": 0.9877189580505461, "best_model_checkpoint": "./outputs/ft/lncrna-function/lncRNA_function/splicebert-ms1024/seed_666/checkpoint-1500", "epoch": 22.80701754385965, "eval_steps": 250, "global_step": 6500, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.3508771929824561, "grad_norm": 3.3389978408813477, "learning_rate": 3e-05, "loss": 0.5186, "step": 100}, {"epoch": 0.7017543859649122, "grad_norm": 0.37512487173080444, "learning_rate": 2.9648520710059175e-05, "loss": 0.1647, "step": 200}, {"epoch": 0.8771929824561403, "eval_accuracy": 0.9333333333333333, "eval_f1": 0.9331225530758686, "eval_loss": 0.20295535027980804, "eval_precision": 0.9388660989264472, "eval_recall": 0.9333333333333333, "eval_runtime": 6.2433, "eval_samples_per_second": 91.298, "eval_steps_per_second": 22.905, "step": 250}, {"epoch": 1.0526315789473684, "grad_norm": 0.1161445751786232, "learning_rate": 2.9297041420118343e-05, "loss": 0.0949, "step": 300}, {"epoch": 1.4035087719298245, "grad_norm": 0.19000214338302612, "learning_rate": 2.894201183431953e-05, "loss": 0.0866, "step": 400}, {"epoch": 1.7543859649122808, "grad_norm": 12.602439880371094, "learning_rate": 2.858698224852071e-05, "loss": 0.0928, "step": 500}, {"epoch": 1.7543859649122808, "eval_accuracy": 0.9719298245614035, "eval_f1": 0.9719173779699221, "eval_loss": 0.09560095518827438, "eval_precision": 0.9727679727679729, "eval_recall": 0.9719298245614035, "eval_runtime": 6.6068, "eval_samples_per_second": 86.275, "eval_steps_per_second": 21.644, "step": 500}, {"epoch": 2.1052631578947367, "grad_norm": 0.06775994598865509, "learning_rate": 2.8231952662721894e-05, "loss": 0.0686, "step": 600}, {"epoch": 2.456140350877193, "grad_norm": 0.28925344347953796, "learning_rate": 2.7876923076923077e-05, "loss": 0.0724, "step": 700}, {"epoch": 2.6315789473684212, "eval_accuracy": 0.9754385964912281, "eval_f1": 0.9754277057236818, "eval_loss": 0.08033046871423721, "eval_precision": 0.9762829762829764, "eval_recall": 0.9754385964912281, "eval_runtime": 6.2347, "eval_samples_per_second": 91.424, "eval_steps_per_second": 22.936, "step": 750}, {"epoch": 2.807017543859649, "grad_norm": 0.034019388258457184, "learning_rate": 2.752189349112426e-05, "loss": 0.0594, "step": 800}, {"epoch": 3.1578947368421053, "grad_norm": 0.03789934143424034, "learning_rate": 2.7170414201183432e-05, "loss": 0.0666, "step": 900}, {"epoch": 3.5087719298245617, "grad_norm": 0.02472812496125698, "learning_rate": 2.6815384615384615e-05, "loss": 0.049, "step": 1000}, {"epoch": 3.5087719298245617, "eval_accuracy": 0.9824561403508771, "eval_f1": 0.9824552763447879, "eval_loss": 0.061074621975421906, "eval_precision": 0.9825511950645864, "eval_recall": 0.9824561403508771, "eval_runtime": 6.1224, "eval_samples_per_second": 93.101, "eval_steps_per_second": 23.357, "step": 1000}, {"epoch": 3.8596491228070176, "grad_norm": 10.881855964660645, "learning_rate": 2.6460355029585802e-05, "loss": 0.0467, "step": 1100}, {"epoch": 4.2105263157894735, "grad_norm": 0.07170745730400085, "learning_rate": 2.6105325443786982e-05, "loss": 0.0337, "step": 1200}, {"epoch": 4.385964912280702, "eval_accuracy": 0.9824561403508771, "eval_f1": 0.9824552763447879, "eval_loss": 0.08872972428798676, "eval_precision": 0.9825511950645864, "eval_recall": 0.9824561403508771, "eval_runtime": 6.5743, "eval_samples_per_second": 86.701, "eval_steps_per_second": 21.751, "step": 1250}, {"epoch": 4.56140350877193, "grad_norm": 0.02985038235783577, "learning_rate": 2.5750295857988166e-05, "loss": 0.0284, "step": 1300}, {"epoch": 4.912280701754386, "grad_norm": 0.015634162351489067, "learning_rate": 2.539526627218935e-05, "loss": 0.0314, "step": 1400}, {"epoch": 5.2631578947368425, "grad_norm": 0.0125426659360528, "learning_rate": 2.5040236686390533e-05, "loss": 0.0248, "step": 1500}, {"epoch": 5.2631578947368425, "eval_accuracy": 0.9859649122807017, "eval_f1": 0.9859642210758303, "eval_loss": 0.05325772985816002, "eval_precision": 0.98606065830142, "eval_recall": 0.9859649122807017, "eval_runtime": 6.5455, "eval_samples_per_second": 87.083, "eval_steps_per_second": 21.847, "step": 1500}, {"epoch": 5.614035087719298, "grad_norm": 0.011861481703817844, "learning_rate": 2.4685207100591716e-05, "loss": 0.0523, "step": 1600}, {"epoch": 5.964912280701754, "grad_norm": 0.00879656057804823, "learning_rate": 2.43301775147929e-05, "loss": 0.0162, "step": 1700}, {"epoch": 6.140350877192983, "eval_accuracy": 0.987719298245614, "eval_f1": 0.9877189580505461, "eval_loss": 0.07235293835401535, "eval_precision": 0.9877733451536642, "eval_recall": 0.987719298245614, "eval_runtime": 6.4374, "eval_samples_per_second": 88.545, "eval_steps_per_second": 22.214, "step": 1750}, {"epoch": 6.315789473684211, "grad_norm": 0.02419952116906643, "learning_rate": 2.3975147928994083e-05, "loss": 0.0179, "step": 1800}, {"epoch": 6.666666666666667, "grad_norm": 0.014598690904676914, "learning_rate": 2.3620118343195267e-05, "loss": 0.0235, "step": 1900}, {"epoch": 7.017543859649122, "grad_norm": 0.005418827757239342, "learning_rate": 2.326508875739645e-05, "loss": 0.0261, "step": 2000}, {"epoch": 7.017543859649122, "eval_accuracy": 0.9614035087719298, "eval_f1": 0.9613559322033898, "eval_loss": 0.2287500500679016, "eval_precision": 0.963686978038973, "eval_recall": 0.9614035087719298, "eval_runtime": 6.6723, "eval_samples_per_second": 85.428, "eval_steps_per_second": 21.432, "step": 2000}, {"epoch": 7.368421052631579, "grad_norm": 0.010170853696763515, "learning_rate": 2.2910059171597634e-05, "loss": 0.013, "step": 2100}, {"epoch": 7.719298245614035, "grad_norm": 0.003839484415948391, "learning_rate": 2.2555029585798817e-05, "loss": 0.0059, "step": 2200}, {"epoch": 7.894736842105263, "eval_accuracy": 0.956140350877193, "eval_f1": 0.9560688224286387, "eval_loss": 0.3189088702201843, "eval_precision": 0.959130564092396, "eval_recall": 0.956140350877193, "eval_runtime": 6.3081, "eval_samples_per_second": 90.36, "eval_steps_per_second": 22.669, "step": 2250}, {"epoch": 8.070175438596491, "grad_norm": 0.021540872752666473, "learning_rate": 2.22e-05, "loss": 0.017, "step": 2300}, {"epoch": 8.421052631578947, "grad_norm": 0.008635940961539745, "learning_rate": 2.1844970414201184e-05, "loss": 0.0044, "step": 2400}, {"epoch": 8.771929824561404, "grad_norm": 0.0036812114994972944, "learning_rate": 2.1489940828402368e-05, "loss": 0.0122, "step": 2500}, {"epoch": 8.771929824561404, "eval_accuracy": 0.9789473684210527, "eval_f1": 0.978945035460993, "eval_loss": 0.11868457496166229, "eval_precision": 0.9791597383882054, "eval_recall": 0.9789473684210527, "eval_runtime": 6.6767, "eval_samples_per_second": 85.371, "eval_steps_per_second": 21.418, "step": 2500}, {"epoch": 9.12280701754386, "grad_norm": 0.002657294739037752, "learning_rate": 2.113491124260355e-05, "loss": 0.003, "step": 2600}, {"epoch": 9.473684210526315, "grad_norm": 0.012113603763282299, "learning_rate": 2.0779881656804735e-05, "loss": 0.0058, "step": 2700}, {"epoch": 9.649122807017545, "eval_accuracy": 0.9789473684210527, "eval_f1": 0.978945035460993, "eval_loss": 0.11368831247091293, "eval_precision": 0.9791597383882054, "eval_recall": 0.9789473684210527, "eval_runtime": 6.2483, "eval_samples_per_second": 91.224, "eval_steps_per_second": 22.886, "step": 2750}, {"epoch": 9.824561403508772, "grad_norm": 0.004273245111107826, "learning_rate": 2.042485207100592e-05, "loss": 0.0017, "step": 2800}, {"epoch": 10.175438596491228, "grad_norm": 0.0043294369243085384, "learning_rate": 2.007337278106509e-05, "loss": 0.0283, "step": 2900}, {"epoch": 10.526315789473685, "grad_norm": 0.0020223117899149656, "learning_rate": 1.9718343195266273e-05, "loss": 0.0002, "step": 3000}, {"epoch": 10.526315789473685, "eval_accuracy": 0.9754385964912281, "eval_f1": 0.9754277057236818, "eval_loss": 0.16371160745620728, "eval_precision": 0.9762829762829764, "eval_recall": 0.9754385964912281, "eval_runtime": 6.144, "eval_samples_per_second": 92.774, "eval_steps_per_second": 23.275, "step": 3000}, {"epoch": 10.87719298245614, "grad_norm": 37.72980880737305, "learning_rate": 1.9363313609467453e-05, "loss": 0.0038, "step": 3100}, {"epoch": 11.228070175438596, "grad_norm": 0.0022025289945304394, "learning_rate": 1.900828402366864e-05, "loss": 0.0029, "step": 3200}, {"epoch": 11.403508771929825, "eval_accuracy": 0.9701754385964912, "eval_f1": 0.9701643271270617, "eval_loss": 0.19839169085025787, "eval_precision": 0.9708768987966068, "eval_recall": 0.9701754385964912, "eval_runtime": 6.4238, "eval_samples_per_second": 88.732, "eval_steps_per_second": 22.261, "step": 3250}, {"epoch": 11.578947368421053, "grad_norm": 0.6888498663902283, "learning_rate": 1.8653254437869824e-05, "loss": 0.0075, "step": 3300}, {"epoch": 11.929824561403509, "grad_norm": 0.001643746392801404, "learning_rate": 1.8298224852071007e-05, "loss": 0.0124, "step": 3400}, {"epoch": 12.280701754385966, "grad_norm": 0.0012721068924292922, "learning_rate": 1.794674556213018e-05, "loss": 0.0072, "step": 3500}, {"epoch": 12.280701754385966, "eval_accuracy": 0.9719298245614035, "eval_f1": 0.9719173779699221, "eval_loss": 0.1901237815618515, "eval_precision": 0.9727679727679729, "eval_recall": 0.9719298245614035, "eval_runtime": 6.9253, "eval_samples_per_second": 82.307, "eval_steps_per_second": 20.649, "step": 3500}, {"epoch": 12.631578947368421, "grad_norm": 0.45498746633529663, "learning_rate": 1.7591715976331362e-05, "loss": 0.0104, "step": 3600}, {"epoch": 12.982456140350877, "grad_norm": 0.056542541831731796, "learning_rate": 1.7236686390532545e-05, "loss": 0.0098, "step": 3700}, {"epoch": 13.157894736842104, "eval_accuracy": 0.9578947368421052, "eval_f1": 0.9578319195107516, "eval_loss": 0.3247097134590149, "eval_precision": 0.9606395759279671, "eval_recall": 0.9578947368421052, "eval_runtime": 6.206, "eval_samples_per_second": 91.847, "eval_steps_per_second": 23.042, "step": 3750}, {"epoch": 13.333333333333334, "grad_norm": 0.0013171443715691566, "learning_rate": 1.688165680473373e-05, "loss": 0.002, "step": 3800}, {"epoch": 13.68421052631579, "grad_norm": 0.0030671427957713604, "learning_rate": 1.6526627218934912e-05, "loss": 0.0092, "step": 3900}, {"epoch": 14.035087719298245, "grad_norm": 0.0029420414939522743, "learning_rate": 1.6171597633136092e-05, "loss": 0.0399, "step": 4000}, {"epoch": 14.035087719298245, "eval_accuracy": 0.9736842105263158, "eval_f1": 0.973670514980091, "eval_loss": 0.15603210031986237, "eval_precision": 0.9746718318199763, "eval_recall": 0.9736842105263158, "eval_runtime": 6.4711, "eval_samples_per_second": 88.084, "eval_steps_per_second": 22.098, "step": 4000}, {"epoch": 14.385964912280702, "grad_norm": 0.0083963917568326, "learning_rate": 1.581656804733728e-05, "loss": 0.0016, "step": 4100}, {"epoch": 14.736842105263158, "grad_norm": 0.0010471029672771692, "learning_rate": 1.546153846153846e-05, "loss": 0.0043, "step": 4200}, {"epoch": 14.912280701754385, "eval_accuracy": 0.9754385964912281, "eval_f1": 0.9754277057236818, "eval_loss": 0.2102290242910385, "eval_precision": 0.9762829762829764, "eval_recall": 0.9754385964912281, "eval_runtime": 6.3561, "eval_samples_per_second": 89.677, "eval_steps_per_second": 22.498, "step": 4250}, {"epoch": 15.087719298245615, "grad_norm": 0.004470640793442726, "learning_rate": 1.5106508875739645e-05, "loss": 0.0143, "step": 4300}, {"epoch": 15.43859649122807, "grad_norm": 0.1367305964231491, "learning_rate": 1.4751479289940828e-05, "loss": 0.0074, "step": 4400}, {"epoch": 15.789473684210526, "grad_norm": 0.0023705989588052034, "learning_rate": 1.4396449704142012e-05, "loss": 0.0001, "step": 4500}, {"epoch": 15.789473684210526, "eval_accuracy": 0.9719298245614035, "eval_f1": 0.9719128806543805, "eval_loss": 0.21511125564575195, "eval_precision": 0.9730713695096819, "eval_recall": 0.9719298245614035, "eval_runtime": 5.7084, "eval_samples_per_second": 99.853, "eval_steps_per_second": 25.051, "step": 4500}, {"epoch": 16.140350877192983, "grad_norm": 0.001150684547610581, "learning_rate": 1.4041420118343195e-05, "loss": 0.0001, "step": 4600}, {"epoch": 16.49122807017544, "grad_norm": 0.000751435523852706, "learning_rate": 1.3686390532544379e-05, "loss": 0.0001, "step": 4700}, {"epoch": 16.666666666666668, "eval_accuracy": 0.9719298245614035, "eval_f1": 0.9719128806543805, "eval_loss": 0.22369560599327087, "eval_precision": 0.9730713695096819, "eval_recall": 0.9719298245614035, "eval_runtime": 6.2044, "eval_samples_per_second": 91.87, "eval_steps_per_second": 23.048, "step": 4750}, {"epoch": 16.842105263157894, "grad_norm": 0.0006915325648151338, "learning_rate": 1.3331360946745564e-05, "loss": 0.0112, "step": 4800}, {"epoch": 17.19298245614035, "grad_norm": 0.0009645046084187925, "learning_rate": 1.2976331360946747e-05, "loss": 0.0155, "step": 4900}, {"epoch": 17.54385964912281, "grad_norm": 0.0012695580953732133, "learning_rate": 1.262130177514793e-05, "loss": 0.0075, "step": 5000}, {"epoch": 17.54385964912281, "eval_accuracy": 0.9754385964912281, "eval_f1": 0.9754358747044918, "eval_loss": 0.16844278573989868, "eval_precision": 0.9756494106344457, "eval_recall": 0.9754385964912281, "eval_runtime": 6.1537, "eval_samples_per_second": 92.627, "eval_steps_per_second": 23.238, "step": 5000}, {"epoch": 17.894736842105264, "grad_norm": 0.0013691030908375978, "learning_rate": 1.2266272189349113e-05, "loss": 0.0057, "step": 5100}, {"epoch": 18.24561403508772, "grad_norm": 0.0008323328220285475, "learning_rate": 1.1911242603550296e-05, "loss": 0.0028, "step": 5200}, {"epoch": 18.42105263157895, "eval_accuracy": 0.9298245614035088, "eval_f1": 0.9295112781954887, "eval_loss": 0.5530291795730591, "eval_precision": 0.9376041914741605, "eval_recall": 0.9298245614035088, "eval_runtime": 6.596, "eval_samples_per_second": 86.416, "eval_steps_per_second": 21.68, "step": 5250}, {"epoch": 18.596491228070175, "grad_norm": 0.0009651682921685278, "learning_rate": 1.155621301775148e-05, "loss": 0.0038, "step": 5300}, {"epoch": 18.94736842105263, "grad_norm": 0.000534499529749155, "learning_rate": 1.1201183431952663e-05, "loss": 0.0, "step": 5400}, {"epoch": 19.29824561403509, "grad_norm": 0.000577225349843502, "learning_rate": 1.0846153846153847e-05, "loss": 0.0, "step": 5500}, {"epoch": 19.29824561403509, "eval_accuracy": 0.9736842105263158, "eval_f1": 0.973677648167133, "eval_loss": 0.19019865989685059, "eval_precision": 0.9741570541259981, "eval_recall": 0.9736842105263158, "eval_runtime": 6.2749, "eval_samples_per_second": 90.838, "eval_steps_per_second": 22.789, "step": 5500}, {"epoch": 19.649122807017545, "grad_norm": 0.00047712051309645176, "learning_rate": 1.049112426035503e-05, "loss": 0.0002, "step": 5600}, {"epoch": 20.0, "grad_norm": 0.0006468616193160415, "learning_rate": 1.0136094674556212e-05, "loss": 0.0, "step": 5700}, {"epoch": 20.17543859649123, "eval_accuracy": 0.9736842105263158, "eval_f1": 0.9736821854559446, "eval_loss": 0.17857687175273895, "eval_precision": 0.9738300492610837, "eval_recall": 0.9736842105263158, "eval_runtime": 6.0998, "eval_samples_per_second": 93.446, "eval_steps_per_second": 23.444, "step": 5750}, {"epoch": 20.350877192982455, "grad_norm": 0.0004253752122167498, "learning_rate": 9.784615384615385e-06, "loss": 0.0035, "step": 5800}, {"epoch": 20.70175438596491, "grad_norm": 0.0015860761050134897, "learning_rate": 9.429585798816568e-06, "loss": 0.0002, "step": 5900}, {"epoch": 21.05263157894737, "grad_norm": 0.0004557235515676439, "learning_rate": 9.074556213017752e-06, "loss": 0.0113, "step": 6000}, {"epoch": 21.05263157894737, "eval_accuracy": 0.968421052631579, "eval_f1": 0.9683961508606348, "eval_loss": 0.25653403997421265, "eval_precision": 0.9699020612827131, "eval_recall": 0.968421052631579, "eval_runtime": 6.0805, "eval_samples_per_second": 93.743, "eval_steps_per_second": 23.518, "step": 6000}, {"epoch": 21.403508771929825, "grad_norm": 0.0017673399997875094, "learning_rate": 8.719526627218935e-06, "loss": 0.0, "step": 6100}, {"epoch": 21.75438596491228, "grad_norm": 0.0020748828537762165, "learning_rate": 8.364497041420119e-06, "loss": 0.0003, "step": 6200}, {"epoch": 21.92982456140351, "eval_accuracy": 0.9771929824561404, "eval_f1": 0.9771895422824618, "eval_loss": 0.19052208960056305, "eval_precision": 0.9774810288755298, "eval_recall": 0.9771929824561404, "eval_runtime": 6.4955, "eval_samples_per_second": 87.753, "eval_steps_per_second": 22.015, "step": 6250}, {"epoch": 22.105263157894736, "grad_norm": 0.00059329136274755, "learning_rate": 8.009467455621302e-06, "loss": 0.0025, "step": 6300}, {"epoch": 22.45614035087719, "grad_norm": 0.00041389118996448815, "learning_rate": 7.654437869822486e-06, "loss": 0.007, "step": 6400}, {"epoch": 22.80701754385965, "grad_norm": 0.0004073083691764623, "learning_rate": 7.2994082840236686e-06, "loss": 0.0062, "step": 6500}, {"epoch": 22.80701754385965, "eval_accuracy": 0.9771929824561404, "eval_f1": 0.9771923506652999, "eval_loss": 0.1611858755350113, "eval_precision": 0.9772458628841609, "eval_recall": 0.9771929824561404, "eval_runtime": 6.6335, "eval_samples_per_second": 85.928, "eval_steps_per_second": 21.557, "step": 6500}], "logging_steps": 100, "max_steps": 8550, "num_input_tokens_seen": 0, "num_train_epochs": 30, "save_steps": 500, "stateful_callbacks": {"EarlyStoppingCallback": {"args": {"early_stopping_patience": 20, "early_stopping_threshold": 0.0}, "attributes": {"early_stopping_patience_counter": 19}}, "TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 1.2249742176157696e+16, "train_batch_size": 4, "trial_name": null, "trial_params": null}