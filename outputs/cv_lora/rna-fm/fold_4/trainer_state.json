{"best_global_step": 700, "best_metric": 0.7782101167315176, "best_model_checkpoint": "outputs/cv_lora/rna-fm/fold_4/checkpoint-600", "epoch": 20.0, "eval_steps": 100, "global_step": 1020, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.9803921568627451, "grad_norm": 2.6760504245758057, "learning_rate": 5e-05, "loss": 2.7656, "step": 50}, {"epoch": 1.9607843137254903, "grad_norm": 7.012648582458496, "learning_rate": 0.0001, "loss": 2.7224, "step": 100}, {"epoch": 1.9607843137254903, "eval_accuracy": 0.6206896551724138, "eval_f1": 0.7049808429118773, "eval_loss": 0.6688827872276306, "eval_precision": 0.575, "eval_recall": 0.9108910891089109, "eval_runtime": 5.5522, "eval_samples_per_second": 36.562, "eval_steps_per_second": 9.186, "step": 100}, {"epoch": 2.9411764705882355, "grad_norm": 8.848884582519531, "learning_rate": 9.456521739130435e-05, "loss": 2.433, "step": 150}, {"epoch": 3.9215686274509802, "grad_norm": 6.1549530029296875, "learning_rate": 8.923913043478261e-05, "loss": 2.126, "step": 200}, {"epoch": 3.9215686274509802, "eval_accuracy": 0.6157635467980296, "eval_f1": 0.7194244604316546, "eval_loss": 0.712141215801239, "eval_precision": 0.5649717514124294, "eval_recall": 0.9900990099009901, "eval_runtime": 5.4224, "eval_samples_per_second": 37.438, "eval_steps_per_second": 9.405, "step": 200}, {"epoch": 4.901960784313726, "grad_norm": 3.556424856185913, "learning_rate": 8.391304347826088e-05, "loss": 2.0525, "step": 250}, {"epoch": 5.882352941176471, "grad_norm": 7.906951904296875, "learning_rate": 7.847826086956522e-05, "loss": 1.9344, "step": 300}, {"epoch": 5.882352941176471, "eval_accuracy": 0.6502463054187192, "eval_f1": 0.7380073800738007, "eval_loss": 0.6769111752510071, "eval_precision": 0.5882352941176471, "eval_recall": 0.9900990099009901, "eval_runtime": 3.5005, "eval_samples_per_second": 57.992, "eval_steps_per_second": 14.569, "step": 300}, {"epoch": 6.862745098039216, "grad_norm": 6.130320072174072, "learning_rate": 7.304347826086957e-05, "loss": 1.843, "step": 350}, {"epoch": 7.8431372549019605, "grad_norm": 6.534769058227539, "learning_rate": 6.760869565217392e-05, "loss": 1.7386, "step": 400}, {"epoch": 7.8431372549019605, "eval_accuracy": 0.6354679802955665, "eval_f1": 0.7299270072992701, "eval_loss": 0.8146241903305054, "eval_precision": 0.5780346820809249, "eval_recall": 0.9900990099009901, "eval_runtime": 4.7257, "eval_samples_per_second": 42.957, "eval_steps_per_second": 10.792, "step": 400}, {"epoch": 8.823529411764707, "grad_norm": 6.23035192489624, "learning_rate": 6.217391304347826e-05, "loss": 1.7336, "step": 450}, {"epoch": 9.803921568627452, "grad_norm": 5.028354167938232, "learning_rate": 5.673913043478262e-05, "loss": 1.6445, "step": 500}, {"epoch": 9.803921568627452, "eval_accuracy": 0.6600985221674877, "eval_f1": 0.7434944237918215, "eval_loss": 0.671751856803894, "eval_precision": 0.5952380952380952, "eval_recall": 0.9900990099009901, "eval_runtime": 4.9155, "eval_samples_per_second": 41.298, "eval_steps_per_second": 10.375, "step": 500}, {"epoch": 10.784313725490197, "grad_norm": 8.455183982849121, "learning_rate": 5.141304347826087e-05, "loss": 1.6019, "step": 550}, {"epoch": 11.764705882352942, "grad_norm": 13.91469669342041, "learning_rate": 4.597826086956522e-05, "loss": 1.5784, "step": 600}, {"epoch": 11.764705882352942, "eval_accuracy": 0.6995073891625616, "eval_f1": 0.7662835249042146, "eval_loss": 0.5706841349601746, "eval_precision": 0.625, "eval_recall": 0.9900990099009901, "eval_runtime": 5.2611, "eval_samples_per_second": 38.585, "eval_steps_per_second": 9.694, "step": 600}, {"epoch": 12.745098039215687, "grad_norm": 6.885512351989746, "learning_rate": 4.054347826086957e-05, "loss": 1.552, "step": 650}, {"epoch": 13.72549019607843, "grad_norm": 45.295223236083984, "learning_rate": 3.510869565217392e-05, "loss": 1.4816, "step": 700}, {"epoch": 13.72549019607843, "eval_accuracy": 0.7192118226600985, "eval_f1": 0.7782101167315176, "eval_loss": 0.5697877407073975, "eval_precision": 0.6410256410256411, "eval_recall": 0.9900990099009901, "eval_runtime": 5.1678, "eval_samples_per_second": 39.282, "eval_steps_per_second": 9.869, "step": 700}, {"epoch": 14.705882352941176, "grad_norm": 13.44842529296875, "learning_rate": 2.967391304347826e-05, "loss": 1.504, "step": 750}, {"epoch": 15.686274509803921, "grad_norm": 15.393701553344727, "learning_rate": 2.423913043478261e-05, "loss": 1.4758, "step": 800}, {"epoch": 15.686274509803921, "eval_accuracy": 0.6896551724137931, "eval_f1": 0.7604562737642585, "eval_loss": 0.6609935164451599, "eval_precision": 0.6172839506172839, "eval_recall": 0.9900990099009901, "eval_runtime": 5.1868, "eval_samples_per_second": 39.138, "eval_steps_per_second": 9.833, "step": 800}, {"epoch": 16.666666666666668, "grad_norm": 3.987377882003784, "learning_rate": 1.8804347826086958e-05, "loss": 1.4405, "step": 850}, {"epoch": 17.647058823529413, "grad_norm": 5.698648452758789, "learning_rate": 1.3369565217391305e-05, "loss": 1.584, "step": 900}, {"epoch": 17.647058823529413, "eval_accuracy": 0.6847290640394089, "eval_f1": 0.7575757575757575, "eval_loss": 0.6524775624275208, "eval_precision": 0.6134969325153374, "eval_recall": 0.9900990099009901, "eval_runtime": 3.7868, "eval_samples_per_second": 53.607, "eval_steps_per_second": 13.468, "step": 900}, {"epoch": 18.627450980392158, "grad_norm": 7.774500370025635, "learning_rate": 7.934782608695653e-06, "loss": 1.4139, "step": 950}, {"epoch": 19.607843137254903, "grad_norm": 9.832337379455566, "learning_rate": 2.5e-06, "loss": 1.428, "step": 1000}, {"epoch": 19.607843137254903, "eval_accuracy": 0.6945812807881774, "eval_f1": 0.7633587786259542, "eval_loss": 0.6556437611579895, "eval_precision": 0.6211180124223602, "eval_recall": 0.9900990099009901, "eval_runtime": 4.6951, "eval_samples_per_second": 43.236, "eval_steps_per_second": 10.862, "step": 1000}, {"epoch": 20.0, "step": 1020, "total_flos": 0.0, "train_loss": 1.795909148571538, "train_runtime": 1164.0356, "train_samples_per_second": 13.986, "train_steps_per_second": 0.876}], "logging_steps": 50, "max_steps": 1020, "num_input_tokens_seen": 0, "num_train_epochs": 20, "save_steps": 200, "stateful_callbacks": {"EarlyStoppingCallback": {"args": {"early_stopping_patience": 8, "early_stopping_threshold": 0.0}, "attributes": {"early_stopping_patience_counter": 3}}, "TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": true}, "attributes": {}}}, "total_flos": 0.0, "train_batch_size": 4, "trial_name": null, "trial_params": null}