{"best_global_step": 300, "best_metric": 0.7499999999999999, "best_model_checkpoint": "outputs/cv_lora/rna-fm/fold_1/checkpoint-200", "epoch": 7.8431372549019605, "eval_steps": 100, "global_step": 400, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.9803921568627451, "grad_norm": 4.779024600982666, "learning_rate": 5e-05, "loss": 2.7616, "step": 50}, {"epoch": 1.9607843137254903, "grad_norm": 4.523606300354004, "learning_rate": 9.900000000000001e-05, "loss": 2.737, "step": 100}, {"epoch": 1.9607843137254903, "eval_accuracy": 0.5, "eval_f1": 0.6666666666666666, "eval_loss": 0.6851722598075867, "eval_precision": 0.5, "eval_recall": 1.0, "eval_runtime": 5.0382, "eval_samples_per_second": 40.49, "eval_steps_per_second": 10.123, "step": 100}, {"epoch": 2.9411764705882355, "grad_norm": 19.139137268066406, "learning_rate": 9.467391304347827e-05, "loss": 2.407, "step": 150}, {"epoch": 3.9215686274509802, "grad_norm": 12.618846893310547, "learning_rate": 8.923913043478261e-05, "loss": 2.1601, "step": 200}, {"epoch": 3.9215686274509802, "eval_accuracy": 0.6470588235294118, "eval_f1": 0.7391304347826086, "eval_loss": 0.6591963171958923, "eval_precision": 0.5862068965517241, "eval_recall": 1.0, "eval_runtime": 7.2748, "eval_samples_per_second": 28.042, "eval_steps_per_second": 7.01, "step": 200}, {"epoch": 4.901960784313726, "grad_norm": 7.801342964172363, "learning_rate": 8.380434782608696e-05, "loss": 2.0664, "step": 250}, {"epoch": 5.882352941176471, "grad_norm": 6.608046054840088, "learning_rate": 7.847826086956522e-05, "loss": 1.8428, "step": 300}, {"epoch": 5.882352941176471, "eval_accuracy": 0.6666666666666666, "eval_f1": 0.7499999999999999, "eval_loss": 0.634864330291748, "eval_precision": 0.6, "eval_recall": 1.0, "eval_runtime": 7.1494, "eval_samples_per_second": 28.534, "eval_steps_per_second": 7.133, "step": 300}, {"epoch": 6.862745098039216, "grad_norm": 12.53113842010498, "learning_rate": 7.304347826086957e-05, "loss": 1.9143, "step": 350}, {"epoch": 7.8431372549019605, "grad_norm": 15.289412498474121, "learning_rate": 6.760869565217392e-05, "loss": 1.7486, "step": 400}, {"epoch": 7.8431372549019605, "eval_accuracy": 0.6568627450980392, "eval_f1": 0.7445255474452555, "eval_loss": 0.6426007151603699, "eval_precision": 0.5930232558139535, "eval_recall": 1.0, "eval_runtime": 6.9757, "eval_samples_per_second": 29.244, "eval_steps_per_second": 7.311, "step": 400}], "logging_steps": 50, "max_steps": 1020, "num_input_tokens_seen": 0, "num_train_epochs": 20, "save_steps": 200, "stateful_callbacks": {"EarlyStoppingCallback": {"args": {"early_stopping_patience": 8, "early_stopping_threshold": 0.0}, "attributes": {"early_stopping_patience_counter": 1}}, "TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 0.0, "train_batch_size": 4, "trial_name": null, "trial_params": null}