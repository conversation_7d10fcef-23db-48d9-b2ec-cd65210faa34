{"best_global_step": 200, "best_metric": 0.7734375, "best_model_checkpoint": "outputs/cv_lora/rna-fm/fold_2/checkpoint-200", "epoch": 3.9215686274509802, "eval_steps": 100, "global_step": 200, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.9803921568627451, "grad_norm": 4.744022369384766, "learning_rate": 5e-05, "loss": 2.7665, "step": 50}, {"epoch": 1.9607843137254903, "grad_norm": 1.672065258026123, "learning_rate": 0.0001, "loss": 2.7323, "step": 100}, {"epoch": 1.9607843137254903, "eval_accuracy": 0.625615763546798, "eval_f1": 0.6910569105691058, "eval_loss": 0.6658568978309631, "eval_precision": 0.5902777777777778, "eval_recall": 0.8333333333333334, "eval_runtime": 7.6627, "eval_samples_per_second": 26.492, "eval_steps_per_second": 6.656, "step": 100}, {"epoch": 2.9411764705882355, "grad_norm": 3.523186445236206, "learning_rate": 9.456521739130435e-05, "loss": 2.4066, "step": 150}, {"epoch": 3.9215686274509802, "grad_norm": 16.822298049926758, "learning_rate": 8.923913043478261e-05, "loss": 2.1403, "step": 200}, {"epoch": 3.9215686274509802, "eval_accuracy": 0.7142857142857143, "eval_f1": 0.7734375, "eval_loss": 0.5559579730033875, "eval_precision": 0.6428571428571429, "eval_recall": 0.9705882352941176, "eval_runtime": 6.1073, "eval_samples_per_second": 33.239, "eval_steps_per_second": 8.351, "step": 200}], "logging_steps": 50, "max_steps": 1020, "num_input_tokens_seen": 0, "num_train_epochs": 20, "save_steps": 200, "stateful_callbacks": {"EarlyStoppingCallback": {"args": {"early_stopping_patience": 8, "early_stopping_threshold": 0.0}, "attributes": {"early_stopping_patience_counter": 0}}, "TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 0.0, "train_batch_size": 4, "trial_name": null, "trial_params": null}