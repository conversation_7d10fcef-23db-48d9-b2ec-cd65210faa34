{"best_global_step": 600, "best_metric": 0.7968127490039841, "best_model_checkpoint": "outputs/cv_lora/rna-fm/fold_2/checkpoint-600", "epoch": 15.686274509803921, "eval_steps": 100, "global_step": 800, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.9803921568627451, "grad_norm": 4.744022369384766, "learning_rate": 5e-05, "loss": 2.7665, "step": 50}, {"epoch": 1.9607843137254903, "grad_norm": 1.672065258026123, "learning_rate": 0.0001, "loss": 2.7323, "step": 100}, {"epoch": 1.9607843137254903, "eval_accuracy": 0.625615763546798, "eval_f1": 0.6910569105691058, "eval_loss": 0.6658568978309631, "eval_precision": 0.5902777777777778, "eval_recall": 0.8333333333333334, "eval_runtime": 7.6627, "eval_samples_per_second": 26.492, "eval_steps_per_second": 6.656, "step": 100}, {"epoch": 2.9411764705882355, "grad_norm": 3.523186445236206, "learning_rate": 9.456521739130435e-05, "loss": 2.4066, "step": 150}, {"epoch": 3.9215686274509802, "grad_norm": 16.822298049926758, "learning_rate": 8.923913043478261e-05, "loss": 2.1403, "step": 200}, {"epoch": 3.9215686274509802, "eval_accuracy": 0.7142857142857143, "eval_f1": 0.7734375, "eval_loss": 0.5559579730033875, "eval_precision": 0.6428571428571429, "eval_recall": 0.9705882352941176, "eval_runtime": 6.1073, "eval_samples_per_second": 33.239, "eval_steps_per_second": 8.351, "step": 200}, {"epoch": 4.901960784313726, "grad_norm": 10.913190841674805, "learning_rate": 8.380434782608696e-05, "loss": 1.9884, "step": 250}, {"epoch": 5.882352941176471, "grad_norm": 12.839181900024414, "learning_rate": 7.847826086956522e-05, "loss": 2.0841, "step": 300}, {"epoch": 5.882352941176471, "eval_accuracy": 0.6847290640394089, "eval_f1": 0.7575757575757576, "eval_loss": 0.6540396809577942, "eval_precision": 0.6172839506172839, "eval_recall": 0.9803921568627451, "eval_runtime": 6.6494, "eval_samples_per_second": 30.529, "eval_steps_per_second": 7.67, "step": 300}, {"epoch": 6.862745098039216, "grad_norm": 5.413449764251709, "learning_rate": 7.304347826086957e-05, "loss": 1.9467, "step": 350}, {"epoch": 7.8431372549019605, "grad_norm": 26.724430084228516, "learning_rate": 6.760869565217392e-05, "loss": 1.8117, "step": 400}, {"epoch": 7.8431372549019605, "eval_accuracy": 0.7389162561576355, "eval_f1": 0.7888446215139443, "eval_loss": 0.5263354182243347, "eval_precision": 0.6644295302013423, "eval_recall": 0.9705882352941176, "eval_runtime": 7.6101, "eval_samples_per_second": 26.675, "eval_steps_per_second": 6.702, "step": 400}, {"epoch": 8.823529411764707, "grad_norm": 2.663825750350952, "learning_rate": 6.217391304347826e-05, "loss": 1.8457, "step": 450}, {"epoch": 9.803921568627452, "grad_norm": 6.89875602722168, "learning_rate": 5.673913043478262e-05, "loss": 1.8177, "step": 500}, {"epoch": 9.803921568627452, "eval_accuracy": 0.7142857142857143, "eval_f1": 0.7734375, "eval_loss": 0.6080332398414612, "eval_precision": 0.6428571428571429, "eval_recall": 0.9705882352941176, "eval_runtime": 7.6429, "eval_samples_per_second": 26.561, "eval_steps_per_second": 6.673, "step": 500}, {"epoch": 10.784313725490197, "grad_norm": 7.885233402252197, "learning_rate": 5.1304347826086966e-05, "loss": 1.6917, "step": 550}, {"epoch": 11.764705882352942, "grad_norm": 15.120132446289062, "learning_rate": 4.586956521739131e-05, "loss": 1.679, "step": 600}, {"epoch": 11.764705882352942, "eval_accuracy": 0.7487684729064039, "eval_f1": 0.7968127490039841, "eval_loss": 0.5611843466758728, "eval_precision": 0.6711409395973155, "eval_recall": 0.9803921568627451, "eval_runtime": 6.2667, "eval_samples_per_second": 32.394, "eval_steps_per_second": 8.138, "step": 600}, {"epoch": 12.745098039215687, "grad_norm": 8.056962013244629, "learning_rate": 4.0434782608695655e-05, "loss": 1.6558, "step": 650}, {"epoch": 13.72549019607843, "grad_norm": 22.145931243896484, "learning_rate": 3.5e-05, "loss": 1.535, "step": 700}, {"epoch": 13.72549019607843, "eval_accuracy": 0.7438423645320197, "eval_f1": 0.7936507936507936, "eval_loss": 0.6477583050727844, "eval_precision": 0.6666666666666666, "eval_recall": 0.9803921568627451, "eval_runtime": 7.5243, "eval_samples_per_second": 26.979, "eval_steps_per_second": 6.778, "step": 700}, {"epoch": 14.705882352941176, "grad_norm": 7.99900484085083, "learning_rate": 2.9565217391304352e-05, "loss": 1.5968, "step": 750}, {"epoch": 15.686274509803921, "grad_norm": 8.863694190979004, "learning_rate": 2.4130434782608697e-05, "loss": 1.5383, "step": 800}, {"epoch": 15.686274509803921, "eval_accuracy": 0.7438423645320197, "eval_f1": 0.792, "eval_loss": 0.5952096581459045, "eval_precision": 0.668918918918919, "eval_recall": 0.9705882352941176, "eval_runtime": 5.9114, "eval_samples_per_second": 34.341, "eval_steps_per_second": 8.627, "step": 800}], "logging_steps": 50, "max_steps": 1020, "num_input_tokens_seen": 0, "num_train_epochs": 20, "save_steps": 200, "stateful_callbacks": {"EarlyStoppingCallback": {"args": {"early_stopping_patience": 8, "early_stopping_threshold": 0.0}, "attributes": {"early_stopping_patience_counter": 2}}, "TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 0.0, "train_batch_size": 4, "trial_name": null, "trial_params": null}