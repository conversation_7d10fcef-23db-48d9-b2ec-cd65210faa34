{"best_global_step": 1000, "best_metric": 0.7857142857142857, "best_model_checkpoint": "outputs/cv_lora/rna-fm/fold_3/checkpoint-1000", "epoch": 20.0, "eval_steps": 100, "global_step": 1020, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.9803921568627451, "grad_norm": 4.853226184844971, "learning_rate": 5e-05, "loss": 2.7656, "step": 50}, {"epoch": 1.9607843137254903, "grad_norm": 8.087651252746582, "learning_rate": 0.0001, "loss": 2.7343, "step": 100}, {"epoch": 1.9607843137254903, "eval_accuracy": 0.6157635467980296, "eval_f1": 0.7234042553191489, "eval_loss": 0.6694239974021912, "eval_precision": 0.5666666666666667, "eval_recall": 1.0, "eval_runtime": 5.5529, "eval_samples_per_second": 36.557, "eval_steps_per_second": 9.184, "step": 100}, {"epoch": 2.9411764705882355, "grad_norm": 7.690954685211182, "learning_rate": 9.467391304347827e-05, "loss": 2.4238, "step": 150}, {"epoch": 3.9215686274509802, "grad_norm": 4.3174591064453125, "learning_rate": 8.923913043478261e-05, "loss": 2.2165, "step": 200}, {"epoch": 3.9215686274509802, "eval_accuracy": 0.6699507389162561, "eval_f1": 0.7452471482889734, "eval_loss": 0.5860171914100647, "eval_precision": 0.6086956521739131, "eval_recall": 0.9607843137254902, "eval_runtime": 3.9517, "eval_samples_per_second": 51.37, "eval_steps_per_second": 12.906, "step": 200}, {"epoch": 4.901960784313726, "grad_norm": 12.123154640197754, "learning_rate": 8.391304347826088e-05, "loss": 2.0029, "step": 250}, {"epoch": 5.882352941176471, "grad_norm": 5.310046195983887, "learning_rate": 7.847826086956522e-05, "loss": 1.9324, "step": 300}, {"epoch": 5.882352941176471, "eval_accuracy": 0.6748768472906403, "eval_f1": 0.7555555555555554, "eval_loss": 0.6633039712905884, "eval_precision": 0.6071428571428571, "eval_recall": 1.0, "eval_runtime": 5.191, "eval_samples_per_second": 39.106, "eval_steps_per_second": 9.825, "step": 300}, {"epoch": 6.862745098039216, "grad_norm": 8.636027336120605, "learning_rate": 7.304347826086957e-05, "loss": 1.7542, "step": 350}, {"epoch": 7.8431372549019605, "grad_norm": 4.1725172996521, "learning_rate": 6.760869565217392e-05, "loss": 1.7299, "step": 400}, {"epoch": 7.8431372549019605, "eval_accuracy": 0.6699507389162561, "eval_f1": 0.7527675276752768, "eval_loss": 0.6441546678543091, "eval_precision": 0.6035502958579881, "eval_recall": 1.0, "eval_runtime": 5.0438, "eval_samples_per_second": 40.248, "eval_steps_per_second": 10.111, "step": 400}, {"epoch": 8.823529411764707, "grad_norm": 9.233718872070312, "learning_rate": 6.217391304347826e-05, "loss": 1.8706, "step": 450}, {"epoch": 9.803921568627452, "grad_norm": 22.122112274169922, "learning_rate": 5.673913043478262e-05, "loss": 1.6999, "step": 500}, {"epoch": 9.803921568627452, "eval_accuracy": 0.6600985221674877, "eval_f1": 0.7453874538745388, "eval_loss": 0.6645459532737732, "eval_precision": 0.5976331360946746, "eval_recall": 0.9901960784313726, "eval_runtime": 5.2119, "eval_samples_per_second": 38.95, "eval_steps_per_second": 9.785, "step": 500}, {"epoch": 10.784313725490197, "grad_norm": 10.276491165161133, "learning_rate": 5.1304347826086966e-05, "loss": 1.6467, "step": 550}, {"epoch": 11.764705882352942, "grad_norm": 6.976615905761719, "learning_rate": 4.586956521739131e-05, "loss": 1.7327, "step": 600}, {"epoch": 11.764705882352942, "eval_accuracy": 0.6798029556650246, "eval_f1": 0.7565543071161048, "eval_loss": 0.6582632660865784, "eval_precision": 0.6121212121212121, "eval_recall": 0.9901960784313726, "eval_runtime": 4.5843, "eval_samples_per_second": 44.282, "eval_steps_per_second": 11.125, "step": 600}, {"epoch": 12.745098039215687, "grad_norm": 5.498788356781006, "learning_rate": 4.0434782608695655e-05, "loss": 1.6418, "step": 650}, {"epoch": 13.72549019607843, "grad_norm": 13.722646713256836, "learning_rate": 3.5e-05, "loss": 1.5576, "step": 700}, {"epoch": 13.72549019607843, "eval_accuracy": 0.6945812807881774, "eval_f1": 0.763358778625954, "eval_loss": 0.6496696472167969, "eval_precision": 0.625, "eval_recall": 0.9803921568627451, "eval_runtime": 4.9455, "eval_samples_per_second": 41.048, "eval_steps_per_second": 10.312, "step": 700}, {"epoch": 14.705882352941176, "grad_norm": 7.978954792022705, "learning_rate": 2.9565217391304352e-05, "loss": 1.5159, "step": 750}, {"epoch": 15.686274509803921, "grad_norm": 11.623668670654297, "learning_rate": 2.4130434782608697e-05, "loss": 1.4701, "step": 800}, {"epoch": 15.686274509803921, "eval_accuracy": 0.6896551724137931, "eval_f1": 0.7604562737642584, "eval_loss": 0.6667689681053162, "eval_precision": 0.6211180124223602, "eval_recall": 0.9803921568627451, "eval_runtime": 5.0417, "eval_samples_per_second": 40.264, "eval_steps_per_second": 10.116, "step": 800}, {"epoch": 16.666666666666668, "grad_norm": 8.278183937072754, "learning_rate": 1.8695652173913045e-05, "loss": 1.4933, "step": 850}, {"epoch": 17.647058823529413, "grad_norm": 10.193931579589844, "learning_rate": 1.3260869565217394e-05, "loss": 1.4979, "step": 900}, {"epoch": 17.647058823529413, "eval_accuracy": 0.729064039408867, "eval_f1": 0.7843137254901961, "eval_loss": 0.6355466246604919, "eval_precision": 0.6535947712418301, "eval_recall": 0.9803921568627451, "eval_runtime": 5.1942, "eval_samples_per_second": 39.082, "eval_steps_per_second": 9.819, "step": 900}, {"epoch": 18.627450980392158, "grad_norm": 7.945565223693848, "learning_rate": 7.82608695652174e-06, "loss": 1.4198, "step": 950}, {"epoch": 19.607843137254903, "grad_norm": 11.88878059387207, "learning_rate": 2.391304347826087e-06, "loss": 1.4231, "step": 1000}, {"epoch": 19.607843137254903, "eval_accuracy": 0.7339901477832512, "eval_f1": 0.7857142857142857, "eval_loss": 0.635613203048706, "eval_precision": 0.66, "eval_recall": 0.9705882352941176, "eval_runtime": 5.3142, "eval_samples_per_second": 38.2, "eval_steps_per_second": 9.597, "step": 1000}, {"epoch": 20.0, "step": 1020, "total_flos": 0.0, "train_loss": 1.8183996911142386, "train_runtime": 1169.3767, "train_samples_per_second": 13.922, "train_steps_per_second": 0.872}], "logging_steps": 50, "max_steps": 1020, "num_input_tokens_seen": 0, "num_train_epochs": 20, "save_steps": 200, "stateful_callbacks": {"EarlyStoppingCallback": {"args": {"early_stopping_patience": 8, "early_stopping_threshold": 0.0}, "attributes": {"early_stopping_patience_counter": 0}}, "TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": true}, "attributes": {}}}, "total_flos": 0.0, "train_batch_size": 4, "trial_name": null, "trial_params": null}