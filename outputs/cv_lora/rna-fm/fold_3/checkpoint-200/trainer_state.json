{"best_global_step": 200, "best_metric": 0.7452471482889734, "best_model_checkpoint": "outputs/cv_lora/rna-fm/fold_3/checkpoint-200", "epoch": 3.9215686274509802, "eval_steps": 100, "global_step": 200, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.9803921568627451, "grad_norm": 4.853226184844971, "learning_rate": 5e-05, "loss": 2.7656, "step": 50}, {"epoch": 1.9607843137254903, "grad_norm": 8.087651252746582, "learning_rate": 0.0001, "loss": 2.7343, "step": 100}, {"epoch": 1.9607843137254903, "eval_accuracy": 0.6157635467980296, "eval_f1": 0.7234042553191489, "eval_loss": 0.6694239974021912, "eval_precision": 0.5666666666666667, "eval_recall": 1.0, "eval_runtime": 5.5529, "eval_samples_per_second": 36.557, "eval_steps_per_second": 9.184, "step": 100}, {"epoch": 2.9411764705882355, "grad_norm": 7.690954685211182, "learning_rate": 9.467391304347827e-05, "loss": 2.4238, "step": 150}, {"epoch": 3.9215686274509802, "grad_norm": 4.3174591064453125, "learning_rate": 8.923913043478261e-05, "loss": 2.2165, "step": 200}, {"epoch": 3.9215686274509802, "eval_accuracy": 0.6699507389162561, "eval_f1": 0.7452471482889734, "eval_loss": 0.5860171914100647, "eval_precision": 0.6086956521739131, "eval_recall": 0.9607843137254902, "eval_runtime": 3.9517, "eval_samples_per_second": 51.37, "eval_steps_per_second": 12.906, "step": 200}], "logging_steps": 50, "max_steps": 1020, "num_input_tokens_seen": 0, "num_train_epochs": 20, "save_steps": 200, "stateful_callbacks": {"EarlyStoppingCallback": {"args": {"early_stopping_patience": 8, "early_stopping_threshold": 0.0}, "attributes": {"early_stopping_patience_counter": 0}}, "TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 0.0, "train_batch_size": 4, "trial_name": null, "trial_params": null}