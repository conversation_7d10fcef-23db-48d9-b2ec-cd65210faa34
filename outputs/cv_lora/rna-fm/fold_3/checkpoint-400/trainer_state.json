{"best_global_step": 300, "best_metric": 0.7555555555555554, "best_model_checkpoint": "outputs/cv_lora/rna-fm/fold_3/checkpoint-200", "epoch": 7.8431372549019605, "eval_steps": 100, "global_step": 400, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.9803921568627451, "grad_norm": 4.853226184844971, "learning_rate": 5e-05, "loss": 2.7656, "step": 50}, {"epoch": 1.9607843137254903, "grad_norm": 8.087651252746582, "learning_rate": 0.0001, "loss": 2.7343, "step": 100}, {"epoch": 1.9607843137254903, "eval_accuracy": 0.6157635467980296, "eval_f1": 0.7234042553191489, "eval_loss": 0.6694239974021912, "eval_precision": 0.5666666666666667, "eval_recall": 1.0, "eval_runtime": 5.5529, "eval_samples_per_second": 36.557, "eval_steps_per_second": 9.184, "step": 100}, {"epoch": 2.9411764705882355, "grad_norm": 7.690954685211182, "learning_rate": 9.467391304347827e-05, "loss": 2.4238, "step": 150}, {"epoch": 3.9215686274509802, "grad_norm": 4.3174591064453125, "learning_rate": 8.923913043478261e-05, "loss": 2.2165, "step": 200}, {"epoch": 3.9215686274509802, "eval_accuracy": 0.6699507389162561, "eval_f1": 0.7452471482889734, "eval_loss": 0.5860171914100647, "eval_precision": 0.6086956521739131, "eval_recall": 0.9607843137254902, "eval_runtime": 3.9517, "eval_samples_per_second": 51.37, "eval_steps_per_second": 12.906, "step": 200}, {"epoch": 4.901960784313726, "grad_norm": 12.123154640197754, "learning_rate": 8.391304347826088e-05, "loss": 2.0029, "step": 250}, {"epoch": 5.882352941176471, "grad_norm": 5.310046195983887, "learning_rate": 7.847826086956522e-05, "loss": 1.9324, "step": 300}, {"epoch": 5.882352941176471, "eval_accuracy": 0.6748768472906403, "eval_f1": 0.7555555555555554, "eval_loss": 0.6633039712905884, "eval_precision": 0.6071428571428571, "eval_recall": 1.0, "eval_runtime": 5.191, "eval_samples_per_second": 39.106, "eval_steps_per_second": 9.825, "step": 300}, {"epoch": 6.862745098039216, "grad_norm": 8.636027336120605, "learning_rate": 7.304347826086957e-05, "loss": 1.7542, "step": 350}, {"epoch": 7.8431372549019605, "grad_norm": 4.1725172996521, "learning_rate": 6.760869565217392e-05, "loss": 1.7299, "step": 400}, {"epoch": 7.8431372549019605, "eval_accuracy": 0.6699507389162561, "eval_f1": 0.7527675276752768, "eval_loss": 0.6441546678543091, "eval_precision": 0.6035502958579881, "eval_recall": 1.0, "eval_runtime": 5.0438, "eval_samples_per_second": 40.248, "eval_steps_per_second": 10.111, "step": 400}], "logging_steps": 50, "max_steps": 1020, "num_input_tokens_seen": 0, "num_train_epochs": 20, "save_steps": 200, "stateful_callbacks": {"EarlyStoppingCallback": {"args": {"early_stopping_patience": 8, "early_stopping_threshold": 0.0}, "attributes": {"early_stopping_patience_counter": 1}}, "TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 0.0, "train_batch_size": 4, "trial_name": null, "trial_params": null}