{"best_global_step": 800, "best_metric": 0.7952755905511811, "best_model_checkpoint": "outputs/cv_lora/rna-fm/fold_0/checkpoint-800", "epoch": 19.607843137254903, "eval_steps": 100, "global_step": 1000, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.9803921568627451, "grad_norm": 0.9821391105651855, "learning_rate": 5e-05, "loss": 2.7634, "step": 50}, {"epoch": 1.9607843137254903, "grad_norm": 4.143887996673584, "learning_rate": 9.900000000000001e-05, "loss": 2.7244, "step": 100}, {"epoch": 1.9607843137254903, "eval_accuracy": 0.6421568627450981, "eval_f1": 0.6604651162790697, "eval_loss": 0.6610466241836548, "eval_precision": 0.6283185840707964, "eval_recall": 0.696078431372549, "eval_runtime": 5.5762, "eval_samples_per_second": 36.584, "eval_steps_per_second": 9.146, "step": 100}, {"epoch": 2.9411764705882355, "grad_norm": 18.577713012695312, "learning_rate": 9.467391304347827e-05, "loss": 2.3892, "step": 150}, {"epoch": 3.9215686274509802, "grad_norm": 19.903732299804688, "learning_rate": 8.923913043478261e-05, "loss": 2.0885, "step": 200}, {"epoch": 3.9215686274509802, "eval_accuracy": 0.6862745098039216, "eval_f1": 0.7611940298507462, "eval_loss": 0.6123407483100891, "eval_precision": 0.6144578313253012, "eval_recall": 1.0, "eval_runtime": 4.7635, "eval_samples_per_second": 42.826, "eval_steps_per_second": 10.706, "step": 200}, {"epoch": 4.901960784313726, "grad_norm": 4.161500453948975, "learning_rate": 8.391304347826088e-05, "loss": 2.0438, "step": 250}, {"epoch": 5.882352941176471, "grad_norm": 4.865981578826904, "learning_rate": 7.847826086956522e-05, "loss": 1.9535, "step": 300}, {"epoch": 5.882352941176471, "eval_accuracy": 0.6715686274509803, "eval_f1": 0.7527675276752768, "eval_loss": 0.6436110138893127, "eval_precision": 0.6035502958579881, "eval_recall": 1.0, "eval_runtime": 4.9244, "eval_samples_per_second": 41.426, "eval_steps_per_second": 10.356, "step": 300}, {"epoch": 6.862745098039216, "grad_norm": 16.086057662963867, "learning_rate": 7.304347826086957e-05, "loss": 1.9222, "step": 350}, {"epoch": 7.8431372549019605, "grad_norm": 8.255660057067871, "learning_rate": 6.760869565217392e-05, "loss": 1.8499, "step": 400}, {"epoch": 7.8431372549019605, "eval_accuracy": 0.7009803921568627, "eval_f1": 0.769811320754717, "eval_loss": 0.5486668348312378, "eval_precision": 0.6257668711656442, "eval_recall": 1.0, "eval_runtime": 5.0838, "eval_samples_per_second": 40.127, "eval_steps_per_second": 10.032, "step": 400}, {"epoch": 8.823529411764707, "grad_norm": 25.36577033996582, "learning_rate": 6.217391304347826e-05, "loss": 1.7871, "step": 450}, {"epoch": 9.803921568627452, "grad_norm": 10.30509090423584, "learning_rate": 5.673913043478262e-05, "loss": 1.7803, "step": 500}, {"epoch": 9.803921568627452, "eval_accuracy": 0.7009803921568627, "eval_f1": 0.768060836501901, "eval_loss": 0.5767863988876343, "eval_precision": 0.6273291925465838, "eval_recall": 0.9901960784313726, "eval_runtime": 5.2469, "eval_samples_per_second": 38.88, "eval_steps_per_second": 9.72, "step": 500}, {"epoch": 10.784313725490197, "grad_norm": 13.197319030761719, "learning_rate": 5.1304347826086966e-05, "loss": 1.649, "step": 550}, {"epoch": 11.764705882352942, "grad_norm": 25.025646209716797, "learning_rate": 4.586956521739131e-05, "loss": 1.6894, "step": 600}, {"epoch": 11.764705882352942, "eval_accuracy": 0.7058823529411765, "eval_f1": 0.7727272727272727, "eval_loss": 0.6138393878936768, "eval_precision": 0.6296296296296297, "eval_recall": 1.0, "eval_runtime": 5.3821, "eval_samples_per_second": 37.903, "eval_steps_per_second": 9.476, "step": 600}, {"epoch": 12.745098039215687, "grad_norm": 7.325428485870361, "learning_rate": 4.0434782608695655e-05, "loss": 1.6786, "step": 650}, {"epoch": 13.72549019607843, "grad_norm": 6.751414775848389, "learning_rate": 3.5e-05, "loss": 1.601, "step": 700}, {"epoch": 13.72549019607843, "eval_accuracy": 0.7205882352941176, "eval_f1": 0.7799227799227798, "eval_loss": 0.597659170627594, "eval_precision": 0.643312101910828, "eval_recall": 0.9901960784313726, "eval_runtime": 5.318, "eval_samples_per_second": 38.36, "eval_steps_per_second": 9.59, "step": 700}, {"epoch": 14.705882352941176, "grad_norm": 19.570274353027344, "learning_rate": 2.967391304347826e-05, "loss": 1.6123, "step": 750}, {"epoch": 15.686274509803921, "grad_norm": 5.59031867980957, "learning_rate": 2.423913043478261e-05, "loss": 1.5652, "step": 800}, {"epoch": 15.686274509803921, "eval_accuracy": 0.7450980392156863, "eval_f1": 0.7952755905511811, "eval_loss": 0.565920889377594, "eval_precision": 0.6644736842105263, "eval_recall": 0.9901960784313726, "eval_runtime": 5.409, "eval_samples_per_second": 37.715, "eval_steps_per_second": 9.429, "step": 800}, {"epoch": 16.666666666666668, "grad_norm": 11.213093757629395, "learning_rate": 1.8804347826086958e-05, "loss": 1.5764, "step": 850}, {"epoch": 17.647058823529413, "grad_norm": 9.793022155761719, "learning_rate": 1.3369565217391305e-05, "loss": 1.4978, "step": 900}, {"epoch": 17.647058823529413, "eval_accuracy": 0.7303921568627451, "eval_f1": 0.7859922178988327, "eval_loss": 0.5941892862319946, "eval_precision": 0.6516129032258065, "eval_recall": 0.9901960784313726, "eval_runtime": 5.263, "eval_samples_per_second": 38.761, "eval_steps_per_second": 9.69, "step": 900}, {"epoch": 18.627450980392158, "grad_norm": 11.188767433166504, "learning_rate": 7.934782608695653e-06, "loss": 1.4876, "step": 950}, {"epoch": 19.607843137254903, "grad_norm": 10.52096176147461, "learning_rate": 2.5e-06, "loss": 1.6109, "step": 1000}, {"epoch": 19.607843137254903, "eval_accuracy": 0.7303921568627451, "eval_f1": 0.7859922178988327, "eval_loss": 0.5601627826690674, "eval_precision": 0.6516129032258065, "eval_recall": 0.9901960784313726, "eval_runtime": 5.5162, "eval_samples_per_second": 36.982, "eval_steps_per_second": 9.245, "step": 1000}], "logging_steps": 50, "max_steps": 1020, "num_input_tokens_seen": 0, "num_train_epochs": 20, "save_steps": 200, "stateful_callbacks": {"EarlyStoppingCallback": {"args": {"early_stopping_patience": 8, "early_stopping_threshold": 0.0}, "attributes": {"early_stopping_patience_counter": 2}}, "TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 0.0, "train_batch_size": 4, "trial_name": null, "trial_params": null}