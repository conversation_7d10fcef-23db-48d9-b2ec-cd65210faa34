{"best_global_step": 600, "best_metric": 0.7727272727272727, "best_model_checkpoint": "outputs/cv_lora/rna-fm/fold_0/checkpoint-600", "epoch": 11.764705882352942, "eval_steps": 100, "global_step": 600, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.9803921568627451, "grad_norm": 0.9821391105651855, "learning_rate": 5e-05, "loss": 2.7634, "step": 50}, {"epoch": 1.9607843137254903, "grad_norm": 4.143887996673584, "learning_rate": 9.900000000000001e-05, "loss": 2.7244, "step": 100}, {"epoch": 1.9607843137254903, "eval_accuracy": 0.6421568627450981, "eval_f1": 0.6604651162790697, "eval_loss": 0.6610466241836548, "eval_precision": 0.6283185840707964, "eval_recall": 0.696078431372549, "eval_runtime": 5.5762, "eval_samples_per_second": 36.584, "eval_steps_per_second": 9.146, "step": 100}, {"epoch": 2.9411764705882355, "grad_norm": 18.577713012695312, "learning_rate": 9.467391304347827e-05, "loss": 2.3892, "step": 150}, {"epoch": 3.9215686274509802, "grad_norm": 19.903732299804688, "learning_rate": 8.923913043478261e-05, "loss": 2.0885, "step": 200}, {"epoch": 3.9215686274509802, "eval_accuracy": 0.6862745098039216, "eval_f1": 0.7611940298507462, "eval_loss": 0.6123407483100891, "eval_precision": 0.6144578313253012, "eval_recall": 1.0, "eval_runtime": 4.7635, "eval_samples_per_second": 42.826, "eval_steps_per_second": 10.706, "step": 200}, {"epoch": 4.901960784313726, "grad_norm": 4.161500453948975, "learning_rate": 8.391304347826088e-05, "loss": 2.0438, "step": 250}, {"epoch": 5.882352941176471, "grad_norm": 4.865981578826904, "learning_rate": 7.847826086956522e-05, "loss": 1.9535, "step": 300}, {"epoch": 5.882352941176471, "eval_accuracy": 0.6715686274509803, "eval_f1": 0.7527675276752768, "eval_loss": 0.6436110138893127, "eval_precision": 0.6035502958579881, "eval_recall": 1.0, "eval_runtime": 4.9244, "eval_samples_per_second": 41.426, "eval_steps_per_second": 10.356, "step": 300}, {"epoch": 6.862745098039216, "grad_norm": 16.086057662963867, "learning_rate": 7.304347826086957e-05, "loss": 1.9222, "step": 350}, {"epoch": 7.8431372549019605, "grad_norm": 8.255660057067871, "learning_rate": 6.760869565217392e-05, "loss": 1.8499, "step": 400}, {"epoch": 7.8431372549019605, "eval_accuracy": 0.7009803921568627, "eval_f1": 0.769811320754717, "eval_loss": 0.5486668348312378, "eval_precision": 0.6257668711656442, "eval_recall": 1.0, "eval_runtime": 5.0838, "eval_samples_per_second": 40.127, "eval_steps_per_second": 10.032, "step": 400}, {"epoch": 8.823529411764707, "grad_norm": 25.36577033996582, "learning_rate": 6.217391304347826e-05, "loss": 1.7871, "step": 450}, {"epoch": 9.803921568627452, "grad_norm": 10.30509090423584, "learning_rate": 5.673913043478262e-05, "loss": 1.7803, "step": 500}, {"epoch": 9.803921568627452, "eval_accuracy": 0.7009803921568627, "eval_f1": 0.768060836501901, "eval_loss": 0.5767863988876343, "eval_precision": 0.6273291925465838, "eval_recall": 0.9901960784313726, "eval_runtime": 5.2469, "eval_samples_per_second": 38.88, "eval_steps_per_second": 9.72, "step": 500}, {"epoch": 10.784313725490197, "grad_norm": 13.197319030761719, "learning_rate": 5.1304347826086966e-05, "loss": 1.649, "step": 550}, {"epoch": 11.764705882352942, "grad_norm": 25.025646209716797, "learning_rate": 4.586956521739131e-05, "loss": 1.6894, "step": 600}, {"epoch": 11.764705882352942, "eval_accuracy": 0.7058823529411765, "eval_f1": 0.7727272727272727, "eval_loss": 0.6138393878936768, "eval_precision": 0.6296296296296297, "eval_recall": 1.0, "eval_runtime": 5.3821, "eval_samples_per_second": 37.903, "eval_steps_per_second": 9.476, "step": 600}], "logging_steps": 50, "max_steps": 1020, "num_input_tokens_seen": 0, "num_train_epochs": 20, "save_steps": 200, "stateful_callbacks": {"EarlyStoppingCallback": {"args": {"early_stopping_patience": 8, "early_stopping_threshold": 0.0}, "attributes": {"early_stopping_patience_counter": 0}}, "TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 0.0, "train_batch_size": 4, "trial_name": null, "trial_params": null}