{"best_global_step": 300, "best_metric": 0.8050847457627119, "best_model_checkpoint": "outputs/cv_lora/splicebert-ms1024/fold_0/checkpoint-200", "epoch": 20.0, "eval_steps": 100, "global_step": 1020, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.9803921568627451, "grad_norm": 1.441124439239502, "learning_rate": 5e-05, "loss": 2.7651, "step": 50}, {"epoch": 1.9607843137254903, "grad_norm": 2.669462203979492, "learning_rate": 9.900000000000001e-05, "loss": 2.7334, "step": 100}, {"epoch": 1.9607843137254903, "eval_accuracy": 0.5931372549019608, "eval_f1": 0.5088757396449705, "eval_loss": 0.6677868366241455, "eval_precision": 0.6417910447761194, "eval_recall": 0.4215686274509804, "eval_runtime": 2.1204, "eval_samples_per_second": 96.206, "eval_steps_per_second": 24.052, "step": 100}, {"epoch": 2.9411764705882355, "grad_norm": 9.339183807373047, "learning_rate": 9.478260869565218e-05, "loss": 2.493, "step": 150}, {"epoch": 3.9215686274509802, "grad_norm": 13.361529350280762, "learning_rate": 8.934782608695653e-05, "loss": 2.0676, "step": 200}, {"epoch": 3.9215686274509802, "eval_accuracy": 0.7058823529411765, "eval_f1": 0.765625, "eval_loss": 0.5840229988098145, "eval_precision": 0.6363636363636364, "eval_recall": 0.9607843137254902, "eval_runtime": 2.3649, "eval_samples_per_second": 86.26, "eval_steps_per_second": 21.565, "step": 200}, {"epoch": 4.901960784313726, "grad_norm": 3.4153785705566406, "learning_rate": 8.391304347826088e-05, "loss": 2.0109, "step": 250}, {"epoch": 5.882352941176471, "grad_norm": 4.85373067855835, "learning_rate": 7.847826086956522e-05, "loss": 1.7744, "step": 300}, {"epoch": 5.882352941176471, "eval_accuracy": 0.7745098039215687, "eval_f1": 0.8050847457627119, "eval_loss": 0.48093608021736145, "eval_precision": 0.7089552238805971, "eval_recall": 0.9313725490196079, "eval_runtime": 2.341, "eval_samples_per_second": 87.141, "eval_steps_per_second": 21.785, "step": 300}, {"epoch": 6.862745098039216, "grad_norm": 6.551260471343994, "learning_rate": 7.304347826086957e-05, "loss": 1.703, "step": 350}, {"epoch": 7.8431372549019605, "grad_norm": 5.116859436035156, "learning_rate": 6.760869565217392e-05, "loss": 1.6632, "step": 400}, {"epoch": 7.8431372549019605, "eval_accuracy": 0.7843137254901961, "eval_f1": 0.8018018018018018, "eval_loss": 0.4792307913303375, "eval_precision": 0.7416666666666667, "eval_recall": 0.8725490196078431, "eval_runtime": 2.3134, "eval_samples_per_second": 88.184, "eval_steps_per_second": 22.046, "step": 400}, {"epoch": 8.823529411764707, "grad_norm": 16.337982177734375, "learning_rate": 6.228260869565218e-05, "loss": 1.5311, "step": 450}, {"epoch": 9.803921568627452, "grad_norm": 28.50994873046875, "learning_rate": 5.6847826086956524e-05, "loss": 1.5544, "step": 500}, {"epoch": 9.803921568627452, "eval_accuracy": 0.7696078431372549, "eval_f1": 0.7873303167420814, "eval_loss": 0.4975014626979828, "eval_precision": 0.7310924369747899, "eval_recall": 0.8529411764705882, "eval_runtime": 2.5458, "eval_samples_per_second": 80.133, "eval_steps_per_second": 20.033, "step": 500}, {"epoch": 10.784313725490197, "grad_norm": 4.296060085296631, "learning_rate": 5.141304347826087e-05, "loss": 1.4307, "step": 550}, {"epoch": 11.764705882352942, "grad_norm": 7.704607963562012, "learning_rate": 4.597826086956522e-05, "loss": 1.4717, "step": 600}, {"epoch": 11.764705882352942, "eval_accuracy": 0.7549019607843137, "eval_f1": 0.7787610619469026, "eval_loss": 0.5345161557197571, "eval_precision": 0.7096774193548387, "eval_recall": 0.8627450980392157, "eval_runtime": 2.4197, "eval_samples_per_second": 84.308, "eval_steps_per_second": 21.077, "step": 600}, {"epoch": 12.745098039215687, "grad_norm": 7.948872089385986, "learning_rate": 4.054347826086957e-05, "loss": 1.4128, "step": 650}, {"epoch": 13.72549019607843, "grad_norm": 5.787350654602051, "learning_rate": 3.510869565217392e-05, "loss": 1.3509, "step": 700}, {"epoch": 13.72549019607843, "eval_accuracy": 0.7450980392156863, "eval_f1": 0.7796610169491525, "eval_loss": 0.5611903667449951, "eval_precision": 0.6865671641791045, "eval_recall": 0.9019607843137255, "eval_runtime": 2.4125, "eval_samples_per_second": 84.56, "eval_steps_per_second": 21.14, "step": 700}, {"epoch": 14.705882352941176, "grad_norm": 7.899132251739502, "learning_rate": 2.967391304347826e-05, "loss": 1.3655, "step": 750}, {"epoch": 15.686274509803921, "grad_norm": 8.12803840637207, "learning_rate": 2.423913043478261e-05, "loss": 1.2939, "step": 800}, {"epoch": 15.686274509803921, "eval_accuracy": 0.7598039215686274, "eval_f1": 0.7822222222222223, "eval_loss": 0.5912490487098694, "eval_precision": 0.7154471544715447, "eval_recall": 0.8627450980392157, "eval_runtime": 2.4012, "eval_samples_per_second": 84.959, "eval_steps_per_second": 21.24, "step": 800}, {"epoch": 16.666666666666668, "grad_norm": 4.720434665679932, "learning_rate": 1.8804347826086958e-05, "loss": 1.3262, "step": 850}, {"epoch": 17.647058823529413, "grad_norm": 4.0390753746032715, "learning_rate": 1.3369565217391305e-05, "loss": 1.2719, "step": 900}, {"epoch": 17.647058823529413, "eval_accuracy": 0.7598039215686274, "eval_f1": 0.7822222222222223, "eval_loss": 0.5933506488800049, "eval_precision": 0.7154471544715447, "eval_recall": 0.8627450980392157, "eval_runtime": 1.6729, "eval_samples_per_second": 121.942, "eval_steps_per_second": 30.485, "step": 900}, {"epoch": 18.627450980392158, "grad_norm": 16.700729370117188, "learning_rate": 7.934782608695653e-06, "loss": 1.1572, "step": 950}, {"epoch": 19.607843137254903, "grad_norm": 10.581889152526855, "learning_rate": 2.5e-06, "loss": 1.3367, "step": 1000}, {"epoch": 19.607843137254903, "eval_accuracy": 0.7450980392156863, "eval_f1": 0.7636363636363636, "eval_loss": 0.6215693354606628, "eval_precision": 0.711864406779661, "eval_recall": 0.8235294117647058, "eval_runtime": 2.3215, "eval_samples_per_second": 87.875, "eval_steps_per_second": 21.969, "step": 1000}, {"epoch": 20.0, "step": 1020, "total_flos": 0.0, "train_loss": 1.679436741623224, "train_runtime": 465.1162, "train_samples_per_second": 34.959, "train_steps_per_second": 2.193}], "logging_steps": 50, "max_steps": 1020, "num_input_tokens_seen": 0, "num_train_epochs": 20, "save_steps": 200, "stateful_callbacks": {"EarlyStoppingCallback": {"args": {"early_stopping_patience": 8, "early_stopping_threshold": 0.0}, "attributes": {"early_stopping_patience_counter": 7}}, "TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": true}, "attributes": {}}}, "total_flos": 0.0, "train_batch_size": 4, "trial_name": null, "trial_params": null}