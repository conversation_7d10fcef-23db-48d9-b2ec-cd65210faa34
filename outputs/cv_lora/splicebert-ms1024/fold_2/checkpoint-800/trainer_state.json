{"best_global_step": 300, "best_metric": 0.798283261802575, "best_model_checkpoint": "outputs/cv_lora/splicebert-ms1024/fold_2/checkpoint-200", "epoch": 15.686274509803921, "eval_steps": 100, "global_step": 800, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.9803921568627451, "grad_norm": 2.7657854557037354, "learning_rate": 5e-05, "loss": 2.77, "step": 50}, {"epoch": 1.9607843137254903, "grad_norm": 2.1045031547546387, "learning_rate": 0.0001, "loss": 2.735, "step": 100}, {"epoch": 1.9607843137254903, "eval_accuracy": 0.6206896551724138, "eval_f1": 0.6169154228855721, "eval_loss": 0.6659314632415771, "eval_precision": 0.6262626262626263, "eval_recall": 0.6078431372549019, "eval_runtime": 2.3737, "eval_samples_per_second": 85.521, "eval_steps_per_second": 21.486, "step": 100}, {"epoch": 2.9411764705882355, "grad_norm": 5.326802730560303, "learning_rate": 9.467391304347827e-05, "loss": 2.4056, "step": 150}, {"epoch": 3.9215686274509802, "grad_norm": 15.105899810791016, "learning_rate": 8.934782608695653e-05, "loss": 2.0753, "step": 200}, {"epoch": 3.9215686274509802, "eval_accuracy": 0.7487684729064039, "eval_f1": 0.7733333333333334, "eval_loss": 0.5341586470603943, "eval_precision": 0.7073170731707317, "eval_recall": 0.8529411764705882, "eval_runtime": 2.4888, "eval_samples_per_second": 81.564, "eval_steps_per_second": 20.492, "step": 200}, {"epoch": 4.901960784313726, "grad_norm": 9.800994873046875, "learning_rate": 8.391304347826088e-05, "loss": 1.9103, "step": 250}, {"epoch": 5.882352941176471, "grad_norm": 4.516547679901123, "learning_rate": 7.847826086956522e-05, "loss": 1.9797, "step": 300}, {"epoch": 5.882352941176471, "eval_accuracy": 0.7684729064039408, "eval_f1": 0.798283261802575, "eval_loss": 0.5098385214805603, "eval_precision": 0.7099236641221374, "eval_recall": 0.9117647058823529, "eval_runtime": 2.3739, "eval_samples_per_second": 85.512, "eval_steps_per_second": 21.483, "step": 300}, {"epoch": 6.862745098039216, "grad_norm": 7.292038440704346, "learning_rate": 7.304347826086957e-05, "loss": 1.7707, "step": 350}, {"epoch": 7.8431372549019605, "grad_norm": 12.420613288879395, "learning_rate": 6.760869565217392e-05, "loss": 1.6354, "step": 400}, {"epoch": 7.8431372549019605, "eval_accuracy": 0.7684729064039408, "eval_f1": 0.7929515418502202, "eval_loss": 0.4907273054122925, "eval_precision": 0.72, "eval_recall": 0.8823529411764706, "eval_runtime": 3.1634, "eval_samples_per_second": 64.172, "eval_steps_per_second": 16.122, "step": 400}, {"epoch": 8.823529411764707, "grad_norm": 6.522562503814697, "learning_rate": 6.217391304347826e-05, "loss": 1.6123, "step": 450}, {"epoch": 9.803921568627452, "grad_norm": 22.50455093383789, "learning_rate": 5.673913043478262e-05, "loss": 1.5376, "step": 500}, {"epoch": 9.803921568627452, "eval_accuracy": 0.7487684729064039, "eval_f1": 0.767123287671233, "eval_loss": 0.5324861407279968, "eval_precision": 0.717948717948718, "eval_recall": 0.8235294117647058, "eval_runtime": 3.4074, "eval_samples_per_second": 59.576, "eval_steps_per_second": 14.967, "step": 500}, {"epoch": 10.784313725490197, "grad_norm": 8.707581520080566, "learning_rate": 5.1304347826086966e-05, "loss": 1.4454, "step": 550}, {"epoch": 11.764705882352942, "grad_norm": 16.60944366455078, "learning_rate": 4.586956521739131e-05, "loss": 1.4325, "step": 600}, {"epoch": 11.764705882352942, "eval_accuracy": 0.7438423645320197, "eval_f1": 0.761467889908257, "eval_loss": 0.5597534775733948, "eval_precision": 0.7155172413793104, "eval_recall": 0.8137254901960784, "eval_runtime": 3.2375, "eval_samples_per_second": 62.702, "eval_steps_per_second": 15.753, "step": 600}, {"epoch": 12.745098039215687, "grad_norm": 13.054447174072266, "learning_rate": 4.0434782608695655e-05, "loss": 1.3666, "step": 650}, {"epoch": 13.72549019607843, "grad_norm": 15.350931167602539, "learning_rate": 3.5e-05, "loss": 1.2957, "step": 700}, {"epoch": 13.72549019607843, "eval_accuracy": 0.7339901477832512, "eval_f1": 0.7692307692307693, "eval_loss": 0.6134455800056458, "eval_precision": 0.6818181818181818, "eval_recall": 0.8823529411764706, "eval_runtime": 2.8781, "eval_samples_per_second": 70.532, "eval_steps_per_second": 17.72, "step": 700}, {"epoch": 14.705882352941176, "grad_norm": 7.222897052764893, "learning_rate": 2.9565217391304352e-05, "loss": 1.35, "step": 750}, {"epoch": 15.686274509803921, "grad_norm": 7.192198276519775, "learning_rate": 2.4130434782608697e-05, "loss": 1.3249, "step": 800}, {"epoch": 15.686274509803921, "eval_accuracy": 0.7389162561576355, "eval_f1": 0.7601809954751132, "eval_loss": 0.6089236736297607, "eval_precision": 0.7058823529411765, "eval_recall": 0.8235294117647058, "eval_runtime": 3.6044, "eval_samples_per_second": 56.32, "eval_steps_per_second": 14.149, "step": 800}], "logging_steps": 50, "max_steps": 1020, "num_input_tokens_seen": 0, "num_train_epochs": 20, "save_steps": 200, "stateful_callbacks": {"EarlyStoppingCallback": {"args": {"early_stopping_patience": 8, "early_stopping_threshold": 0.0}, "attributes": {"early_stopping_patience_counter": 5}}, "TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 0.0, "train_batch_size": 4, "trial_name": null, "trial_params": null}