{"best_global_step": 200, "best_metric": 0.7733333333333334, "best_model_checkpoint": "outputs/cv_lora/splicebert-ms1024/fold_2/checkpoint-200", "epoch": 3.9215686274509802, "eval_steps": 100, "global_step": 200, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.9803921568627451, "grad_norm": 2.7657854557037354, "learning_rate": 5e-05, "loss": 2.77, "step": 50}, {"epoch": 1.9607843137254903, "grad_norm": 2.1045031547546387, "learning_rate": 0.0001, "loss": 2.735, "step": 100}, {"epoch": 1.9607843137254903, "eval_accuracy": 0.6206896551724138, "eval_f1": 0.6169154228855721, "eval_loss": 0.6659314632415771, "eval_precision": 0.6262626262626263, "eval_recall": 0.6078431372549019, "eval_runtime": 2.3737, "eval_samples_per_second": 85.521, "eval_steps_per_second": 21.486, "step": 100}, {"epoch": 2.9411764705882355, "grad_norm": 5.326802730560303, "learning_rate": 9.467391304347827e-05, "loss": 2.4056, "step": 150}, {"epoch": 3.9215686274509802, "grad_norm": 15.105899810791016, "learning_rate": 8.934782608695653e-05, "loss": 2.0753, "step": 200}, {"epoch": 3.9215686274509802, "eval_accuracy": 0.7487684729064039, "eval_f1": 0.7733333333333334, "eval_loss": 0.5341586470603943, "eval_precision": 0.7073170731707317, "eval_recall": 0.8529411764705882, "eval_runtime": 2.4888, "eval_samples_per_second": 81.564, "eval_steps_per_second": 20.492, "step": 200}], "logging_steps": 50, "max_steps": 1020, "num_input_tokens_seen": 0, "num_train_epochs": 20, "save_steps": 200, "stateful_callbacks": {"EarlyStoppingCallback": {"args": {"early_stopping_patience": 8, "early_stopping_threshold": 0.0}, "attributes": {"early_stopping_patience_counter": 0}}, "TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 0.0, "train_batch_size": 4, "trial_name": null, "trial_params": null}