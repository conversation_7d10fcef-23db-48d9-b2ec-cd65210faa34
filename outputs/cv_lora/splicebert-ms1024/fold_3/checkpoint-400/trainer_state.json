{"best_global_step": 300, "best_metric": 0.7982456140350878, "best_model_checkpoint": "outputs/cv_lora/splicebert-ms1024/fold_3/checkpoint-200", "epoch": 7.8431372549019605, "eval_steps": 100, "global_step": 400, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.9803921568627451, "grad_norm": 2.499570608139038, "learning_rate": 5e-05, "loss": 2.7723, "step": 50}, {"epoch": 1.9607843137254903, "grad_norm": 4.110760688781738, "learning_rate": 0.0001, "loss": 2.73, "step": 100}, {"epoch": 1.9607843137254903, "eval_accuracy": 0.6108374384236454, "eval_f1": 0.6183574879227053, "eval_loss": 0.6666193604469299, "eval_precision": 0.6095238095238096, "eval_recall": 0.6274509803921569, "eval_runtime": 2.2498, "eval_samples_per_second": 90.23, "eval_steps_per_second": 22.669, "step": 100}, {"epoch": 2.9411764705882355, "grad_norm": 5.312799453735352, "learning_rate": 9.467391304347827e-05, "loss": 2.4605, "step": 150}, {"epoch": 3.9215686274509802, "grad_norm": 7.299253940582275, "learning_rate": 8.923913043478261e-05, "loss": 2.0702, "step": 200}, {"epoch": 3.9215686274509802, "eval_accuracy": 0.7389162561576355, "eval_f1": 0.7836734693877551, "eval_loss": 0.5306539535522461, "eval_precision": 0.6713286713286714, "eval_recall": 0.9411764705882353, "eval_runtime": 2.8934, "eval_samples_per_second": 70.161, "eval_steps_per_second": 17.627, "step": 200}, {"epoch": 4.901960784313726, "grad_norm": 5.978110313415527, "learning_rate": 8.380434782608696e-05, "loss": 1.8294, "step": 250}, {"epoch": 5.882352941176471, "grad_norm": 18.926624298095703, "learning_rate": 7.847826086956522e-05, "loss": 1.8135, "step": 300}, {"epoch": 5.882352941176471, "eval_accuracy": 0.7733990147783252, "eval_f1": 0.7982456140350878, "eval_loss": 0.49514177441596985, "eval_precision": 0.7222222222222222, "eval_recall": 0.8921568627450981, "eval_runtime": 3.0994, "eval_samples_per_second": 65.496, "eval_steps_per_second": 16.455, "step": 300}, {"epoch": 6.862745098039216, "grad_norm": 13.870551109313965, "learning_rate": 7.304347826086957e-05, "loss": 1.6314, "step": 350}, {"epoch": 7.8431372549019605, "grad_norm": 5.014033317565918, "learning_rate": 6.771739130434783e-05, "loss": 1.608, "step": 400}, {"epoch": 7.8431372549019605, "eval_accuracy": 0.7586206896551724, "eval_f1": 0.7841409691629956, "eval_loss": 0.50044184923172, "eval_precision": 0.712, "eval_recall": 0.8725490196078431, "eval_runtime": 3.2639, "eval_samples_per_second": 62.195, "eval_steps_per_second": 15.625, "step": 400}], "logging_steps": 50, "max_steps": 1020, "num_input_tokens_seen": 0, "num_train_epochs": 20, "save_steps": 200, "stateful_callbacks": {"EarlyStoppingCallback": {"args": {"early_stopping_patience": 8, "early_stopping_threshold": 0.0}, "attributes": {"early_stopping_patience_counter": 1}}, "TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 0.0, "train_batch_size": 4, "trial_name": null, "trial_params": null}