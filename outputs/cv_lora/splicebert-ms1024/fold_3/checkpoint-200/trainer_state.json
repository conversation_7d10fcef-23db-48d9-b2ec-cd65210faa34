{"best_global_step": 200, "best_metric": 0.7836734693877551, "best_model_checkpoint": "outputs/cv_lora/splicebert-ms1024/fold_3/checkpoint-200", "epoch": 3.9215686274509802, "eval_steps": 100, "global_step": 200, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.9803921568627451, "grad_norm": 2.499570608139038, "learning_rate": 5e-05, "loss": 2.7723, "step": 50}, {"epoch": 1.9607843137254903, "grad_norm": 4.110760688781738, "learning_rate": 0.0001, "loss": 2.73, "step": 100}, {"epoch": 1.9607843137254903, "eval_accuracy": 0.6108374384236454, "eval_f1": 0.6183574879227053, "eval_loss": 0.6666193604469299, "eval_precision": 0.6095238095238096, "eval_recall": 0.6274509803921569, "eval_runtime": 2.2498, "eval_samples_per_second": 90.23, "eval_steps_per_second": 22.669, "step": 100}, {"epoch": 2.9411764705882355, "grad_norm": 5.312799453735352, "learning_rate": 9.467391304347827e-05, "loss": 2.4605, "step": 150}, {"epoch": 3.9215686274509802, "grad_norm": 7.299253940582275, "learning_rate": 8.923913043478261e-05, "loss": 2.0702, "step": 200}, {"epoch": 3.9215686274509802, "eval_accuracy": 0.7389162561576355, "eval_f1": 0.7836734693877551, "eval_loss": 0.5306539535522461, "eval_precision": 0.6713286713286714, "eval_recall": 0.9411764705882353, "eval_runtime": 2.8934, "eval_samples_per_second": 70.161, "eval_steps_per_second": 17.627, "step": 200}], "logging_steps": 50, "max_steps": 1020, "num_input_tokens_seen": 0, "num_train_epochs": 20, "save_steps": 200, "stateful_callbacks": {"EarlyStoppingCallback": {"args": {"early_stopping_patience": 8, "early_stopping_threshold": 0.0}, "attributes": {"early_stopping_patience_counter": 0}}, "TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 0.0, "train_batch_size": 4, "trial_name": null, "trial_params": null}