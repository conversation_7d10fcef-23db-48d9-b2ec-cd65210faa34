{"best_global_step": 600, "best_metric": 0.8067226890756304, "best_model_checkpoint": "outputs/cv_lora/splicebert-ms1024/fold_3/checkpoint-600", "epoch": 20.0, "eval_steps": 100, "global_step": 1020, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.9803921568627451, "grad_norm": 2.499570608139038, "learning_rate": 5e-05, "loss": 2.7723, "step": 50}, {"epoch": 1.9607843137254903, "grad_norm": 4.110760688781738, "learning_rate": 0.0001, "loss": 2.73, "step": 100}, {"epoch": 1.9607843137254903, "eval_accuracy": 0.6108374384236454, "eval_f1": 0.6183574879227053, "eval_loss": 0.6666193604469299, "eval_precision": 0.6095238095238096, "eval_recall": 0.6274509803921569, "eval_runtime": 2.2498, "eval_samples_per_second": 90.23, "eval_steps_per_second": 22.669, "step": 100}, {"epoch": 2.9411764705882355, "grad_norm": 5.312799453735352, "learning_rate": 9.467391304347827e-05, "loss": 2.4605, "step": 150}, {"epoch": 3.9215686274509802, "grad_norm": 7.299253940582275, "learning_rate": 8.923913043478261e-05, "loss": 2.0702, "step": 200}, {"epoch": 3.9215686274509802, "eval_accuracy": 0.7389162561576355, "eval_f1": 0.7836734693877551, "eval_loss": 0.5306539535522461, "eval_precision": 0.6713286713286714, "eval_recall": 0.9411764705882353, "eval_runtime": 2.8934, "eval_samples_per_second": 70.161, "eval_steps_per_second": 17.627, "step": 200}, {"epoch": 4.901960784313726, "grad_norm": 5.978110313415527, "learning_rate": 8.380434782608696e-05, "loss": 1.8294, "step": 250}, {"epoch": 5.882352941176471, "grad_norm": 18.926624298095703, "learning_rate": 7.847826086956522e-05, "loss": 1.8135, "step": 300}, {"epoch": 5.882352941176471, "eval_accuracy": 0.7733990147783252, "eval_f1": 0.7982456140350878, "eval_loss": 0.49514177441596985, "eval_precision": 0.7222222222222222, "eval_recall": 0.8921568627450981, "eval_runtime": 3.0994, "eval_samples_per_second": 65.496, "eval_steps_per_second": 16.455, "step": 300}, {"epoch": 6.862745098039216, "grad_norm": 13.870551109313965, "learning_rate": 7.304347826086957e-05, "loss": 1.6314, "step": 350}, {"epoch": 7.8431372549019605, "grad_norm": 5.014033317565918, "learning_rate": 6.771739130434783e-05, "loss": 1.608, "step": 400}, {"epoch": 7.8431372549019605, "eval_accuracy": 0.7586206896551724, "eval_f1": 0.7841409691629956, "eval_loss": 0.50044184923172, "eval_precision": 0.712, "eval_recall": 0.8725490196078431, "eval_runtime": 3.2639, "eval_samples_per_second": 62.195, "eval_steps_per_second": 15.625, "step": 400}, {"epoch": 8.823529411764707, "grad_norm": 7.316703796386719, "learning_rate": 6.228260869565218e-05, "loss": 1.675, "step": 450}, {"epoch": 9.803921568627452, "grad_norm": 5.495606422424316, "learning_rate": 5.6847826086956524e-05, "loss": 1.514, "step": 500}, {"epoch": 9.803921568627452, "eval_accuracy": 0.7635467980295566, "eval_f1": 0.8016528925619835, "eval_loss": 0.5108949542045593, "eval_precision": 0.6928571428571428, "eval_recall": 0.9509803921568627, "eval_runtime": 2.8774, "eval_samples_per_second": 70.549, "eval_steps_per_second": 17.724, "step": 500}, {"epoch": 10.784313725490197, "grad_norm": 6.676844120025635, "learning_rate": 5.141304347826087e-05, "loss": 1.3786, "step": 550}, {"epoch": 11.764705882352942, "grad_norm": 11.385841369628906, "learning_rate": 4.597826086956522e-05, "loss": 1.4506, "step": 600}, {"epoch": 11.764705882352942, "eval_accuracy": 0.7733990147783252, "eval_f1": 0.8067226890756304, "eval_loss": 0.5622352361679077, "eval_precision": 0.7058823529411765, "eval_recall": 0.9411764705882353, "eval_runtime": 3.4299, "eval_samples_per_second": 59.185, "eval_steps_per_second": 14.869, "step": 600}, {"epoch": 12.745098039215687, "grad_norm": 12.270768165588379, "learning_rate": 4.065217391304348e-05, "loss": 1.3788, "step": 650}, {"epoch": 13.72549019607843, "grad_norm": 14.515340805053711, "learning_rate": 3.521739130434783e-05, "loss": 1.312, "step": 700}, {"epoch": 13.72549019607843, "eval_accuracy": 0.7635467980295566, "eval_f1": 0.7894736842105262, "eval_loss": 0.5684444904327393, "eval_precision": 0.7142857142857143, "eval_recall": 0.8823529411764706, "eval_runtime": 3.1195, "eval_samples_per_second": 65.074, "eval_steps_per_second": 16.349, "step": 700}, {"epoch": 14.705882352941176, "grad_norm": 33.476627349853516, "learning_rate": 2.9782608695652175e-05, "loss": 1.2964, "step": 750}, {"epoch": 15.686274509803921, "grad_norm": 9.37376880645752, "learning_rate": 2.4347826086956523e-05, "loss": 1.2616, "step": 800}, {"epoch": 15.686274509803921, "eval_accuracy": 0.7487684729064039, "eval_f1": 0.7829787234042552, "eval_loss": 0.6027925610542297, "eval_precision": 0.6917293233082706, "eval_recall": 0.9019607843137255, "eval_runtime": 3.1269, "eval_samples_per_second": 64.92, "eval_steps_per_second": 16.31, "step": 800}, {"epoch": 16.666666666666668, "grad_norm": 15.647979736328125, "learning_rate": 1.8913043478260868e-05, "loss": 1.2342, "step": 850}, {"epoch": 17.647058823529413, "grad_norm": 16.384492874145508, "learning_rate": 1.3478260869565218e-05, "loss": 1.3189, "step": 900}, {"epoch": 17.647058823529413, "eval_accuracy": 0.7536945812807881, "eval_f1": 0.7807017543859649, "eval_loss": 0.6291791200637817, "eval_precision": 0.7063492063492064, "eval_recall": 0.8725490196078431, "eval_runtime": 2.9851, "eval_samples_per_second": 68.005, "eval_steps_per_second": 17.085, "step": 900}, {"epoch": 18.627450980392158, "grad_norm": 22.867759704589844, "learning_rate": 8.043478260869565e-06, "loss": 1.195, "step": 950}, {"epoch": 19.607843137254903, "grad_norm": 13.678882598876953, "learning_rate": 2.608695652173913e-06, "loss": 1.2244, "step": 1000}, {"epoch": 19.607843137254903, "eval_accuracy": 0.7536945812807881, "eval_f1": 0.7807017543859649, "eval_loss": 0.6586934924125671, "eval_precision": 0.7063492063492064, "eval_recall": 0.8725490196078431, "eval_runtime": 3.2622, "eval_samples_per_second": 62.228, "eval_steps_per_second": 15.634, "step": 1000}], "logging_steps": 50, "max_steps": 1020, "num_input_tokens_seen": 0, "num_train_epochs": 20, "save_steps": 200, "stateful_callbacks": {"EarlyStoppingCallback": {"args": {"early_stopping_patience": 8, "early_stopping_threshold": 0.0}, "attributes": {"early_stopping_patience_counter": 4}}, "TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": true}, "attributes": {}}}, "total_flos": 0.0, "train_batch_size": 4, "trial_name": null, "trial_params": null}