{"best_global_step": 600, "best_metric": 0.8189655172413794, "best_model_checkpoint": "outputs/cv_lora/splicebert-ms1024/fold_1/checkpoint-600", "epoch": 11.764705882352942, "eval_steps": 100, "global_step": 600, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.9803921568627451, "grad_norm": 3.0379483699798584, "learning_rate": 5e-05, "loss": 2.7659, "step": 50}, {"epoch": 1.9607843137254903, "grad_norm": 2.304701089859009, "learning_rate": 9.900000000000001e-05, "loss": 2.743, "step": 100}, {"epoch": 1.9607843137254903, "eval_accuracy": 0.6715686274509803, "eval_f1": 0.7392996108949417, "eval_loss": 0.6721885800361633, "eval_precision": 0.6129032258064516, "eval_recall": 0.9313725490196079, "eval_runtime": 2.503, "eval_samples_per_second": 81.501, "eval_steps_per_second": 20.375, "step": 100}, {"epoch": 2.9411764705882355, "grad_norm": 4.558028221130371, "learning_rate": 9.467391304347827e-05, "loss": 2.506, "step": 150}, {"epoch": 3.9215686274509802, "grad_norm": 7.135382175445557, "learning_rate": 8.923913043478261e-05, "loss": 2.0874, "step": 200}, {"epoch": 3.9215686274509802, "eval_accuracy": 0.7696078431372549, "eval_f1": 0.7684729064039408, "eval_loss": 0.5166981816291809, "eval_precision": 0.7722772277227723, "eval_recall": 0.7647058823529411, "eval_runtime": 2.4137, "eval_samples_per_second": 84.516, "eval_steps_per_second": 21.129, "step": 200}, {"epoch": 4.901960784313726, "grad_norm": 6.824845790863037, "learning_rate": 8.380434782608696e-05, "loss": 1.9937, "step": 250}, {"epoch": 5.882352941176471, "grad_norm": 4.226147174835205, "learning_rate": 7.83695652173913e-05, "loss": 1.818, "step": 300}, {"epoch": 5.882352941176471, "eval_accuracy": 0.7598039215686274, "eval_f1": 0.7966804979253113, "eval_loss": 0.48325005173683167, "eval_precision": 0.6906474820143885, "eval_recall": 0.9411764705882353, "eval_runtime": 2.4341, "eval_samples_per_second": 83.809, "eval_steps_per_second": 20.952, "step": 300}, {"epoch": 6.862745098039216, "grad_norm": 24.223079681396484, "learning_rate": 7.293478260869565e-05, "loss": 1.7948, "step": 350}, {"epoch": 7.8431372549019605, "grad_norm": 10.568153381347656, "learning_rate": 6.760869565217392e-05, "loss": 1.6535, "step": 400}, {"epoch": 7.8431372549019605, "eval_accuracy": 0.7843137254901961, "eval_f1": 0.8103448275862069, "eval_loss": 0.46583834290504456, "eval_precision": 0.7230769230769231, "eval_recall": 0.9215686274509803, "eval_runtime": 2.289, "eval_samples_per_second": 89.123, "eval_steps_per_second": 22.281, "step": 400}, {"epoch": 8.823529411764707, "grad_norm": 20.86929702758789, "learning_rate": 6.217391304347826e-05, "loss": 1.5737, "step": 450}, {"epoch": 9.803921568627452, "grad_norm": 11.058870315551758, "learning_rate": 5.673913043478262e-05, "loss": 1.6442, "step": 500}, {"epoch": 9.803921568627452, "eval_accuracy": 0.7892156862745098, "eval_f1": 0.8154506437768241, "eval_loss": 0.4880197048187256, "eval_precision": 0.7251908396946565, "eval_recall": 0.9313725490196079, "eval_runtime": 2.1343, "eval_samples_per_second": 95.58, "eval_steps_per_second": 23.895, "step": 500}, {"epoch": 10.784313725490197, "grad_norm": 12.295050621032715, "learning_rate": 5.1304347826086966e-05, "loss": 1.4387, "step": 550}, {"epoch": 11.764705882352942, "grad_norm": 9.20841121673584, "learning_rate": 4.597826086956522e-05, "loss": 1.4452, "step": 600}, {"epoch": 11.764705882352942, "eval_accuracy": 0.7941176470588235, "eval_f1": 0.8189655172413794, "eval_loss": 0.49989327788352966, "eval_precision": 0.7307692307692307, "eval_recall": 0.9313725490196079, "eval_runtime": 2.3558, "eval_samples_per_second": 86.596, "eval_steps_per_second": 21.649, "step": 600}], "logging_steps": 50, "max_steps": 1020, "num_input_tokens_seen": 0, "num_train_epochs": 20, "save_steps": 200, "stateful_callbacks": {"EarlyStoppingCallback": {"args": {"early_stopping_patience": 8, "early_stopping_threshold": 0.0}, "attributes": {"early_stopping_patience_counter": 0}}, "TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 0.0, "train_batch_size": 4, "trial_name": null, "trial_params": null}