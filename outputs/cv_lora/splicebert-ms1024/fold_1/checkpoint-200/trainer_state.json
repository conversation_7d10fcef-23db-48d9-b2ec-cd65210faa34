{"best_global_step": 200, "best_metric": 0.7684729064039408, "best_model_checkpoint": "outputs/cv_lora/splicebert-ms1024/fold_1/checkpoint-200", "epoch": 3.9215686274509802, "eval_steps": 100, "global_step": 200, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.9803921568627451, "grad_norm": 3.0379483699798584, "learning_rate": 5e-05, "loss": 2.7659, "step": 50}, {"epoch": 1.9607843137254903, "grad_norm": 2.304701089859009, "learning_rate": 9.900000000000001e-05, "loss": 2.743, "step": 100}, {"epoch": 1.9607843137254903, "eval_accuracy": 0.6715686274509803, "eval_f1": 0.7392996108949417, "eval_loss": 0.6721885800361633, "eval_precision": 0.6129032258064516, "eval_recall": 0.9313725490196079, "eval_runtime": 2.503, "eval_samples_per_second": 81.501, "eval_steps_per_second": 20.375, "step": 100}, {"epoch": 2.9411764705882355, "grad_norm": 4.558028221130371, "learning_rate": 9.467391304347827e-05, "loss": 2.506, "step": 150}, {"epoch": 3.9215686274509802, "grad_norm": 7.135382175445557, "learning_rate": 8.923913043478261e-05, "loss": 2.0874, "step": 200}, {"epoch": 3.9215686274509802, "eval_accuracy": 0.7696078431372549, "eval_f1": 0.7684729064039408, "eval_loss": 0.5166981816291809, "eval_precision": 0.7722772277227723, "eval_recall": 0.7647058823529411, "eval_runtime": 2.4137, "eval_samples_per_second": 84.516, "eval_steps_per_second": 21.129, "step": 200}], "logging_steps": 50, "max_steps": 1020, "num_input_tokens_seen": 0, "num_train_epochs": 20, "save_steps": 200, "stateful_callbacks": {"EarlyStoppingCallback": {"args": {"early_stopping_patience": 8, "early_stopping_threshold": 0.0}, "attributes": {"early_stopping_patience_counter": 0}}, "TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 0.0, "train_batch_size": 4, "trial_name": null, "trial_params": null}