{"best_global_step": 200, "best_metric": 0.7673469387755103, "best_model_checkpoint": "outputs/cv_lora/splicebert-ms1024/fold_4/checkpoint-200", "epoch": 3.9215686274509802, "eval_steps": 100, "global_step": 200, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.9803921568627451, "grad_norm": 1.9876924753189087, "learning_rate": 5e-05, "loss": 2.7709, "step": 50}, {"epoch": 1.9607843137254903, "grad_norm": 4.195080280303955, "learning_rate": 0.0001, "loss": 2.7221, "step": 100}, {"epoch": 1.9607843137254903, "eval_accuracy": 0.5911330049261084, "eval_f1": 0.6066350710900474, "eval_loss": 0.6684377789497375, "eval_precision": 0.5818181818181818, "eval_recall": 0.6336633663366337, "eval_runtime": 3.6744, "eval_samples_per_second": 55.247, "eval_steps_per_second": 13.88, "step": 100}, {"epoch": 2.9411764705882355, "grad_norm": NaN, "learning_rate": 9.478260869565218e-05, "loss": 2.3876, "step": 150}, {"epoch": 3.9215686274509802, "grad_norm": 5.60146427154541, "learning_rate": 8.934782608695653e-05, "loss": 2.0797, "step": 200}, {"epoch": 3.9215686274509802, "eval_accuracy": 0.7192118226600985, "eval_f1": 0.7673469387755103, "eval_loss": 0.572607159614563, "eval_precision": 0.6527777777777778, "eval_recall": 0.9306930693069307, "eval_runtime": 3.5554, "eval_samples_per_second": 57.096, "eval_steps_per_second": 14.344, "step": 200}], "logging_steps": 50, "max_steps": 1020, "num_input_tokens_seen": 0, "num_train_epochs": 20, "save_steps": 200, "stateful_callbacks": {"EarlyStoppingCallback": {"args": {"early_stopping_patience": 8, "early_stopping_threshold": 0.0}, "attributes": {"early_stopping_patience_counter": 0}}, "TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 0.0, "train_batch_size": 4, "trial_name": null, "trial_params": null}