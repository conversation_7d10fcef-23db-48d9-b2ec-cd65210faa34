{"best_global_step": 400, "best_metric": 0.7896995708154506, "best_model_checkpoint": "outputs/cv_lora/splicebert-ms1024/fold_4/checkpoint-400", "epoch": 20.0, "eval_steps": 100, "global_step": 1020, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.9803921568627451, "grad_norm": 1.9876924753189087, "learning_rate": 5e-05, "loss": 2.7709, "step": 50}, {"epoch": 1.9607843137254903, "grad_norm": 4.195080280303955, "learning_rate": 0.0001, "loss": 2.7221, "step": 100}, {"epoch": 1.9607843137254903, "eval_accuracy": 0.5911330049261084, "eval_f1": 0.6066350710900474, "eval_loss": 0.6684377789497375, "eval_precision": 0.5818181818181818, "eval_recall": 0.6336633663366337, "eval_runtime": 3.6744, "eval_samples_per_second": 55.247, "eval_steps_per_second": 13.88, "step": 100}, {"epoch": 2.9411764705882355, "grad_norm": NaN, "learning_rate": 9.478260869565218e-05, "loss": 2.3876, "step": 150}, {"epoch": 3.9215686274509802, "grad_norm": 5.60146427154541, "learning_rate": 8.934782608695653e-05, "loss": 2.0797, "step": 200}, {"epoch": 3.9215686274509802, "eval_accuracy": 0.7192118226600985, "eval_f1": 0.7673469387755103, "eval_loss": 0.572607159614563, "eval_precision": 0.6527777777777778, "eval_recall": 0.9306930693069307, "eval_runtime": 3.5554, "eval_samples_per_second": 57.096, "eval_steps_per_second": 14.344, "step": 200}, {"epoch": 4.901960784313726, "grad_norm": 5.815230369567871, "learning_rate": 8.391304347826088e-05, "loss": 1.933, "step": 250}, {"epoch": 5.882352941176471, "grad_norm": NaN, "learning_rate": 7.858695652173913e-05, "loss": 1.762, "step": 300}, {"epoch": 5.882352941176471, "eval_accuracy": 0.7487684729064039, "eval_f1": 0.7829787234042552, "eval_loss": 0.5188917517662048, "eval_precision": 0.6865671641791045, "eval_recall": 0.9108910891089109, "eval_runtime": 2.8309, "eval_samples_per_second": 71.709, "eval_steps_per_second": 18.016, "step": 300}, {"epoch": 6.862745098039216, "grad_norm": 9.433487892150879, "learning_rate": 7.315217391304349e-05, "loss": 1.705, "step": 350}, {"epoch": 7.8431372549019605, "grad_norm": 3.256493091583252, "learning_rate": 6.771739130434783e-05, "loss": 1.5963, "step": 400}, {"epoch": 7.8431372549019605, "eval_accuracy": 0.7586206896551724, "eval_f1": 0.7896995708154506, "eval_loss": 0.5280916690826416, "eval_precision": 0.696969696969697, "eval_recall": 0.9108910891089109, "eval_runtime": 3.0689, "eval_samples_per_second": 66.148, "eval_steps_per_second": 16.618, "step": 400}, {"epoch": 8.823529411764707, "grad_norm": 3.7376928329467773, "learning_rate": 6.228260869565218e-05, "loss": 1.5306, "step": 450}, {"epoch": 9.803921568627452, "grad_norm": 11.499629974365234, "learning_rate": 5.6847826086956524e-05, "loss": 1.497, "step": 500}, {"epoch": 9.803921568627452, "eval_accuracy": 0.7389162561576355, "eval_f1": 0.7685589519650655, "eval_loss": 0.5271603465080261, "eval_precision": 0.6875, "eval_recall": 0.8712871287128713, "eval_runtime": 3.2588, "eval_samples_per_second": 62.292, "eval_steps_per_second": 15.65, "step": 500}, {"epoch": 10.784313725490197, "grad_norm": 15.104257583618164, "learning_rate": 5.141304347826087e-05, "loss": 1.4693, "step": 550}, {"epoch": 11.764705882352942, "grad_norm": 17.208215713500977, "learning_rate": 4.597826086956522e-05, "loss": 1.3787, "step": 600}, {"epoch": 11.764705882352942, "eval_accuracy": 0.7487684729064039, "eval_f1": 0.7753303964757708, "eval_loss": 0.5732812285423279, "eval_precision": 0.6984126984126984, "eval_recall": 0.8712871287128713, "eval_runtime": 2.7298, "eval_samples_per_second": 74.363, "eval_steps_per_second": 18.682, "step": 600}, {"epoch": 12.745098039215687, "grad_norm": 19.779687881469727, "learning_rate": 4.054347826086957e-05, "loss": 1.346, "step": 650}, {"epoch": 13.72549019607843, "grad_norm": 22.978633880615234, "learning_rate": 3.510869565217392e-05, "loss": 1.3288, "step": 700}, {"epoch": 13.72549019607843, "eval_accuracy": 0.7389162561576355, "eval_f1": 0.7685589519650655, "eval_loss": 0.5839979648590088, "eval_precision": 0.6875, "eval_recall": 0.8712871287128713, "eval_runtime": 3.0023, "eval_samples_per_second": 67.614, "eval_steps_per_second": 16.987, "step": 700}, {"epoch": 14.705882352941176, "grad_norm": 6.460373878479004, "learning_rate": 2.967391304347826e-05, "loss": 1.2595, "step": 750}, {"epoch": 15.686274509803921, "grad_norm": 10.859024047851562, "learning_rate": 2.423913043478261e-05, "loss": 1.2909, "step": 800}, {"epoch": 15.686274509803921, "eval_accuracy": 0.7389162561576355, "eval_f1": 0.7665198237885461, "eval_loss": 0.5869380235671997, "eval_precision": 0.6904761904761905, "eval_recall": 0.8613861386138614, "eval_runtime": 3.2017, "eval_samples_per_second": 63.405, "eval_steps_per_second": 15.929, "step": 800}, {"epoch": 16.666666666666668, "grad_norm": 9.940821647644043, "learning_rate": 1.8913043478260868e-05, "loss": 1.1722, "step": 850}, {"epoch": 17.647058823529413, "grad_norm": 8.383382797241211, "learning_rate": 1.3478260869565218e-05, "loss": 1.3546, "step": 900}, {"epoch": 17.647058823529413, "eval_accuracy": 0.7339901477832512, "eval_f1": 0.763157894736842, "eval_loss": 0.6068034768104553, "eval_precision": 0.6850393700787402, "eval_recall": 0.8613861386138614, "eval_runtime": 3.2323, "eval_samples_per_second": 62.803, "eval_steps_per_second": 15.778, "step": 900}, {"epoch": 18.627450980392158, "grad_norm": 12.698315620422363, "learning_rate": 8.043478260869565e-06, "loss": 1.2182, "step": 950}, {"epoch": 19.607843137254903, "grad_norm": 15.755779266357422, "learning_rate": 2.608695652173913e-06, "loss": 1.0871, "step": 1000}, {"epoch": 19.607843137254903, "eval_accuracy": 0.729064039408867, "eval_f1": 0.7577092511013215, "eval_loss": 0.612378716468811, "eval_precision": 0.6825396825396826, "eval_recall": 0.8514851485148515, "eval_runtime": 2.8138, "eval_samples_per_second": 72.143, "eval_steps_per_second": 18.125, "step": 1000}], "logging_steps": 50, "max_steps": 1020, "num_input_tokens_seen": 0, "num_train_epochs": 20, "save_steps": 200, "stateful_callbacks": {"EarlyStoppingCallback": {"args": {"early_stopping_patience": 8, "early_stopping_threshold": 0.0}, "attributes": {"early_stopping_patience_counter": 6}}, "TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": true}, "attributes": {}}}, "total_flos": 0.0, "train_batch_size": 4, "trial_name": null, "trial_params": null}