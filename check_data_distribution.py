#!/usr/bin/env python3
"""
Check data distribution and class balance.
"""

import os
import sys
import numpy as np
from collections import Counter

# Add paths
sys.path.append('.')
sys.path.append('downstream')

from sliding_window_dataset import load_fasta_data

def check_data_distribution():
    """Check data distribution across folds."""
    
    data_path = "data/cv_splits_565"
    
    print("🔍 Checking Data Distribution")
    print("=" * 50)
    
    # Check all folds
    for fold in range(5):
        train_file = f"fold_{fold}_train.fa"
        val_file = f"fold_{fold}_val.fa"
        
        print(f"\n--- Fold {fold} ---")
        
        # Load training data
        train_sequences, train_labels = load_fasta_data(os.path.join(data_path, train_file))
        train_counter = Counter(train_labels)
        
        # Load validation data
        val_sequences, val_labels = load_fasta_data(os.path.join(data_path, val_file))
        val_counter = Counter(val_labels)
        
        print(f"Training: {len(train_sequences)} sequences")
        print(f"  Class distribution: {dict(train_counter)}")
        print(f"  Class 0: {train_counter[0]} ({train_counter[0]/len(train_labels)*100:.1f}%)")
        print(f"  Class 1: {train_counter[1]} ({train_counter[1]/len(train_labels)*100:.1f}%)")
        
        print(f"Validation: {len(val_sequences)} sequences")
        print(f"  Class distribution: {dict(val_counter)}")
        print(f"  Class 0: {val_counter[0]} ({val_counter[0]/len(val_labels)*100:.1f}%)")
        print(f"  Class 1: {val_counter[1]} ({val_counter[1]/len(val_labels)*100:.1f}%)")
        
        # Check for severe imbalance
        train_ratio = min(train_counter[0], train_counter[1]) / max(train_counter[0], train_counter[1])
        val_ratio = min(val_counter[0], val_counter[1]) / max(val_counter[0], val_counter[1])
        
        if train_ratio < 0.3:
            print(f"  ⚠️  Training set is imbalanced (ratio: {train_ratio:.2f})")
        if val_ratio < 0.3:
            print(f"  ⚠️  Validation set is imbalanced (ratio: {val_ratio:.2f})")
    
    # Check test set
    test_file = "final_test.fa"
    if os.path.exists(os.path.join(data_path, test_file)):
        print(f"\n--- Test Set ---")
        test_sequences, test_labels = load_fasta_data(os.path.join(data_path, test_file))
        test_counter = Counter(test_labels)
        
        print(f"Test: {len(test_sequences)} sequences")
        print(f"  Class distribution: {dict(test_counter)}")
        print(f"  Class 0: {test_counter[0]} ({test_counter[0]/len(test_labels)*100:.1f}%)")
        print(f"  Class 1: {test_counter[1]} ({test_counter[1]/len(test_labels)*100:.1f}%)")

def check_original_data():
    """Check original data distribution."""
    
    original_file = "data/565_hum_label.fa"
    
    print(f"\n--- Original Data ---")
    
    if os.path.exists(original_file):
        sequences, labels = load_fasta_data(original_file)
        counter = Counter(labels)
        
        print(f"Original: {len(sequences)} sequences")
        print(f"  Class distribution: {dict(counter)}")
        print(f"  Class 0: {counter[0]} ({counter[0]/len(labels)*100:.1f}%)")
        print(f"  Class 1: {counter[1]} ({counter[1]/len(labels)*100:.1f}%)")
        
        # Check balance
        ratio = min(counter[0], counter[1]) / max(counter[0], counter[1])
        print(f"  Balance ratio: {ratio:.2f}")
        
        if ratio < 0.8:
            print(f"  ⚠️  Data is imbalanced")
        else:
            print(f"  ✅ Data is well balanced")
    else:
        print(f"  ❌ Original file not found: {original_file}")

if __name__ == "__main__":
    check_data_distribution()
    check_original_data()
