#!/usr/bin/env python3
"""
Data splitting script for 565_hum_label.fa dataset
Splits data into 10% final test set and 90% for 5-fold cross-validation
"""

import os
import random
import numpy as np
from sklearn.model_selection import StratifiedKFold, train_test_split
from collections import defaultdict, Counter
import argparse

def set_seed(seed=42):
    """Set random seeds for reproducibility."""
    random.seed(seed)
    np.random.seed(seed)

def load_fasta_with_labels(fasta_path):
    """Load FASTA file with labels in header."""
    sequences = []
    labels = []
    sequence_ids = []
    
    with open(fasta_path, 'r') as f:
        lines = f.readlines()
    
    i = 0
    seq_id = 0
    while i < len(lines):
        line = lines[i].strip()
        if line.startswith('label='):
            # Extract label
            label = int(line.split('=')[1])
            labels.append(label)
            
            # Get sequence (next line)
            if i + 1 < len(lines):
                sequence = lines[i + 1].strip()
                sequences.append(sequence)
                sequence_ids.append(f"seq_{seq_id}")
                seq_id += 1
            i += 2
        else:
            i += 1
    
    return sequences, labels, sequence_ids

def save_fasta_with_labels(sequences, labels, sequence_ids, output_path):
    """Save sequences and labels to FASTA format."""
    with open(output_path, 'w') as f:
        for seq_id, seq, label in zip(sequence_ids, sequences, labels):
            f.write(f"label={label}\n")
            f.write(f"{seq}\n")

def create_data_splits(sequences, labels, sequence_ids, test_size=0.1, cv_folds=5, seed=42):
    """Create train/test split and cross-validation folds."""
    set_seed(seed)
    
    # First split: 90% for CV, 10% for final test
    train_sequences, test_sequences, train_labels, test_labels, train_ids, test_ids = train_test_split(
        sequences, labels, sequence_ids, 
        test_size=test_size, 
        stratify=labels, 
        random_state=seed
    )
    
    print(f"📊 Data Split Summary:")
    print(f"   Training set: {len(train_sequences)} sequences")
    print(f"     - Label 0: {sum(1 for l in train_labels if l == 0)}")
    print(f"     - Label 1: {sum(1 for l in train_labels if l == 1)}")
    print(f"   Test set: {len(test_sequences)} sequences")
    print(f"     - Label 0: {sum(1 for l in test_labels if l == 0)}")
    print(f"     - Label 1: {sum(1 for l in test_labels if l == 1)}")
    
    # Create cross-validation folds
    skf = StratifiedKFold(n_splits=cv_folds, shuffle=True, random_state=seed)
    cv_folds_data = []
    
    for fold_idx, (train_idx, val_idx) in enumerate(skf.split(train_sequences, train_labels)):
        fold_train_sequences = [train_sequences[i] for i in train_idx]
        fold_train_labels = [train_labels[i] for i in train_idx]
        fold_train_ids = [train_ids[i] for i in train_idx]
        
        fold_val_sequences = [train_sequences[i] for i in val_idx]
        fold_val_labels = [train_labels[i] for i in val_idx]
        fold_val_ids = [train_ids[i] for i in val_idx]
        
        cv_folds_data.append({
            'fold': fold_idx,
            'train': (fold_train_sequences, fold_train_labels, fold_train_ids),
            'val': (fold_val_sequences, fold_val_labels, fold_val_ids)
        })
        
        print(f"   Fold {fold_idx}: Train={len(fold_train_sequences)}, Val={len(fold_val_sequences)}")
    
    return {
        'final_test': (test_sequences, test_labels, test_ids),
        'cv_folds': cv_folds_data
    }

def save_data_splits(data_splits, output_dir):
    """Save all data splits to files."""
    os.makedirs(output_dir, exist_ok=True)
    
    # Save final test set
    test_sequences, test_labels, test_ids = data_splits['final_test']
    save_fasta_with_labels(
        test_sequences, test_labels, test_ids,
        os.path.join(output_dir, 'final_test.fa')
    )
    
    # Save cross-validation folds
    for fold_data in data_splits['cv_folds']:
        fold_idx = fold_data['fold']
        
        # Training fold
        train_sequences, train_labels, train_ids = fold_data['train']
        save_fasta_with_labels(
            train_sequences, train_labels, train_ids,
            os.path.join(output_dir, f'fold_{fold_idx}_train.fa')
        )
        
        # Validation fold
        val_sequences, val_labels, val_ids = fold_data['val']
        save_fasta_with_labels(
            val_sequences, val_labels, val_ids,
            os.path.join(output_dir, f'fold_{fold_idx}_val.fa')
        )
    
    # Save split summary
    with open(os.path.join(output_dir, 'split_summary.txt'), 'w') as f:
        f.write("565_hum_label.fa Data Split Summary\n")
        f.write("=" * 40 + "\n\n")
        
        test_sequences, test_labels, test_ids = data_splits['final_test']
        f.write(f"Final test set: {len(test_sequences)} sequences\n")
        f.write(f"  Label distribution: {Counter(test_labels)}\n\n")
        
        f.write("Cross-validation folds:\n")
        for fold_data in data_splits['cv_folds']:
            fold_idx = fold_data['fold']
            train_sequences, train_labels, train_ids = fold_data['train']
            val_sequences, val_labels, val_ids = fold_data['val']
            
            f.write(f"  Fold {fold_idx}:\n")
            f.write(f"    Train: {len(train_sequences)} sequences, {Counter(train_labels)}\n")
            f.write(f"    Val: {len(val_sequences)} sequences, {Counter(val_labels)}\n")

def analyze_sequence_lengths(sequences, labels):
    """Analyze sequence length distribution."""
    seq_lengths = [len(seq) for seq in sequences]
    
    print(f"\n📏 Sequence Length Analysis:")
    print(f"   Total sequences: {len(sequences)}")
    print(f"   Length range: {min(seq_lengths)}-{max(seq_lengths)} bp")
    print(f"   Mean length: {np.mean(seq_lengths):.1f} bp")
    print(f"   Median length: {np.median(seq_lengths):.1f} bp")
    
    # Sliding window analysis
    long_sequences = sum(1 for l in seq_lengths if l > 1024)
    very_long_sequences = sum(1 for l in seq_lengths if l > 9216)
    
    print(f"   Sequences > 1024 bp (need sliding window): {long_sequences} ({long_sequences/len(sequences)*100:.1f}%)")
    print(f"   Sequences > 9216 bp (need truncation): {very_long_sequences} ({very_long_sequences/len(sequences)*100:.1f}%)")

def main():
    parser = argparse.ArgumentParser(description='Split 565_hum_label.fa for cross-validation')
    parser.add_argument('--input', default='data/565_hum_label.fa', help='Input FASTA file')
    parser.add_argument('--output_dir', default='data/cv_splits_565', help='Output directory')
    parser.add_argument('--test_size', type=float, default=0.1, help='Test set size (default: 0.1)')
    parser.add_argument('--cv_folds', type=int, default=5, help='Number of CV folds (default: 5)')
    parser.add_argument('--seed', type=int, default=42, help='Random seed (default: 42)')
    
    args = parser.parse_args()
    
    print("🧬 lncRNA Function Prediction - Data Splitting for 565_hum_label.fa")
    print("=" * 70)
    
    # Load data
    print(f"Loading data from: {args.input}")
    sequences, labels, sequence_ids = load_fasta_with_labels(args.input)
    
    # Analyze sequence lengths
    analyze_sequence_lengths(sequences, labels)
    
    # Create data splits
    print(f"\nCreating data splits (test_size={args.test_size}, cv_folds={args.cv_folds}, seed={args.seed})")
    data_splits = create_data_splits(
        sequences, labels, sequence_ids,
        test_size=args.test_size,
        cv_folds=args.cv_folds,
        seed=args.seed
    )
    
    # Save splits
    print(f"\nSaving data splits to: {args.output_dir}")
    save_data_splits(data_splits, args.output_dir)
    
    print(f"\n✅ Data splitting completed!")
    print(f"Files saved in: {args.output_dir}")

if __name__ == "__main__":
    main()
