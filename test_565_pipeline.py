#!/usr/bin/env python3
"""
Test script for 565_hum_label.fa lncRNA function prediction pipeline
Validates environment, data, and model components
"""

import os
import sys
import torch
import numpy as np
import json
from collections import Counter

def test_environment():
    """Test Python environment and dependencies."""
    print("🔧 Testing Environment")
    print("-" * 30)
    
    # Python version
    print(f"Python version: {sys.version}")
    
    # Required packages
    required_packages = [
        'torch', 'transformers', 'sklearn', 'numpy', 'pandas', 
        'matplotlib', 'seaborn', 'scipy'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - MISSING")
            missing_packages.append(package)
    
    # CUDA availability
    if torch.cuda.is_available():
        print(f"✅ CUDA available: {torch.cuda.get_device_name()}")
        print(f"   GPU memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
    else:
        print("⚠️  CUDA not available - will use CPU")
    
    if missing_packages:
        print(f"\n❌ Missing packages: {missing_packages}")
        return False
    
    print("✅ Environment check passed")
    return True

def test_data_files():
    """Test data files and structure."""
    print("\n📁 Testing Data Files")
    print("-" * 30)
    
    # Check original data file
    original_file = 'data/565_hum_label.fa'
    if os.path.exists(original_file):
        print(f"✅ Original data file: {original_file}")
        
        # Quick validation
        sequences = []
        labels = []
        with open(original_file, 'r') as f:
            lines = f.readlines()
        
        i = 0
        while i < len(lines):
            line = lines[i].strip()
            if line.startswith('label='):
                label = int(line.split('=')[1])
                labels.append(label)
                if i + 1 < len(lines):
                    sequence = lines[i + 1].strip()
                    sequences.append(sequence)
                i += 2
            else:
                i += 1
        
        print(f"   Total sequences: {len(sequences)}")
        print(f"   Label distribution: {Counter(labels)}")
        
        # Check sequence lengths
        seq_lengths = [len(seq) for seq in sequences]
        print(f"   Length range: {min(seq_lengths)}-{max(seq_lengths)} bp")
        print(f"   Mean length: {np.mean(seq_lengths):.1f} bp")
        print(f"   Sequences > 1024: {sum(1 for l in seq_lengths if l > 1024)}")
        print(f"   Sequences > 9216: {sum(1 for l in seq_lengths if l > 9216)}")
        
    else:
        print(f"❌ Original data file not found: {original_file}")
        return False
    
    # Check CV splits
    cv_dir = 'data/cv_splits_565'
    if os.path.exists(cv_dir):
        print(f"✅ CV splits directory: {cv_dir}")
        
        expected_files = ['final_test.fa'] + [f'fold_{i}_{split}.fa' for i in range(5) for split in ['train', 'val']]
        missing_files = []
        
        for file in expected_files:
            file_path = os.path.join(cv_dir, file)
            if os.path.exists(file_path):
                print(f"   ✅ {file}")
            else:
                print(f"   ❌ {file} - MISSING")
                missing_files.append(file)
        
        if missing_files:
            print(f"❌ Missing CV split files: {missing_files}")
            return False
    else:
        print(f"❌ CV splits directory not found: {cv_dir}")
        print("   Run: python data_split_565.py")
        return False
    
    print("✅ Data files check passed")
    return True

def test_model_imports():
    """Test model and tokenizer imports."""
    print("\n🤖 Testing Model Imports")
    print("-" * 30)
    
    # Test tokenizer
    try:
        sys.path.append('.')
        sys.path.append('downstream')
        from tokenizer.tokenization_opensource import OpenRnaLMTokenizer
        print("✅ OpenRnaLMTokenizer")
    except ImportError as e:
        print(f"❌ OpenRnaLMTokenizer - {e}")
        return False
    
    # Test RNA-FM
    try:
        from model.rnafm.modeling_rnafm import RnaFmForSequenceClassification
        print("✅ RnaFmForSequenceClassification")
    except ImportError as e:
        print(f"❌ RnaFmForSequenceClassification - {e}")
        print("   Check if RNA-FM model files are available")
    
    # Test SpliceBERT
    try:
        from model.splicebert.modeling_splicebert import SpliceBertForSequenceClassification
        print("✅ SpliceBertForSequenceClassification")
    except ImportError as e:
        print(f"❌ SpliceBertForSequenceClassification - {e}")
        print("   Check if SpliceBERT model files are available")
    
    print("✅ Model imports check passed")
    return True

def test_custom_modules():
    """Test custom modules."""
    print("\n🔧 Testing Custom Modules")
    print("-" * 30)
    
    # Test sliding window dataset
    try:
        from sliding_window_dataset import SlidingWindowDataset, load_fasta_data
        print("✅ SlidingWindowDataset")
        
        # Quick test
        sequences = ["ATCG" * 100, "GCTA" * 500]
        labels = [0, 1]
        
        class DummyTokenizer:
            def __call__(self, text, **kwargs):
                tokens = [ord(c) for c in text[:kwargs.get('max_length', len(text))]]
                max_len = kwargs.get('max_length', len(tokens))
                
                if len(tokens) < max_len:
                    tokens.extend([0] * (max_len - len(tokens)))
                else:
                    tokens = tokens[:max_len]
                
                attention_mask = [1 if t != 0 else 0 for t in tokens]
                
                return {
                    'input_ids': torch.tensor([tokens]),
                    'attention_mask': torch.tensor([attention_mask])
                }
        
        tokenizer = DummyTokenizer()
        dataset = SlidingWindowDataset(sequences, labels, tokenizer)
        print(f"   Test dataset length: {len(dataset)}")
        
    except Exception as e:
        print(f"❌ SlidingWindowDataset - {e}")
        return False
    
    # Test sequence level model
    try:
        from sequence_level_model import SequenceLevelAggregationModel
        print("✅ SequenceLevelAggregationModel")
    except Exception as e:
        print(f"❌ SequenceLevelAggregationModel - {e}")
        return False
    
    print("✅ Custom modules check passed")
    return True

def test_training_script():
    """Test training script syntax."""
    print("\n🚀 Testing Training Script")
    print("-" * 30)
    
    # Check if training script exists and is syntactically correct
    script_path = 'train_full_finetune_565.py'
    if os.path.exists(script_path):
        print(f"✅ Training script exists: {script_path}")
        
        # Test syntax by compiling
        try:
            with open(script_path, 'r') as f:
                script_content = f.read()
            compile(script_content, script_path, 'exec')
            print("✅ Training script syntax is valid")
        except SyntaxError as e:
            print(f"❌ Training script syntax error: {e}")
            return False
    else:
        print(f"❌ Training script not found: {script_path}")
        return False
    
    # Check CV training script
    cv_script_path = 'run_cv_full_finetune_565.py'
    if os.path.exists(cv_script_path):
        print(f"✅ CV training script exists: {cv_script_path}")
    else:
        print(f"❌ CV training script not found: {cv_script_path}")
        return False
    
    print("✅ Training scripts check passed")
    return True

def create_test_config():
    """Create a test configuration file."""
    print("\n⚙️  Creating Test Configuration")
    print("-" * 30)
    
    config = {
        "window_size": 1024,
        "window_stride": 512,
        "max_sequence_length": 9216,
        "pooling_strategy": "mean",
        "batch_size": 2,  # Small batch size for testing
        "gradient_accumulation_steps": 2,
        "learning_rate": 2e-5,
        "num_epochs": 2,  # Few epochs for testing
        "patience": 5,
        "warmup_steps": 10,
        "logging_steps": 5,
        "eval_steps": 10,
        "save_steps": 50,
        "seed": 42
    }
    
    config_file = 'test_config_565.json'
    with open(config_file, 'w') as f:
        json.dump(config, f, indent=2)
    
    print(f"✅ Test configuration saved: {config_file}")
    return config_file

def run_quick_test():
    """Run a quick end-to-end test with minimal data."""
    print("\n🧪 Running Quick End-to-End Test")
    print("-" * 30)
    
    try:
        # Import required modules
        from sliding_window_dataset import SlidingWindowDataset
        from sequence_level_model import SequenceLevelAggregationModel
        
        # Create dummy data
        sequences = [
            "ATCGATCGATCG" * 50,  # 600 bp
            "GCTAGCTAGCTA" * 100,  # 1200 bp (needs sliding window)
            "TTAATTAATTAA" * 80   # 960 bp
        ]
        labels = [0, 1, 0]
        
        # Dummy tokenizer
        class TestTokenizer:
            def __call__(self, text, **kwargs):
                # Simple character-level encoding
                char_to_id = {'A': 1, 'T': 2, 'C': 3, 'G': 4}
                tokens = [char_to_id.get(c, 0) for c in text[:kwargs.get('max_length', len(text))]]
                max_len = kwargs.get('max_length', len(tokens))
                
                if len(tokens) < max_len:
                    tokens.extend([0] * (max_len - len(tokens)))
                else:
                    tokens = tokens[:max_len]
                
                attention_mask = [1 if t != 0 else 0 for t in tokens]
                
                return {
                    'input_ids': torch.tensor([tokens]),
                    'attention_mask': torch.tensor([attention_mask])
                }
        
        tokenizer = TestTokenizer()
        
        # Create dataset
        dataset = SlidingWindowDataset(
            sequences=sequences,
            labels=labels,
            tokenizer=tokenizer,
            window_size=512,  # Smaller for testing
            window_stride=256,
            max_length=2048,
            return_windows=False
        )
        
        print(f"✅ Created test dataset with {len(dataset)} sequences")
        
        # Test dataset statistics
        stats = dataset.get_dataset_stats()
        print(f"   Total windows: {stats['total_windows']}")
        print(f"   Avg windows per sequence: {stats['avg_windows_per_sequence']:.1f}")
        
        # Test getting an item
        item = dataset[0]
        print(f"✅ Dataset item shape: {item['input_ids'].shape}")
        
        # Create dummy base model
        class DummyBaseModel(torch.nn.Module):
            def __init__(self):
                super().__init__()
                self.embeddings = torch.nn.Embedding(5, 64)
                self.encoder = torch.nn.Linear(64, 64)
                self.config = type('Config', (), {'hidden_size': 64})()
            
            def forward(self, input_ids, attention_mask=None, **kwargs):
                embeddings = self.embeddings(input_ids)
                hidden_states = self.encoder(embeddings)
                return type('Output', (), {
                    'last_hidden_state': hidden_states,
                    'hidden_states': [hidden_states]
                })()
        
        # Test sequence-level model
        base_model = DummyBaseModel()
        model = SequenceLevelAggregationModel(base_model, pooling_strategy="mean")
        
        # Test forward pass
        with torch.no_grad():
            output = model(
                input_ids=item['input_ids'].unsqueeze(0),
                attention_mask=item['attention_mask'].unsqueeze(0),
                labels=item['labels'].unsqueeze(0)
            )
        
        print(f"✅ Model forward pass successful")
        print(f"   Output logits shape: {output.logits.shape}")
        print(f"   Loss: {output.loss.item():.4f}")
        
    except Exception as e:
        print(f"❌ Quick test failed: {e}")
        return False
    
    print("✅ Quick end-to-end test passed")
    return True

def main():
    """Main test function."""
    print("🧬 Testing 565_hum_label.fa lncRNA Function Prediction Pipeline")
    print("=" * 70)
    
    tests = [
        ("Environment", test_environment),
        ("Data Files", test_data_files),
        ("Model Imports", test_model_imports),
        ("Custom Modules", test_custom_modules),
        ("Training Scripts", test_training_script),
        ("Quick End-to-End", run_quick_test)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed_tests += 1
            else:
                print(f"❌ {test_name} test failed")
        except Exception as e:
            print(f"💥 {test_name} test crashed: {e}")
    
    # Create test configuration
    create_test_config()
    
    # Summary
    print("\n" + "="*70)
    print(f"🏆 TEST SUMMARY: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("🎉 All tests passed! Pipeline is ready to use.")
        print("\nNext steps:")
        print("1. Run data splitting: python data_split_565.py")
        print("2. Run CV training: python run_cv_full_finetune_565.py")
        print("3. Compare models: python compare_models_565.py")
    else:
        print("⚠️  Some tests failed. Please fix the issues before proceeding.")
        
        if not os.path.exists('data/cv_splits_565'):
            print("\n🔧 Quick fix: Run data splitting")
            print("   python data_split_565.py")
    
    return passed_tests == total_tests

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
