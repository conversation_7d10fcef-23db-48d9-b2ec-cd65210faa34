# lncRNA Function Prediction - 565_hum_label.fa Dataset

## 🎯 Overview

This implementation provides a complete pipeline for lncRNA function prediction using the 565_hum_label.fa dataset. It compares RNA-FM and SpliceBERT models using full parameter fine-tuning with 5-fold cross-validation.

## 📊 Dataset Information

- **File**: `data/565_hum_label.fa`
- **Total sequences**: 1,130 lncRNA sequences
- **Label distribution**: 565 functional (label=1) + 565 non-functional (label=0)
- **Sequence length**: 242-15,145 bp (average: 1,736 bp)
- **Sequences requiring sliding window**: 632 (>1024 bp)
- **Sequences requiring truncation**: 4 (>9216 bp)

## 🏗️ Architecture

### Sliding Window Processing
- **Window size**: 1024 bp
- **Window stride**: 512 bp
- **Maximum sequence length**: 9216 bp (truncation)

### Sequence-Level Aggregation
1. **Multi-window encoding**: Each window encoded separately
2. **Average pooling**: Window representations averaged
3. **Binary classification**: Final prediction head

### Models Compared
- **RNA-FM**: Pre-trained RNA foundation model
- **SpliceBERT-MS1024**: Pre-trained splicing-aware model

## 📁 File Structure

```
├── data/
│   ├── 565_hum_label.fa                 # Original dataset
│   └── cv_splits_565/                   # Cross-validation splits
│       ├── final_test.fa                # 10% final test set
│       ├── fold_0_train.fa              # Training folds
│       ├── fold_0_val.fa                # Validation folds
│       └── ...
├── sliding_window_dataset.py            # Sliding window data processing
├── sequence_level_model.py              # Sequence-level aggregation model
├── train_full_finetune_565.py           # Full fine-tuning training script
├── run_cv_full_finetune_565.py          # Cross-validation runner
├── compare_models_565.py                # Model comparison and visualization
├── data_split_565.py                    # Data splitting script
├── test_565_pipeline.py                 # Pipeline testing script
└── run_565_experiment.sh                # Complete pipeline runner
```

## 🚀 Quick Start

### 1. Run Complete Pipeline
```bash
./run_565_experiment.sh
```

### 2. Step-by-Step Execution

#### Step 1: Create Data Splits
```bash
python data_split_565.py --output_dir data/cv_splits_565
```

#### Step 2: Run Cross-Validation Training
```bash
python run_cv_full_finetune_565.py \
    --data_dir data/cv_splits_565 \
    --output_dir outputs/full_finetune_565 \
    --models rna-fm splicebert-ms1024
```

#### Step 3: Compare Models
```bash
python compare_models_565.py \
    --results_dir outputs/full_finetune_565 \
    --output_dir outputs/comparison_565
```

## ⚙️ Configuration

### Training Parameters
- **Batch size**: 4
- **Learning rate**: 2e-5
- **Epochs**: 20
- **Early stopping patience**: 10
- **Gradient accumulation**: 4 steps

### Model Parameters
- **Window size**: 1024
- **Window stride**: 512
- **Max sequence length**: 9216
- **Pooling strategy**: Mean pooling

## 📊 Output Files

### Training Results
- `outputs/full_finetune_565/cv_results.json` - Detailed CV results
- `outputs/full_finetune_565/cv_summary_results.csv` - Summary statistics
- `outputs/full_finetune_565/{model}/fold_{i}/` - Individual fold results

### Comparison Results
- `outputs/comparison_565/model_comparison_boxplots.png` - Performance box plots
- `outputs/comparison_565/model_comparison_radar.png` - Radar chart comparison
- `outputs/comparison_565/fold_wise_comparison.png` - Fold-wise performance
- `outputs/comparison_565/significance_table.png` - Statistical tests
- `outputs/comparison_565/comparison_report.md` - Comprehensive report

## 🔧 Key Features

### 1. Sliding Window Processing
- Handles variable-length sequences (242-15,145 bp)
- Efficient windowing with 1024/512 window/stride
- Automatic truncation for very long sequences

### 2. Sequence-Level Aggregation
- Multiple windows per sequence encoded separately
- Average pooling for sequence representation
- Maintains sequence-level labels

### 3. Full Parameter Fine-tuning
- All model parameters trainable
- Avoids parameter conflicts between models
- Proper gradient accumulation for large models

### 4. Robust Cross-Validation
- 5-fold stratified cross-validation
- 10% held-out final test set
- Balanced label distribution maintained

### 5. Comprehensive Evaluation
- Multiple metrics: Accuracy, Precision, Recall, F1, AUC
- Statistical significance testing
- Visual comparisons and reports

## 🧪 Testing

Run the test suite to validate the pipeline:
```bash
python test_565_pipeline.py
```

This will check:
- Environment and dependencies
- Data file integrity
- Model imports
- Custom module functionality
- Training script syntax

## 📋 Requirements

### Python Packages
- torch
- transformers
- scikit-learn
- numpy
- pandas
- matplotlib
- seaborn
- scipy

### Model Files
- RNA-FM model files in `./model/rnafm/`
- SpliceBERT model files in `./model/splicebert/`

## 🎯 Expected Results

### Performance Metrics
Both models should achieve:
- **Accuracy**: >0.85
- **F1-score**: >0.85
- **AUC**: >0.90

### Comparison Insights
- Model performance differences
- Statistical significance tests
- Fold-wise stability analysis

## 🔍 Troubleshooting

### Common Issues

1. **Missing model files**
   - Ensure RNA-FM and SpliceBERT models are properly installed
   - Check model paths in configuration

2. **Memory issues**
   - Reduce batch size in configuration
   - Use gradient accumulation for effective larger batches

3. **CUDA errors**
   - Verify GPU availability and memory
   - Fall back to CPU if necessary

4. **Data format issues**
   - Verify 565_hum_label.fa format
   - Check label format (label=0 or label=1)

### Performance Optimization

1. **GPU Usage**
   - Enable FP16 training for memory efficiency
   - Use appropriate batch sizes for your GPU

2. **Training Speed**
   - Adjust number of dataloader workers
   - Use gradient accumulation for larger effective batch sizes

3. **Memory Management**
   - Monitor GPU memory usage
   - Reduce sequence length if needed

## 📈 Customization

### Modify Window Parameters
Edit configuration in `cv_config_565.json`:
```json
{
    "window_size": 1024,
    "window_stride": 512,
    "max_sequence_length": 9216
}
```

### Change Pooling Strategy
Options: "mean", "max", "attention"
```python
--pooling_strategy "attention"
```

### Adjust Training Parameters
```bash
--learning_rate 1e-5
--num_train_epochs 30
--batch_size 8
```

## 🏆 Citation

If you use this implementation, please cite the relevant papers for RNA-FM and SpliceBERT models, as well as the original dataset source.

## 📞 Support

For issues and questions:
1. Check the troubleshooting section
2. Run the test suite
3. Review log files in output directories
4. Verify model and data file availability
