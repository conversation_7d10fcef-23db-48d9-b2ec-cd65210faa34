#!/usr/bin/env python3
"""
Evaluate and compare test results for RNA-FM and SpliceBERT-MS1024 models
"""

import os
import sys
import torch
import numpy as np
import pandas as pd
from sklearn.metrics import roc_auc_score, roc_curve, precision_recall_curve
import matplotlib.pyplot as plt
import json

sys.path.append('.')
from downstream.train_lncrna_function import SlidingWindowDataset, load_fasta_data
from tokenizer.tokenization_opensource import OpenRnaLMTokenizer
from transformers import EsmTokenizer
from model.rnafm.modeling_rnafm import RnaFmForSequenceClassification
from model.splicebert.modeling_splicebert import SpliceBertForSequenceClassification

def load_model_and_evaluate(model_type, model_path, test_data_path):
    """Load model and evaluate on test set"""
    
    print(f"\n{'='*50}")
    print(f"Evaluating {model_type.upper()} Model")
    print(f"{'='*50}")
    
    # Load tokenizer
    if model_type == 'rna-fm':
        tokenizer = OpenRnaLMTokenizer.from_pretrained(
            './checkpoint/opensource/rna-fm/',
            model_max_length=1024,
            padding_side="right",
            use_fast=True,
            trust_remote_code=True,
        )
    elif model_type == 'splicebert-ms1024':
        tokenizer = OpenRnaLMTokenizer.from_pretrained(
            './checkpoint/opensource/splicebert-ms1024/',
            model_max_length=1024,
            padding_side="right",
            use_fast=True,
            trust_remote_code=True,
        )
    
    # Load model
    if model_type == 'rna-fm':
        model = RnaFmForSequenceClassification.from_pretrained(
            model_path,
            num_labels=2,
            problem_type="single_label_classification",
            trust_remote_code=True,
        )
    elif model_type == 'splicebert-ms1024':
        model = SpliceBertForSequenceClassification.from_pretrained(
            model_path,
            num_labels=2,
            problem_type="single_label_classification",
            trust_remote_code=True,
        )
    
    model.eval()
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model.to(device)
    
    # Load test data
    sequences, labels = load_fasta_data(test_data_path)
    print(f"Loaded {len(sequences)} test samples")
    
    # Evaluate
    predictions = []
    probabilities = []
    true_labels = []
    
    with torch.no_grad():
        for i, (seq, label) in enumerate(zip(sequences, labels)):
            if i % 100 == 0:
                print(f"Processing sample {i}/{len(sequences)}")
            
            # Tokenize (use first 1024 tokens for simplicity)
            inputs = tokenizer(
                seq[:1024],  # Simplified: just use first 1024 characters
                padding='max_length',
                max_length=1024,
                truncation=True,
                return_tensors='pt'
            )
            
            inputs = {k: v.to(device) for k, v in inputs.items()}
            
            # Forward pass
            outputs = model(**inputs)
            logits = outputs.logits
            probs = torch.softmax(logits, dim=-1)
            
            predictions.append(torch.argmax(logits, dim=-1).cpu().item())
            probabilities.append(probs[0, 1].cpu().item())  # Probability of class 1
            true_labels.append(label)
    
    return np.array(true_labels), np.array(predictions), np.array(probabilities)

def calculate_metrics(y_true, y_pred, y_prob, model_name):
    """Calculate comprehensive metrics"""
    
    from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
    
    accuracy = accuracy_score(y_true, y_pred)
    precision = precision_score(y_true, y_pred, average='weighted')
    recall = recall_score(y_true, y_pred, average='weighted')
    f1 = f1_score(y_true, y_pred, average='weighted')
    auroc = roc_auc_score(y_true, y_prob)
    
    print(f"\n{model_name} Test Results:")
    print(f"{'='*40}")
    print(f"Accuracy:  {accuracy:.4f} ({accuracy*100:.2f}%)")
    print(f"Precision: {precision:.4f} ({precision*100:.2f}%)")
    print(f"Recall:    {recall:.4f} ({recall*100:.2f}%)")
    print(f"F1-Score:  {f1:.4f} ({f1*100:.2f}%)")
    print(f"AUROC:     {auroc:.4f} ({auroc*100:.2f}%)")
    
    return {
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1': f1,
        'auroc': auroc
    }

def plot_roc_curves(results_dict, save_path='model_comparison_roc.png'):
    """Plot ROC curves for both models"""
    
    plt.figure(figsize=(10, 8))
    
    colors = ['blue', 'red']
    model_names = list(results_dict.keys())
    
    for i, (model_name, data) in enumerate(results_dict.items()):
        y_true, y_prob = data['y_true'], data['y_prob']
        fpr, tpr, _ = roc_curve(y_true, y_prob)
        auroc = data['metrics']['auroc']
        
        plt.plot(fpr, tpr, color=colors[i], lw=2, 
                label=f'{model_name} (AUROC = {auroc:.4f})')
    
    plt.plot([0, 1], [0, 1], color='gray', lw=1, linestyle='--', alpha=0.5)
    plt.xlim([0.0, 1.0])
    plt.ylim([0.0, 1.05])
    plt.xlabel('False Positive Rate', fontsize=12)
    plt.ylabel('True Positive Rate', fontsize=12)
    plt.title('ROC Curves: lncRNA Function Prediction', fontsize=14, fontweight='bold')
    plt.legend(loc="lower right", fontsize=11)
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"\nROC curves saved to: {save_path}")

def main():
    """Main evaluation function"""
    
    print("🧬 lncRNA Function Prediction - Model Comparison")
    print("="*60)
    
    # Model paths (use best checkpoints)
    models = {
        'RNA-FM': {
            'type': 'rna-fm',
            'path': './outputs/ft/lncrna-function/lncRNA_function/rna-fm/seed_666/checkpoint-500'
        },
        'SpliceBERT-MS1024': {
            'type': 'splicebert-ms1024', 
            'path': './outputs/ft/lncrna-function/lncRNA_function/splicebert-ms1024/seed_666/checkpoint-1500'
        }
    }
    
    test_data_path = 'data/test.fa'
    results = {}
    
    # Evaluate each model
    for model_name, model_info in models.items():
        try:
            y_true, y_pred, y_prob = load_model_and_evaluate(
                model_info['type'], 
                model_info['path'], 
                test_data_path
            )
            
            metrics = calculate_metrics(y_true, y_pred, y_prob, model_name)
            
            results[model_name] = {
                'y_true': y_true,
                'y_pred': y_pred, 
                'y_prob': y_prob,
                'metrics': metrics
            }
            
        except Exception as e:
            print(f"Error evaluating {model_name}: {e}")
            continue
    
    # Compare results
    if len(results) == 2:
        print(f"\n{'='*60}")
        print("📊 MODEL COMPARISON SUMMARY")
        print(f"{'='*60}")
        
        comparison_table = []
        metrics_names = ['Accuracy', 'Precision', 'Recall', 'F1-Score', 'AUROC']
        
        print(f"{'Metric':<12} {'RNA-FM':<12} {'SpliceBERT':<12} {'Winner':<10}")
        print("-" * 50)
        
        for metric in ['accuracy', 'precision', 'recall', 'f1', 'auroc']:
            rna_fm_val = results['RNA-FM']['metrics'][metric]
            splicebert_val = results['SpliceBERT-MS1024']['metrics'][metric]
            
            winner = 'RNA-FM' if rna_fm_val > splicebert_val else 'SpliceBERT'
            winner_symbol = '🏆' if winner == 'SpliceBERT' else '🥈'
            
            print(f"{metric.capitalize():<12} {rna_fm_val:.4f}      {splicebert_val:.4f}      {winner} {winner_symbol}")
        
        # Plot ROC curves
        plot_roc_curves(results)
        
        # Save detailed results
        summary = {
            'RNA-FM': results['RNA-FM']['metrics'],
            'SpliceBERT-MS1024': results['SpliceBERT-MS1024']['metrics']
        }
        
        with open('test_results_comparison.json', 'w') as f:
            json.dump(summary, f, indent=2)
        
        print(f"\n✅ Detailed results saved to: test_results_comparison.json")
        print(f"✅ ROC curves saved to: model_comparison_roc.png")

if __name__ == "__main__":
    main()
