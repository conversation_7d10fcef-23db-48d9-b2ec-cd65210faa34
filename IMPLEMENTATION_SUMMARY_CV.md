# lncRNA Function Prediction - 5-Fold Cross-Validation Implementation Summary

## 🎯 任务概述

成功实现了基于567_hum_label.fa数据集的lncRNA功能预测5折交叉验证系统，用于比较RNA-FM和SpliceBERT-MS1024模型的性能。

## 📊 数据集信息

- **文件**: `data/567_hum_label.fa`
- **总序列数**: 1,134个lncRNA序列
- **标签分布**: 567个功能性(label=1) + 567个非功能性(label=0)
- **序列长度**: 242-205,012 bp (平均: 2,132 bp)
- **格式**: FASTA格式，标签在header中 (`>seq_id label=1`)

## 🔧 核心实现

### 1. 交叉验证训练脚本
**文件**: `downstream/train_lncrna_function_cv.py`

**主要特性**:
- 5折分层交叉验证，保持标签平衡
- 滑动窗口处理长序列（窗口=1024，步长=512）
- 支持RNA-FM和SpliceBERT-MS1024模型
- 自动保存每折的详细结果
- 计算综合统计指标（均值±标准差）

**关键组件**:
```python
class SlidingWindowDataset(Dataset)     # 处理滑动窗口数据
def train_fold()                        # 单折训练函数
def train_cross_validation()            # 主交叉验证函数
```

### 2. 运行脚本
**文件**: 
- `scripts/lncrna_function_cv/rna_fm_cv.sh`
- `scripts/lncrna_function_cv/splicebert_ms1024_cv.sh`
- `scripts/lncrna_function_cv/run_all_cv.sh`

**配置参数**:
```bash
n_folds=5                    # 5折交叉验证
cv_seed=42                   # 可重现的分割
window_size=1024             # 滑动窗口大小
window_stride=512            # 50%重叠
max_sequence_length=8192     # 最大序列长度
pooling_strategy="mean"      # 窗口融合策略
batch_size=4                 # 批大小
num_train_epochs=20          # 每折训练轮数
patience=5                   # 早停耐心值
```

### 3. 结果分析工具
**文件**: `analyze_cv_results.py`

**功能**:
- 加载和比较两个模型的交叉验证结果
- 生成可视化图表（箱线图、热力图）
- 创建详细的比较报告
- 导出CSV格式的比较表格

**输出**:
```
analysis_results/
├── metrics_comparison.png      # 模型性能箱线图比较
├── fold_wise_comparison.png    # 各折性能热力图
├── comparison_table.csv        # 详细比较表格
└── analysis_report.md          # 综合分析报告
```

### 4. 测试和验证工具
**文件**: 
- `test_cv_setup.py` - 验证设置和数据加载
- `run_lncrna_cv_demo.py` - 交互式演示脚本

## 📈 评估指标

每个模型使用以下指标评估：
- **准确率 (Accuracy)**: 整体分类准确率
- **F1分数 (F1 Score)**: 加权F1分数
- **精确率 (Precision)**: 加权精确率
- **召回率 (Recall)**: 加权召回率

结果包括：
- 5折的均值±标准差
- 各折的详细性能
- 模型间的统计比较

## 🚀 使用方法

### 快速开始
```bash
# 1. 验证设置
python test_cv_setup.py

# 2. 运行完整交叉验证
bash scripts/lncrna_function_cv/run_all_cv.sh

# 3. 分析结果
python analyze_cv_results.py
```

### 单独运行模型
```bash
# 仅RNA-FM
bash scripts/lncrna_function_cv/rna_fm_cv.sh

# 仅SpliceBERT-MS1024
bash scripts/lncrna_function_cv/splicebert_ms1024_cv.sh
```

### 交互式演示
```bash
python run_lncrna_cv_demo.py
```

## 📁 文件结构

```
├── downstream/
│   └── train_lncrna_function_cv.py     # 主交叉验证训练脚本
├── scripts/lncrna_function_cv/
│   ├── rna_fm_cv.sh                    # RNA-FM交叉验证脚本
│   ├── splicebert_ms1024_cv.sh         # SpliceBERT交叉验证脚本
│   └── run_all_cv.sh                   # 运行所有模型
├── data/
│   └── 567_hum_label.fa                # 数据集文件
├── test_cv_setup.py                    # 设置验证脚本
├── analyze_cv_results.py               # 结果分析脚本
├── run_lncrna_cv_demo.py               # 交互式演示脚本
├── README_lncRNA_function_cv.md        # 详细使用说明
└── IMPLEMENTATION_SUMMARY_CV.md        # 本文件
```

## 🔄 与原始实现的区别

| 方面 | 原始实现 | 交叉验证实现 |
|------|----------|-------------|
| **数据分割** | 固定训练/验证/测试集 | 5折交叉验证 |
| **数据集** | train.fa, val.fa, test.fa | 567_hum_label.fa |
| **评估方式** | 单次训练评估 | 5折平均性能 |
| **结果可靠性** | 依赖特定分割 | 更鲁棒的性能估计 |
| **训练脚本** | `train_lncrna_function.py` | `train_lncrna_function_cv.py` |
| **输出格式** | 单一结果 | 均值±标准差 |

## 🎯 关键优势

1. **更可靠的性能评估**: 5折交叉验证提供更鲁棒的性能估计
2. **统计显著性**: 提供置信区间和标准差
3. **公平比较**: 两个模型使用相同的数据分割
4. **完整自动化**: 一键运行完整实验流程
5. **详细分析**: 自动生成可视化和报告
6. **可重现性**: 固定随机种子确保结果一致

## ⚙️ 技术特点

### 滑动窗口机制
- 窗口大小: 1024核苷酸
- 步长: 512核苷酸 (50%重叠)
- 最大序列长度: 8192核苷酸
- 融合策略: 均值池化

### 内存优化
- 梯度累积: 4步
- 混合精度训练: FP16
- 批大小: 4 (适应滑动窗口)
- 数据加载器: 4个工作进程

### 早停机制
- 监控指标: F1分数
- 耐心值: 5个epoch
- 保存最佳模型: 是

## 📊 预期性能

基于原始实现，预期性能范围：
- **准确率**: 0.85-0.95
- **F1分数**: 0.85-0.95
- **模型间差异**: 通常<0.05

交叉验证为这些指标提供置信区间。

## 🔧 自定义选项

### 修改交叉验证参数
```bash
n_folds=5           # 折数
cv_seed=42          # 随机种子
num_train_epochs=20 # 每折训练轮数
patience=5          # 早停耐心值
```

### 调整滑动窗口设置
```bash
window_size=1024         # 窗口大小
window_stride=512        # 步长
max_sequence_length=8192 # 最大长度
pooling_strategy="mean"  # 融合策略
```

### 训练参数优化
```bash
batch_size=4                    # 批大小
lr=3e-5                        # 学习率
gradient_accumulation_steps=4   # 梯度累积
fp16=true                      # 混合精度
```

## 🏁 总结

这个5折交叉验证实现为lncRNA功能预测提供了一个完整、可靠的评估框架。它保持了原始实现的所有技术优势（滑动窗口、模型支持等），同时提供了更鲁棒的性能评估和详细的统计分析。

系统设计为完全自动化，从数据加载到结果分析都可以通过简单的命令完成，同时保持了高度的可定制性和可重现性。
