#!/bin/bash

# Script to compare RNA-FM and SpliceBERT-MS1024 models on lncRNA function prediction

echo "=========================================="
echo "lncRNA Function Prediction Model Comparison"
echo "=========================================="
echo "Comparing RNA-FM vs SpliceBERT-MS1024"
echo "Task: Binary classification of lncRNA functionality"
echo "Features: Sliding window processing for long sequences"
echo "Max sequence length: 8192 nucleotides"
echo "Window size: 1024, Stride: 512"
echo "=========================================="

# Create output directories
mkdir -p ./outputs/ft/lncrna-function/lncRNA_function/rna-fm
mkdir -p ./outputs/ft/lncrna-function/lncRNA_function/splicebert-ms1024
mkdir -p ./logs/lncrna-function

# Set log files
RNA_FM_LOG="./logs/lncrna-function/rna_fm_$(date +%Y%m%d_%H%M%S).log"
SPLICEBERT_LOG="./logs/lncrna-function/splicebert_ms1024_$(date +%Y%m%d_%H%M%S).log"

echo "Starting RNA-FM evaluation..."
echo "Log file: $RNA_FM_LOG"
bash ./scripts/lncrna_function/rna_fm.sh > $RNA_FM_LOG 2>&1

if [ $? -eq 0 ]; then
    echo "✓ RNA-FM evaluation completed successfully"
else
    echo "✗ RNA-FM evaluation failed. Check log: $RNA_FM_LOG"
fi

echo ""
echo "Starting SpliceBERT-MS1024 evaluation..."
echo "Log file: $SPLICEBERT_LOG"
bash ./scripts/lncrna_function/splicebert_ms1024.sh > $SPLICEBERT_LOG 2>&1

if [ $? -eq 0 ]; then
    echo "✓ SpliceBERT-MS1024 evaluation completed successfully"
else
    echo "✗ SpliceBERT-MS1024 evaluation failed. Check log: $SPLICEBERT_LOG"
fi

echo ""
echo "=========================================="
echo "Evaluation Summary"
echo "=========================================="
echo "RNA-FM log: $RNA_FM_LOG"
echo "SpliceBERT-MS1024 log: $SPLICEBERT_LOG"
echo ""
echo "Results can be found in:"
echo "- ./outputs/ft/lncrna-function/lncRNA_function/rna-fm/"
echo "- ./outputs/ft/lncrna-function/lncRNA_function/splicebert-ms1024/"
echo ""
echo "To extract final test results, check the end of each log file"
echo "or look for 'Test Results:' in the output directories."
echo "=========================================="
