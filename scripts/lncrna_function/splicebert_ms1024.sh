#!/bin/bash

# SpliceBERT-MS1024 model evaluation script for lncRNA function prediction

gpu_device="0"
nproc_per_node=1
master_port=$(shuf -i 10000-45000 -n 1)
echo "Using port $master_port for communication."

data_root=./data
model_root=./checkpoint

# SpliceBERT-MS1024 model configuration
MODEL_TYPE='splicebert-ms1024'
token='single'
pos='ape'  # absolute position embedding
model_max_length=1024
seed=666
MODEL_PATH=${model_root}/opensource/splicebert-ms1024/

# Task configuration
task='lncRNA_function'
batch_size=4  # Smaller batch size due to sliding windows
lr=3e-5
DATA_PATH=${data_root}
OUTPUT_PATH=./outputs/ft/lncrna-function/${task}/${MODEL_TYPE}

# Sliding window parameters
window_size=1024  # Match model's max length
window_stride=512  # 50% overlap
max_sequence_length=8192  # Truncate sequences longer than this
pooling_strategy="mean"  # Options: mean, max, attention

# Data files (FASTA format)
data_file_train=train.fa
data_file_val=val.fa
data_file_test=test.fa

EXEC_PREFIX="env CUDA_VISIBLE_DEVICES=$gpu_device torchrun --nproc_per_node=$nproc_per_node --master_port=$master_port"

echo "Model Path: ${MODEL_PATH}"
echo "Output Path: ${OUTPUT_PATH}"
echo "Window Size: ${window_size}, Stride: ${window_stride}"
echo "Max Sequence Length: ${max_sequence_length}"
echo "Pooling Strategy: ${pooling_strategy}"

${EXEC_PREFIX} \
downstream/train_lncrna_function.py \
    --model_name_or_path ${MODEL_PATH} \
    --data_path ${DATA_PATH} \
    --data_train_path ${data_file_train} \
    --data_val_path ${data_file_val} \
    --data_test_path ${data_file_test} \
    --run_name ${MODEL_TYPE}_lncrna_function_seed${seed} \
    --model_max_length ${model_max_length} \
    --per_device_train_batch_size ${batch_size} \
    --per_device_eval_batch_size ${batch_size} \
    --gradient_accumulation_steps 4 \
    --learning_rate ${lr} \
    --num_train_epochs 30 \
    --fp16 \
    --save_steps 500 \
    --output_dir ${OUTPUT_PATH}/seed_${seed} \
    --evaluation_strategy steps \
    --eval_steps 250 \
    --warmup_steps 100 \
    --logging_steps 100 \
    --overwrite_output_dir True \
    --log_level info \
    --seed ${seed} \
    --token_type ${token} \
    --model_type ${MODEL_TYPE} \
    --window_size ${window_size} \
    --window_stride ${window_stride} \
    --max_sequence_length ${max_sequence_length} \
    --pooling_strategy ${pooling_strategy} \
    --patience 10 \
    --load_best_model_at_end True \
    --metric_for_best_model eval_f1 \
    --greater_is_better True \
    --save_total_limit 3 \
    --dataloader_num_workers 4 \
    --dataloader_prefetch_factor 2

echo "SpliceBERT-MS1024 evaluation completed!"
