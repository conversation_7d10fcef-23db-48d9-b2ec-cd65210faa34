#!/usr/bin/env python3
"""
Data augmentation strategies for RNA sequences to improve model performance
"""

import random
import numpy as np
from Bio.Seq import Seq
from Bio.SeqRecord import SeqRecord
from Bio import SeqIO
import os

class RNADataAugmentation:
    """RNA sequence data augmentation techniques."""
    
    def __init__(self, seed=42):
        random.seed(seed)
        np.random.seed(seed)
    
    def reverse_complement(self, sequence):
        """Generate reverse complement of RNA sequence."""
        complement_map = {'A': 'U', 'U': 'A', 'G': 'C', 'C': 'G', 'T': 'U'}
        complement = ''.join([complement_map.get(base, base) for base in sequence])
        return complement[::-1]
    
    def random_mutation(self, sequence, mutation_rate=0.01):
        """Introduce random mutations."""
        bases = ['A', 'U', 'G', 'C']
        sequence = list(sequence)
        
        for i in range(len(sequence)):
            if random.random() < mutation_rate:
                current_base = sequence[i]
                new_bases = [b for b in bases if b != current_base]
                sequence[i] = random.choice(new_bases)
        
        return ''.join(sequence)
    
    def random_insertion(self, sequence, insertion_rate=0.005):
        """Random insertions."""
        bases = ['A', 'U', 'G', 'C']
        sequence = list(sequence)
        
        i = 0
        while i < len(sequence):
            if random.random() < insertion_rate:
                sequence.insert(i, random.choice(bases))
                i += 1  # Skip the inserted base
            i += 1
        
        return ''.join(sequence)
    
    def random_deletion(self, sequence, deletion_rate=0.005):
        """Random deletions."""
        sequence = list(sequence)
        
        i = 0
        while i < len(sequence):
            if random.random() < deletion_rate:
                sequence.pop(i)
            else:
                i += 1
        
        return ''.join(sequence)
    
    def sliding_window_crop(self, sequence, min_length=500, max_length=2000):
        """Extract random subsequences."""
        if len(sequence) < min_length:
            return sequence
        
        crop_length = random.randint(min_length, min(max_length, len(sequence)))
        start_pos = random.randint(0, len(sequence) - crop_length)
        
        return sequence[start_pos:start_pos + crop_length]
    
    def augment_sequence(self, sequence, label, num_augmentations=3):
        """Apply multiple augmentation techniques."""
        augmented_sequences = []
        
        # Original sequence
        augmented_sequences.append((sequence, label))
        
        for i in range(num_augmentations):
            aug_seq = sequence
            
            # Apply random combination of augmentations
            if random.random() < 0.3:  # 30% chance of reverse complement
                aug_seq = self.reverse_complement(aug_seq)
            
            if random.random() < 0.5:  # 50% chance of mutation
                aug_seq = self.random_mutation(aug_seq, mutation_rate=0.005)
            
            if random.random() < 0.3:  # 30% chance of insertion
                aug_seq = self.random_insertion(aug_seq, insertion_rate=0.002)
            
            if random.random() < 0.3:  # 30% chance of deletion
                aug_seq = self.random_deletion(aug_seq, deletion_rate=0.002)
            
            if random.random() < 0.4:  # 40% chance of cropping
                aug_seq = self.sliding_window_crop(aug_seq)
            
            augmented_sequences.append((aug_seq, label))
        
        return augmented_sequences

def augment_dataset(input_file, output_file, augmentation_factor=3):
    """Augment the entire dataset."""
    print(f"🔄 Augmenting dataset: {input_file}")
    
    augmenter = RNADataAugmentation()
    
    # Load original data
    sequences = []
    labels = []
    
    with open(input_file, 'r') as f:
        for line in f:
            if line.startswith('>'):
                label = int(line.split('label=')[1].strip())
                labels.append(label)
            else:
                sequences.append(line.strip().replace('T', 'U'))  # Convert to RNA
    
    print(f"📊 Original dataset: {len(sequences)} sequences")
    
    # Augment data
    augmented_data = []
    
    for seq, label in zip(sequences, labels):
        aug_sequences = augmenter.augment_sequence(seq, label, augmentation_factor)
        augmented_data.extend(aug_sequences)
    
    print(f"📊 Augmented dataset: {len(augmented_data)} sequences")
    
    # Save augmented data
    with open(output_file, 'w') as f:
        for i, (seq, label) in enumerate(augmented_data):
            f.write(f">seq_{i:06d} label={label}\n")
            f.write(f"{seq.replace('U', 'T')}\n")  # Convert back to DNA
    
    print(f"✅ Augmented dataset saved to: {output_file}")
    
    return len(augmented_data)

if __name__ == "__main__":
    # Augment the training data
    input_file = "data/567_hum_label.fa"
    output_file = "data/567_hum_label_augmented.fa"
    
    if os.path.exists(input_file):
        total_sequences = augment_dataset(input_file, output_file, augmentation_factor=4)
        print(f"\n🎯 Data augmentation completed!")
        print(f"   Original: 1134 sequences")
        print(f"   Augmented: {total_sequences} sequences")
        print(f"   Increase: {total_sequences/1134:.1f}x")
    else:
        print(f"❌ Input file not found: {input_file}")
