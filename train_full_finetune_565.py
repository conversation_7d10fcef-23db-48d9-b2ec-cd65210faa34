#!/usr/bin/env python3
"""
Full parameter fine-tuning script for 565_hum_label.fa lncRNA function prediction
Supports RNA-FM and SpliceBERT models with sliding window and sequence-level aggregation
"""

import os
import sys
import random
import logging
import numpy as np
import torch
import transformers
from dataclasses import dataclass, field
from typing import Optional, List
from torch.utils.data import Dataset
from transformers import Trainer, EarlyStoppingCallback, HfArgumentParser
from sklearn.metrics import accuracy_score, precision_recall_fscore_support, roc_auc_score

# Add paths
sys.path.append('.')
sys.path.append('downstream')

# Import custom modules
from sliding_window_dataset import SlidingWindowDataset, load_fasta_data
from sequence_level_model import SequenceLevelAggregationModel, SequenceLevelDataCollator

# Import models and tokenizers
try:
    from tokenizer.tokenization_opensource import OpenRnaLMTokenizer
    TOKENIZER_AVAILABLE = True
except ImportError:
    TOKENIZER_AVAILABLE = False
    print("Warning: OpenRnaLMTokenizer not available")

try:
    from model.rnafm.modeling_rnafm import RnaFmForSequenceClassification
    RNAFM_AVAILABLE = True
except ImportError:
    RNAFM_AVAILABLE = False
    print("Warning: RnaFm model not available")

try:
    from model.splicebert.modeling_splicebert import SpliceBertForSequenceClassification
    SPLICEBERT_AVAILABLE = True
except ImportError:
    SPLICEBERT_AVAILABLE = False
    print("Warning: SpliceBert model not available")

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class ModelArguments:
    """Arguments pertaining to which model/config/tokenizer we are going to fine-tune from."""
    model_name_or_path: str = field(
        metadata={"help": "Path to pretrained model or model identifier from huggingface.co/models"}
    )
    cache_dir: Optional[str] = field(
        default=None,
        metadata={"help": "Where do you want to store the pretrained models downloaded from huggingface.co"},
    )

@dataclass
class DataArguments:
    """Arguments pertaining to what data we are going to input our model for training and eval."""
    data_path: str = field(
        metadata={"help": "Path to the data directory"}
    )
    data_train_path: str = field(
        metadata={"help": "Path to the training data file"}
    )
    data_val_path: str = field(
        metadata={"help": "Path to the validation data file"}
    )
    data_test_path: Optional[str] = field(
        default=None,
        metadata={"help": "Path to the test data file"}
    )

@dataclass
class FullFinetuneTrainingArguments(transformers.TrainingArguments):
    """Custom training arguments for full fine-tuning."""
    
    # Model configuration
    model_type: str = field(default="rna-fm", metadata={"help": "Model type: rna-fm or splicebert-ms1024"})
    token_type: str = field(default="single", metadata={"help": "Token type for tokenization"})
    
    # Sequence processing
    model_max_length: int = field(default=1024, metadata={"help": "Maximum sequence length for tokenization"})
    window_size: int = field(default=1024, metadata={"help": "Size of sliding window"})
    window_stride: int = field(default=512, metadata={"help": "Stride of sliding window"})
    max_sequence_length: int = field(default=9216, metadata={"help": "Maximum sequence length before truncation"})
    
    # Aggregation strategy
    pooling_strategy: str = field(default="mean", metadata={"help": "Pooling strategy: mean, max, attention"})
    
    # Training configuration
    train_from_scratch: bool = field(default=False, metadata={"help": "Whether to train from scratch"})
    save_model: bool = field(default=True, metadata={"help": "Whether to save the model"})
    
    # Early stopping
    patience: int = field(default=10, metadata={"help": "Early stopping patience"})
    
    # Other settings
    seed: int = field(default=42, metadata={"help": "Random seed"})
    attn_implementation: str = field(default="eager", metadata={"help": "Attention implementation"})

def set_seed(seed: int):
    """Set random seeds for reproducibility."""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)

def compute_metrics(eval_pred):
    """Compute metrics for evaluation."""
    predictions, labels = eval_pred
    predictions = np.argmax(predictions, axis=1)

    # Basic metrics
    accuracy = accuracy_score(labels, predictions)

    # Handle the case where all predictions are the same class
    try:
        precision, recall, f1, _ = precision_recall_fscore_support(
            labels, predictions, average='binary', zero_division=0
        )
    except:
        precision, recall, f1 = 0.0, 0.0, 0.0

    # ROC AUC (using prediction probabilities)
    try:
        # Get probabilities for positive class
        probs = torch.softmax(torch.tensor(eval_pred[0]), dim=1)[:, 1].numpy()
        # Check for valid probabilities
        if not np.isnan(probs).any() and not np.isinf(probs).any():
            auc = roc_auc_score(labels, probs)
        else:
            auc = 0.0
    except:
        auc = 0.0

    # Add debugging info for early training stages
    unique_preds = len(np.unique(predictions))
    unique_labels = len(np.unique(labels))

    return {
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1': f1,
        'auc': auc,
        'unique_predictions': unique_preds,
        'unique_labels': unique_labels
    }

def load_tokenizer(model_type: str, model_path: str):
    """Load appropriate tokenizer for the model."""
    if not TOKENIZER_AVAILABLE:
        raise ImportError("OpenRnaLMTokenizer not available")

    # Use the correct tokenizer path based on model type
    if model_type == 'rna-fm':
        tokenizer_path = './checkpoint/opensource/rna-fm/'
    elif model_type == 'splicebert-ms1024':
        tokenizer_path = './checkpoint/opensource/splicebert-ms1024/'
    else:
        # Fallback to model path
        tokenizer_path = model_path

    tokenizer = OpenRnaLMTokenizer.from_pretrained(
        tokenizer_path,
        model_max_length=1024,
        padding_side="right",
        use_fast=True,
        trust_remote_code=True,
    )
    return tokenizer

def load_model(model_type: str, model_path: str, num_labels: int = 2):
    """Load the appropriate model."""
    if model_type == 'rna-fm':
        if not RNAFM_AVAILABLE:
            raise ImportError("RnaFm model not available")
        model = RnaFmForSequenceClassification.from_pretrained(
            model_path,
            num_labels=num_labels,
            problem_type="single_label_classification",
            trust_remote_code=True,
        )
    elif model_type == 'splicebert-ms1024':
        if not SPLICEBERT_AVAILABLE:
            raise ImportError("SpliceBert model not available")
        model = SpliceBertForSequenceClassification.from_pretrained(
            model_path,
            num_labels=num_labels,
            problem_type="single_label_classification",
            trust_remote_code=True,
        )
    else:
        raise ValueError(f"Unsupported model type: {model_type}")
    
    return model

def main():
    """Main training function."""
    # Parse arguments
    parser = HfArgumentParser((ModelArguments, DataArguments, FullFinetuneTrainingArguments))
    model_args, data_args, training_args = parser.parse_args_into_dataclasses()
    
    # Set seed
    set_seed(training_args.seed)
    
    # Setup logging
    logging.basicConfig(
        format="%(asctime)s - %(levelname)s - %(name)s - %(message)s",
        datefmt="%m/%d/%Y %H:%M:%S",
        level=logging.INFO,
    )
    
    logger.info(f"🧬 Starting full fine-tuning for {training_args.model_type}")
    logger.info(f"Model path: {model_args.model_name_or_path}")
    logger.info(f"Output directory: {training_args.output_dir}")
    
    # Load tokenizer
    logger.info("Loading tokenizer...")
    tokenizer = load_tokenizer(training_args.model_type, model_args.model_name_or_path)
    
    # Load datasets
    logger.info("Loading datasets...")
    train_sequences, train_labels = load_fasta_data(
        os.path.join(data_args.data_path, data_args.data_train_path)
    )
    val_sequences, val_labels = load_fasta_data(
        os.path.join(data_args.data_path, data_args.data_val_path)
    )
    
    logger.info(f"Training set: {len(train_sequences)} sequences")
    logger.info(f"Validation set: {len(val_sequences)} sequences")
    
    # Create sliding window datasets
    train_dataset = SlidingWindowDataset(
        sequences=train_sequences,
        labels=train_labels,
        tokenizer=tokenizer,
        window_size=training_args.window_size,
        window_stride=training_args.window_stride,
        max_length=training_args.max_sequence_length,
        return_windows=False  # Return sequence-level aggregated data
    )
    
    val_dataset = SlidingWindowDataset(
        sequences=val_sequences,
        labels=val_labels,
        tokenizer=tokenizer,
        window_size=training_args.window_size,
        window_stride=training_args.window_stride,
        max_length=training_args.max_sequence_length,
        return_windows=False
    )
    
    # Print dataset statistics
    logger.info("Training dataset statistics:")
    train_stats = train_dataset.get_dataset_stats()
    for key, value in train_stats.items():
        logger.info(f"  {key}: {value}")
    
    # Load base model
    logger.info(f"Loading {training_args.model_type} model...")
    base_model = load_model(
        training_args.model_type,
        model_args.model_name_or_path,
        num_labels=2
    )
    
    # Count trainable parameters
    total_params = sum(p.numel() for p in base_model.parameters())
    trainable_params = sum(p.numel() for p in base_model.parameters() if p.requires_grad)
    logger.info(f"Model parameters: {total_params:,} total, {trainable_params:,} trainable ({trainable_params/total_params*100:.2f}%)")
    
    # Wrap with sequence-level aggregation model
    model = SequenceLevelAggregationModel(
        base_model=base_model,
        pooling_strategy=training_args.pooling_strategy,
        num_labels=2,
        dropout_rate=0.1
    )
    
    # Data collator
    data_collator = SequenceLevelDataCollator(tokenizer, max_windows=16)
    
    # Early stopping callback
    early_stopping = EarlyStoppingCallback(
        early_stopping_patience=training_args.patience,
        early_stopping_threshold=0.001
    )
    
    # Initialize trainer
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=train_dataset,
        eval_dataset=val_dataset,
        tokenizer=tokenizer,
        data_collator=data_collator,
        compute_metrics=compute_metrics,
        callbacks=[early_stopping],
    )
    
    # Training
    logger.info("Starting training...")
    train_result = trainer.train()
    
    # Save model
    if training_args.save_model:
        logger.info(f"Saving model to {training_args.output_dir}")
        trainer.save_model()
        tokenizer.save_pretrained(training_args.output_dir)
    
    # Evaluation
    logger.info("Running final evaluation...")
    eval_result = trainer.evaluate()
    
    # Print results
    logger.info("Training completed!")
    logger.info(f"Training loss: {train_result.training_loss:.4f}")
    logger.info("Evaluation results:")
    for key, value in eval_result.items():
        logger.info(f"  {key}: {value:.4f}")
    
    # Save training summary
    summary_path = os.path.join(training_args.output_dir, "training_summary.txt")
    with open(summary_path, 'w') as f:
        f.write(f"Full Fine-tuning Results - {training_args.model_type}\n")
        f.write("=" * 50 + "\n\n")
        f.write(f"Model: {model_args.model_name_or_path}\n")
        f.write(f"Training data: {data_args.data_train_path}\n")
        f.write(f"Validation data: {data_args.data_val_path}\n")
        f.write(f"Training sequences: {len(train_sequences)}\n")
        f.write(f"Validation sequences: {len(val_sequences)}\n")
        f.write(f"Window size: {training_args.window_size}\n")
        f.write(f"Window stride: {training_args.window_stride}\n")
        f.write(f"Pooling strategy: {training_args.pooling_strategy}\n")
        f.write(f"Training loss: {train_result.training_loss:.4f}\n")
        f.write("\nEvaluation metrics:\n")
        for key, value in eval_result.items():
            f.write(f"  {key}: {value:.4f}\n")
    
    logger.info(f"Training summary saved to: {summary_path}")

def create_run_script():
    """Create a shell script to run training with different configurations."""
    script_content = '''#!/bin/bash

# Full Fine-tuning Script for 565_hum_label.fa lncRNA Function Prediction
# Supports RNA-FM and SpliceBERT models

set -e

echo "🧬 lncRNA Function Prediction - Full Fine-tuning"
echo "================================================"

# Configuration
DATA_DIR="data/cv_splits_565"
OUTPUT_BASE_DIR="outputs/full_finetune_565"
MODELS=("rna-fm" "splicebert-ms1024")

# Model paths (update these according to your setup)
declare -A MODEL_PATHS
MODEL_PATHS["rna-fm"]="./model/rnafm"
MODEL_PATHS["splicebert-ms1024"]="./model/splicebert"

# Training parameters
BATCH_SIZE=4
LEARNING_RATE=2e-5
NUM_EPOCHS=20
PATIENCE=10
WINDOW_SIZE=1024
WINDOW_STRIDE=512
MAX_SEQ_LENGTH=9216

# Create output directory
mkdir -p "$OUTPUT_BASE_DIR"

# Function to train a single fold
train_fold() {
    local model_type=$1
    local fold=$2
    local model_path=${MODEL_PATHS[$model_type]}
    local output_dir="$OUTPUT_BASE_DIR/${model_type}/fold_${fold}"

    echo "Training $model_type on fold $fold..."

    python train_full_finetune_565.py \\
        --model_name_or_path "$model_path" \\
        --data_path "$DATA_DIR" \\
        --data_train_path "fold_${fold}_train.fa" \\
        --data_val_path "fold_${fold}_val.fa" \\
        --output_dir "$output_dir" \\
        --model_type "$model_type" \\
        --model_max_length $WINDOW_SIZE \\
        --window_size $WINDOW_SIZE \\
        --window_stride $WINDOW_STRIDE \\
        --max_sequence_length $MAX_SEQ_LENGTH \\
        --pooling_strategy "mean" \\
        --per_device_train_batch_size $BATCH_SIZE \\
        --per_device_eval_batch_size $BATCH_SIZE \\
        --gradient_accumulation_steps 4 \\
        --learning_rate $LEARNING_RATE \\
        --num_train_epochs $NUM_EPOCHS \\
        --patience $PATIENCE \\
        --warmup_steps 100 \\
        --logging_steps 50 \\
        --eval_steps 100 \\
        --save_steps 500 \\
        --evaluation_strategy steps \\
        --save_strategy steps \\
        --load_best_model_at_end True \\
        --metric_for_best_model eval_f1 \\
        --greater_is_better True \\
        --fp16 \\
        --dataloader_num_workers 4 \\
        --seed 42 \\
        --overwrite_output_dir
}

# Train all models on all folds
for model_type in "${MODELS[@]}"; do
    echo "Starting training for $model_type"

    for fold in {0..4}; do
        train_fold "$model_type" "$fold"

        if [ $? -ne 0 ]; then
            echo "❌ Error: Training failed for $model_type fold $fold"
            exit 1
        fi
    done

    echo "✅ Completed training for $model_type"
done

echo "🎉 All training completed!"
echo "Results saved in: $OUTPUT_BASE_DIR"
'''

    with open('run_full_finetune_565.sh', 'w') as f:
        f.write(script_content)

    # Make executable
    import stat
    os.chmod('run_full_finetune_565.sh', stat.S_IRWXU | stat.S_IRGRP | stat.S_IROTH)

    print("Created run_full_finetune_565.sh script")

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "--create-script":
        create_run_script()
    else:
        main()
