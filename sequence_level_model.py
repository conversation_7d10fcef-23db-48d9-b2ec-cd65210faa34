#!/usr/bin/env python3
"""
Sequence-level aggregation model for lncRNA function prediction
Implements: multi-window encoding → average pooling → binary classification head
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from transformers.modeling_outputs import SequenceClassifierOutput
from typing import Optional, Tuple, Union

class SequenceLevelAggregationModel(nn.Module):
    """
    Wrapper model that handles sequence-level aggregation for sliding window inputs.
    
    Architecture:
    1. Encode each window with base model
    2. Average pool window representations
    3. Apply classification head
    """
    
    def __init__(
        self,
        base_model,
        pooling_strategy: str = "mean",
        hidden_size: Optional[int] = None,
        num_labels: int = 2,
        dropout_rate: float = 0.1
    ):
        """
        Initialize sequence-level aggregation model.
        
        Args:
            base_model: Pre-trained transformer model (RNA-FM or SpliceBERT)
            pooling_strategy: Pooling strategy ("mean", "max", "attention")
            hidden_size: Hidden size of the model (auto-detected if None)
            num_labels: Number of classification labels (default: 2)
            dropout_rate: Dropout rate for classification head
        """
        super().__init__()
        
        self.base_model = base_model
        self.pooling_strategy = pooling_strategy
        self.num_labels = num_labels
        
        # Auto-detect hidden size
        if hidden_size is None:
            if hasattr(base_model, 'config'):
                hidden_size = base_model.config.hidden_size
            elif hasattr(base_model, 'hidden_size'):
                hidden_size = base_model.hidden_size
            else:
                # Try to infer from model parameters
                for param in base_model.parameters():
                    if len(param.shape) >= 2:
                        hidden_size = param.shape[-1]
                        break
                if hidden_size is None:
                    hidden_size = 768  # Default
        
        self.hidden_size = hidden_size
        
        # Attention pooling layer (if needed)
        if pooling_strategy == "attention":
            self.attention_pooling = AttentionPooling(hidden_size)
        
        # Classification head
        self.classifier = nn.Sequential(
            nn.Dropout(dropout_rate),
            nn.Linear(hidden_size, hidden_size // 2),
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            nn.Linear(hidden_size // 2, num_labels)
        )
        
        # Initialize classification head
        self._init_classification_head()
    
    def _init_classification_head(self):
        """Initialize classification head weights."""
        for module in self.classifier:
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                nn.init.zeros_(module.bias)

    def gradient_checkpointing_enable(self, gradient_checkpointing_kwargs=None):
        """Enable gradient checkpointing for the base model."""
        if hasattr(self.base_model, 'gradient_checkpointing_enable'):
            self.base_model.gradient_checkpointing_enable(gradient_checkpointing_kwargs)

    def gradient_checkpointing_disable(self):
        """Disable gradient checkpointing for the base model."""
        if hasattr(self.base_model, 'gradient_checkpointing_disable'):
            self.base_model.gradient_checkpointing_disable()

    def forward(
        self,
        input_ids: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None,
        labels: Optional[torch.Tensor] = None,
        num_windows: Optional[torch.Tensor] = None,
        **kwargs
    ) -> SequenceClassifierOutput:
        """
        Forward pass with sequence-level aggregation.
        
        Args:
            input_ids: Input token IDs
                - Shape: (batch_size, max_windows, seq_len) for multi-window
                - Shape: (batch_size, seq_len) for single window
            attention_mask: Attention mask (same shape as input_ids)
            labels: Classification labels (batch_size,)
            num_windows: Number of windows per sequence (batch_size,)
        
        Returns:
            SequenceClassifierOutput with loss and logits
        """
        batch_size = input_ids.shape[0]
        
        # Handle different input shapes
        if len(input_ids.shape) == 3:
            # Multi-window case: (batch_size, num_windows, seq_len)
            batch_size, max_windows, seq_len = input_ids.shape
            
            # Reshape for batch processing
            input_ids_flat = input_ids.view(-1, seq_len)  # (batch_size * max_windows, seq_len)
            if attention_mask is not None:
                attention_mask_flat = attention_mask.view(-1, seq_len)
            else:
                attention_mask_flat = None
            
            # Encode all windows
            outputs = self.base_model(
                input_ids=input_ids_flat,
                attention_mask=attention_mask_flat,
                output_hidden_states=True,
                return_dict=True
            )

            # Get hidden states (last layer)
            if hasattr(outputs, 'last_hidden_state') and outputs.last_hidden_state is not None:
                hidden_states = outputs.last_hidden_state  # (batch_size * max_windows, seq_len, hidden_size)
            elif hasattr(outputs, 'hidden_states') and outputs.hidden_states is not None:
                hidden_states = outputs.hidden_states[-1]
            else:
                # Fallback: try to get hidden states from the base model directly
                # This handles cases where the sequence classification model doesn't return hidden states
                base_outputs = None

                # Try different attribute names for the base model
                if hasattr(self.base_model, 'splicebert'):
                    base_outputs = self.base_model.splicebert(
                        input_ids=input_ids_flat,
                        attention_mask=attention_mask_flat,
                        output_hidden_states=True,
                        return_dict=True
                    )
                elif hasattr(self.base_model, 'rnafm'):
                    base_outputs = self.base_model.rnafm(
                        input_ids=input_ids_flat,
                        attention_mask=attention_mask_flat,
                        output_hidden_states=True,
                        return_dict=True
                    )
                elif hasattr(self.base_model, 'base_model'):
                    base_outputs = self.base_model.base_model(
                        input_ids=input_ids_flat,
                        attention_mask=attention_mask_flat,
                        output_hidden_states=True,
                        return_dict=True
                    )

                if base_outputs is not None and hasattr(base_outputs, 'last_hidden_state'):
                    hidden_states = base_outputs.last_hidden_state
                elif base_outputs is not None and hasattr(base_outputs, 'hidden_states') and base_outputs.hidden_states is not None:
                    hidden_states = base_outputs.hidden_states[-1]
                else:
                    raise ValueError("Cannot extract hidden states from base model output. "
                                   "The model may not support returning hidden states.")
            
            # Pool over sequence length (get window representations)
            if attention_mask_flat is not None:
                # Masked average pooling
                mask_expanded = attention_mask_flat.unsqueeze(-1).expand_as(hidden_states)
                hidden_states_masked = hidden_states * mask_expanded
                window_representations = hidden_states_masked.sum(dim=1) / mask_expanded.sum(dim=1)
            else:
                # Simple average pooling
                window_representations = hidden_states.mean(dim=1)  # (batch_size * max_windows, hidden_size)
            
            # Reshape back to (batch_size, max_windows, hidden_size)
            window_representations = window_representations.view(batch_size, max_windows, self.hidden_size)
            
            # Aggregate windows for each sequence
            sequence_representations = self._aggregate_windows(
                window_representations, num_windows, max_windows
            )
            
        else:
            # Single window case: (batch_size, seq_len)
            outputs = self.base_model(
                input_ids=input_ids,
                attention_mask=attention_mask,
                output_hidden_states=True,
                return_dict=True
            )

            # Get hidden states
            if hasattr(outputs, 'last_hidden_state') and outputs.last_hidden_state is not None:
                hidden_states = outputs.last_hidden_state
            elif hasattr(outputs, 'hidden_states') and outputs.hidden_states is not None:
                hidden_states = outputs.hidden_states[-1]
            else:
                # Fallback: try to get hidden states from the base model directly
                # This handles cases where the sequence classification model doesn't return hidden states
                base_outputs = None

                # Try different attribute names for the base model
                if hasattr(self.base_model, 'splicebert'):
                    base_outputs = self.base_model.splicebert(
                        input_ids=input_ids,
                        attention_mask=attention_mask,
                        output_hidden_states=True,
                        return_dict=True
                    )
                elif hasattr(self.base_model, 'rnafm'):
                    base_outputs = self.base_model.rnafm(
                        input_ids=input_ids,
                        attention_mask=attention_mask,
                        output_hidden_states=True,
                        return_dict=True
                    )
                elif hasattr(self.base_model, 'base_model'):
                    base_outputs = self.base_model.base_model(
                        input_ids=input_ids,
                        attention_mask=attention_mask,
                        output_hidden_states=True,
                        return_dict=True
                    )

                if base_outputs is not None and hasattr(base_outputs, 'last_hidden_state'):
                    hidden_states = base_outputs.last_hidden_state
                elif base_outputs is not None and hasattr(base_outputs, 'hidden_states') and base_outputs.hidden_states is not None:
                    hidden_states = base_outputs.hidden_states[-1]
                else:
                    raise ValueError("Cannot extract hidden states from base model output. "
                                   "The model may not support returning hidden states.")
            
            # Pool over sequence length
            if attention_mask is not None:
                mask_expanded = attention_mask.unsqueeze(-1).expand_as(hidden_states)
                hidden_states_masked = hidden_states * mask_expanded
                sequence_representations = hidden_states_masked.sum(dim=1) / mask_expanded.sum(dim=1)
            else:
                sequence_representations = hidden_states.mean(dim=1)
        
        # Apply classification head
        logits = self.classifier(sequence_representations)
        
        # Calculate loss if labels provided
        loss = None
        if labels is not None:
            loss_fct = nn.CrossEntropyLoss()
            loss = loss_fct(logits.view(-1, self.num_labels), labels.view(-1))
        
        return SequenceClassifierOutput(
            loss=loss,
            logits=logits,
            hidden_states=None,
            attentions=None,
        )
    
    def _aggregate_windows(
        self,
        window_representations: torch.Tensor,
        num_windows: Optional[torch.Tensor],
        max_windows: int
    ) -> torch.Tensor:
        """
        Aggregate window representations into sequence representations.
        
        Args:
            window_representations: (batch_size, max_windows, hidden_size)
            num_windows: (batch_size,) actual number of windows per sequence
            max_windows: Maximum number of windows
        
        Returns:
            sequence_representations: (batch_size, hidden_size)
        """
        batch_size = window_representations.shape[0]
        
        if self.pooling_strategy == "mean":
            if num_windows is not None:
                # Masked average pooling
                sequence_representations = []
                for i in range(batch_size):
                    n_windows = num_windows[i].item()
                    if n_windows > 0:
                        seq_repr = window_representations[i, :n_windows].mean(dim=0)
                    else:
                        seq_repr = window_representations[i, 0]  # Fallback
                    sequence_representations.append(seq_repr)
                sequence_representations = torch.stack(sequence_representations)
            else:
                # Simple average pooling
                sequence_representations = window_representations.mean(dim=1)
                
        elif self.pooling_strategy == "max":
            sequence_representations = window_representations.max(dim=1)[0]
            
        elif self.pooling_strategy == "attention":
            sequence_representations = self.attention_pooling(window_representations, num_windows)
            
        else:
            raise ValueError(f"Unknown pooling strategy: {self.pooling_strategy}")
        
        return sequence_representations

class AttentionPooling(nn.Module):
    """Attention-based pooling for window aggregation."""
    
    def __init__(self, hidden_size: int):
        super().__init__()
        self.attention = nn.Linear(hidden_size, 1)
        
    def forward(
        self,
        window_representations: torch.Tensor,
        num_windows: Optional[torch.Tensor] = None
    ) -> torch.Tensor:
        """
        Apply attention pooling.
        
        Args:
            window_representations: (batch_size, max_windows, hidden_size)
            num_windows: (batch_size,) actual number of windows per sequence
        
        Returns:
            pooled_representations: (batch_size, hidden_size)
        """
        batch_size, max_windows, hidden_size = window_representations.shape
        
        # Compute attention scores
        attention_scores = self.attention(window_representations).squeeze(-1)  # (batch_size, max_windows)
        
        # Apply mask if num_windows provided
        if num_windows is not None:
            mask = torch.arange(max_windows, device=window_representations.device).unsqueeze(0) < num_windows.unsqueeze(1)
            attention_scores = attention_scores.masked_fill(~mask, float('-inf'))
        
        # Apply softmax
        attention_weights = F.softmax(attention_scores, dim=1)  # (batch_size, max_windows)
        
        # Weighted sum
        pooled_representations = torch.sum(
            attention_weights.unsqueeze(-1) * window_representations, dim=1
        )  # (batch_size, hidden_size)
        
        return pooled_representations

# Custom data collator for sequence-level training
class SequenceLevelDataCollator:
    """Data collator that handles variable number of windows per sequence."""

    def __init__(self, tokenizer, max_windows: int = 16):
        self.tokenizer = tokenizer
        self.max_windows = max_windows
    
    def __call__(self, features):
        """Collate features into batch."""
        batch = {}
        
        # Handle different cases
        if 'num_windows' in features[0]:
            # Multi-window case
            batch_input_ids = []
            batch_attention_mask = []
            batch_labels = []
            batch_num_windows = []
            
            for feature in features:
                input_ids = feature['input_ids']
                attention_mask = feature['attention_mask']
                
                if len(input_ids.shape) == 1:
                    # Single window
                    input_ids = input_ids.unsqueeze(0)
                    attention_mask = attention_mask.unsqueeze(0)
                
                num_windows = input_ids.shape[0]
                
                # Pad or truncate to max_windows
                if num_windows > self.max_windows:
                    input_ids = input_ids[:self.max_windows]
                    attention_mask = attention_mask[:self.max_windows]
                    num_windows = self.max_windows
                elif num_windows < self.max_windows:
                    # Pad with zeros
                    pad_size = self.max_windows - num_windows
                    seq_len = input_ids.shape[1]
                    
                    input_ids = torch.cat([
                        input_ids,
                        torch.zeros(pad_size, seq_len, dtype=input_ids.dtype)
                    ], dim=0)
                    attention_mask = torch.cat([
                        attention_mask,
                        torch.zeros(pad_size, seq_len, dtype=attention_mask.dtype)
                    ], dim=0)
                
                batch_input_ids.append(input_ids)
                batch_attention_mask.append(attention_mask)
                batch_labels.append(feature['labels'])
                batch_num_windows.append(torch.tensor(num_windows))
            
            batch['input_ids'] = torch.stack(batch_input_ids)
            batch['attention_mask'] = torch.stack(batch_attention_mask)
            batch['labels'] = torch.stack(batch_labels)
            batch['num_windows'] = torch.stack(batch_num_windows)
            
        else:
            # Single window case - use default collator behavior
            batch['input_ids'] = torch.stack([f['input_ids'] for f in features])
            batch['attention_mask'] = torch.stack([f['attention_mask'] for f in features])
            batch['labels'] = torch.stack([f['labels'] for f in features])
        
        return batch

# Example usage
if __name__ == "__main__":
    print("🧪 Testing Sequence-Level Aggregation Model")
    print("=" * 50)
    
    # Create dummy base model for testing
    class DummyBaseModel(nn.Module):
        def __init__(self, hidden_size=768, vocab_size=1000):
            super().__init__()
            self.embeddings = nn.Embedding(vocab_size, hidden_size)
            self.encoder = nn.TransformerEncoder(
                nn.TransformerEncoderLayer(hidden_size, nhead=8, batch_first=True),
                num_layers=2
            )
            self.config = type('Config', (), {'hidden_size': hidden_size})()
        
        def forward(self, input_ids, attention_mask=None, **kwargs):
            embeddings = self.embeddings(input_ids)
            hidden_states = self.encoder(embeddings)
            return type('Output', (), {
                'last_hidden_state': hidden_states,
                'hidden_states': [hidden_states]
            })()
    
    # Test model
    base_model = DummyBaseModel()
    model = SequenceLevelAggregationModel(base_model, pooling_strategy="mean")
    
    # Test single window
    print("Testing single window input...")
    input_ids = torch.randint(0, 1000, (2, 100))  # batch_size=2, seq_len=100
    attention_mask = torch.ones_like(input_ids)
    labels = torch.tensor([0, 1])
    
    output = model(input_ids, attention_mask, labels)
    print(f"Single window - Loss: {output.loss:.4f}, Logits shape: {output.logits.shape}")
    
    # Test multi-window
    print("\nTesting multi-window input...")
    input_ids = torch.randint(0, 1000, (2, 3, 100))  # batch_size=2, num_windows=3, seq_len=100
    attention_mask = torch.ones_like(input_ids)
    num_windows = torch.tensor([3, 2])  # Different number of windows per sequence
    
    output = model(input_ids, attention_mask, labels, num_windows)
    print(f"Multi-window - Loss: {output.loss:.4f}, Logits shape: {output.logits.shape}")
    
    print("\n✅ Sequence-level aggregation model test completed!")
