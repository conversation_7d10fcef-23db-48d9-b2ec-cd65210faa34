#!/usr/bin/env python3
"""
Analyze 565_hum_label.fa dataset for lncRNA function prediction
"""

import os
import numpy as np
import matplotlib.pyplot as plt
from collections import Counter
import seaborn as sns

def load_fasta_with_labels(fasta_path):
    """Load FASTA file with labels in header."""
    sequences = []
    labels = []
    sequence_ids = []

    with open(fasta_path, 'r') as f:
        lines = f.readlines()

    i = 0
    seq_id = 0
    while i < len(lines):
        line = lines[i].strip()
        if line.startswith('label='):
            # Extract label
            label = int(line.split('=')[1])
            labels.append(label)

            # Get sequence (next line)
            if i + 1 < len(lines):
                sequence = lines[i + 1].strip()
                sequences.append(sequence)
                sequence_ids.append(f"seq_{seq_id}")
                seq_id += 1
            i += 2
        else:
            i += 1

    return sequences, labels, sequence_ids

def analyze_dataset(sequences, labels):
    """Analyze dataset statistics."""
    print("🧬 Dataset Analysis for 565_hum_label.fa")
    print("=" * 60)
    
    # Basic statistics
    total_sequences = len(sequences)
    label_counts = Counter(labels)
    
    print(f"📊 Basic Statistics:")
    print(f"   Total sequences: {total_sequences:,}")
    print(f"   Label distribution:")
    for label, count in sorted(label_counts.items()):
        percentage = count / total_sequences * 100
        print(f"     Label {label}: {count:,} ({percentage:.1f}%)")
    
    # Sequence length analysis
    seq_lengths = [len(seq) for seq in sequences]
    
    print(f"\n📏 Sequence Length Statistics:")
    print(f"   Min length: {min(seq_lengths):,} bp")
    print(f"   Max length: {max(seq_lengths):,} bp")
    print(f"   Mean length: {np.mean(seq_lengths):.1f} bp")
    print(f"   Median length: {np.median(seq_lengths):.1f} bp")
    print(f"   Std deviation: {np.std(seq_lengths):.1f} bp")
    
    # Length percentiles
    percentiles = [25, 50, 75, 90, 95, 99]
    print(f"\n📈 Length Percentiles:")
    for p in percentiles:
        value = np.percentile(seq_lengths, p)
        print(f"   {p}th percentile: {value:.0f} bp")
    
    # Analyze sequences > 1024 (need sliding window)
    long_sequences = [length for length in seq_lengths if length > 1024]
    very_long_sequences = [length for length in seq_lengths if length > 9216]
    
    print(f"\n🔍 Sliding Window Analysis:")
    print(f"   Sequences > 1024 bp: {len(long_sequences):,} ({len(long_sequences)/total_sequences*100:.1f}%)")
    print(f"   Sequences > 9216 bp: {len(very_long_sequences):,} ({len(very_long_sequences)/total_sequences*100:.1f}%)")
    
    if long_sequences:
        print(f"   Mean length of long sequences: {np.mean(long_sequences):.1f} bp")
        print(f"   Max windows needed (1024/512): {max(long_sequences)//512 + 1}")
    
    # Nucleotide composition
    print(f"\n🧬 Nucleotide Composition Analysis:")
    all_nucleotides = ''.join(sequences)
    nucleotide_counts = Counter(all_nucleotides.upper())
    total_nucleotides = sum(nucleotide_counts.values())
    
    for nucleotide in ['A', 'T', 'G', 'C']:
        count = nucleotide_counts.get(nucleotide, 0)
        percentage = count / total_nucleotides * 100
        print(f"   {nucleotide}: {count:,} ({percentage:.2f}%)")
    
    # Check for non-standard nucleotides
    standard_nucleotides = set(['A', 'T', 'G', 'C'])
    non_standard = set(nucleotide_counts.keys()) - standard_nucleotides
    if non_standard:
        print(f"   Non-standard nucleotides: {non_standard}")
    
    return {
        'total_sequences': total_sequences,
        'label_counts': label_counts,
        'seq_lengths': seq_lengths,
        'nucleotide_counts': nucleotide_counts
    }

def create_visualizations(stats, output_dir='analysis_results'):
    """Create visualization plots."""
    os.makedirs(output_dir, exist_ok=True)
    
    # Set style
    plt.style.use('default')
    sns.set_palette("husl")
    
    # 1. Label distribution pie chart
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
    
    labels = list(stats['label_counts'].keys())
    counts = list(stats['label_counts'].values())
    colors = ['#ff9999', '#66b3ff']
    
    ax1.pie(counts, labels=[f'Label {l}' for l in labels], autopct='%1.1f%%', 
            colors=colors, startangle=90)
    ax1.set_title('Label Distribution')
    
    # 2. Sequence length distribution
    seq_lengths = stats['seq_lengths']
    ax2.hist(seq_lengths, bins=50, alpha=0.7, color='skyblue', edgecolor='black')
    ax2.axvline(1024, color='red', linestyle='--', label='Window size (1024)')
    ax2.axvline(9216, color='orange', linestyle='--', label='Truncation (9216)')
    ax2.set_xlabel('Sequence Length (bp)')
    ax2.set_ylabel('Frequency')
    ax2.set_title('Sequence Length Distribution')
    ax2.legend()
    ax2.set_yscale('log')
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'dataset_overview.png'), dpi=300, bbox_inches='tight')
    plt.close()
    
    # 3. Detailed length analysis
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
    
    # Box plot for all sequences
    ax1.boxplot([seq_lengths], labels=['All sequences'])
    ax1.set_ylabel('Sequence Length (bp)')
    ax1.set_title('Sequence Length Distribution')
    ax1.set_yscale('log')
    
    # Cumulative distribution
    sorted_lengths = np.sort(seq_lengths)
    cumulative = np.arange(1, len(sorted_lengths) + 1) / len(sorted_lengths)
    ax2.plot(sorted_lengths, cumulative, linewidth=2)
    ax2.axvline(1024, color='red', linestyle='--', label='Window size (1024)')
    ax2.axvline(9216, color='orange', linestyle='--', label='Truncation (9216)')
    ax2.set_xlabel('Sequence Length (bp)')
    ax2.set_ylabel('Cumulative Probability')
    ax2.set_title('Cumulative Length Distribution')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'length_analysis.png'), dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"📊 Visualizations saved to: {output_dir}/")

def main():
    """Main analysis function."""
    fasta_path = 'data/565_hum_label.fa'
    
    if not os.path.exists(fasta_path):
        print(f"❌ Error: File not found: {fasta_path}")
        return
    
    # Load data
    print("Loading dataset...")
    sequences, labels, sequence_ids = load_fasta_with_labels(fasta_path)
    
    # Analyze
    stats = analyze_dataset(sequences, labels)
    
    # Create visualizations
    print("\nCreating visualizations...")
    create_visualizations(stats)
    
    # Save summary
    output_dir = 'analysis_results'
    os.makedirs(output_dir, exist_ok=True)
    
    with open(os.path.join(output_dir, 'dataset_summary.txt'), 'w') as f:
        f.write("565_hum_label.fa Dataset Summary\n")
        f.write("=" * 40 + "\n\n")
        f.write(f"Total sequences: {stats['total_sequences']:,}\n")
        f.write(f"Label distribution: {dict(stats['label_counts'])}\n")
        f.write(f"Sequence length range: {min(stats['seq_lengths'])}-{max(stats['seq_lengths'])} bp\n")
        f.write(f"Mean length: {np.mean(stats['seq_lengths']):.1f} bp\n")
        f.write(f"Sequences > 1024 bp: {sum(1 for l in stats['seq_lengths'] if l > 1024)}\n")
        f.write(f"Sequences > 9216 bp: {sum(1 for l in stats['seq_lengths'] if l > 9216)}\n")
    
    print(f"\n✅ Analysis completed! Results saved to: {output_dir}/")

if __name__ == "__main__":
    main()
