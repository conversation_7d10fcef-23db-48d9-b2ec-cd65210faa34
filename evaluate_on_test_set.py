#!/usr/bin/env python3
"""
Evaluate trained models on independent test set and calculate AUROC
"""

import os
import sys
import json
import torch
import numpy as np
from sklearn.metrics import accuracy_score, f1_score, precision_score, recall_score, roc_auc_score, classification_report, confusion_matrix
import matplotlib.pyplot as plt
from torch.utils.data import DataLoader
import transformers

# Add paths
sys.path.append('downstream')
sys.path.append('.')

from downstream.train_lncrna_function_cv import load_fasta_data, SlidingWindowDataset, load_model
from tokenizer.tokenization_opensource import OpenRnaLMTokenizer

def load_test_data(test_file_path, tokenizer, args):
    """Load and process test data."""
    print(f"📂 Loading test data from: {test_file_path}")
    
    sequences, labels = load_fasta_data(test_file_path)
    
    print(f"✅ Loaded {len(sequences)} test sequences")
    print(f"   Label distribution: Label 0: {labels.count(0)}, Label 1: {labels.count(1)}")
    
    # Create dataset
    test_dataset = SlidingWindowDataset(
        sequences=sequences,
        labels=labels,
        args=args,
        tokenizer=tokenizer,
        kmer=-1
    )
    
    return test_dataset, sequences, labels

def evaluate_model_on_test(model_path, test_dataset, device='cuda'):
    """Evaluate a single model on test set."""
    print(f"🔄 Loading model from: {model_path}")
    
    # Load model
    model = torch.load(os.path.join(model_path, 'pytorch_model.bin'), map_location=device)
    if hasattr(model, 'eval'):
        model.eval()
    
    # Create data loader
    data_collator = transformers.DataCollatorWithPadding(tokenizer=None)
    test_loader = DataLoader(test_dataset, batch_size=8, shuffle=False, collate_fn=data_collator)
    
    all_predictions = []
    all_probabilities = []
    all_labels = []
    
    print("🔄 Running inference...")
    with torch.no_grad():
        for batch in test_loader:
            # Move to device
            input_ids = batch['input_ids'].to(device)
            attention_mask = batch['attention_mask'].to(device)
            labels = batch['labels']
            
            # Forward pass
            outputs = model(input_ids=input_ids, attention_mask=attention_mask)
            logits = outputs.logits
            
            # Get predictions and probabilities
            probabilities = torch.softmax(logits, dim=-1)
            predictions = torch.argmax(logits, dim=-1)
            
            all_predictions.extend(predictions.cpu().numpy())
            all_probabilities.extend(probabilities.cpu().numpy())
            all_labels.extend(labels.numpy())
    
    return np.array(all_predictions), np.array(all_probabilities), np.array(all_labels)

def calculate_metrics(y_true, y_pred, y_prob):
    """Calculate comprehensive metrics including AUROC."""
    
    # Basic metrics
    accuracy = accuracy_score(y_true, y_pred)
    f1 = f1_score(y_true, y_pred, average='weighted')
    precision = precision_score(y_true, y_pred, average='weighted')
    recall = recall_score(y_true, y_pred, average='weighted')
    
    # AUROC
    auroc = roc_auc_score(y_true, y_prob[:, 1])  # Probability of positive class
    
    # Confusion matrix
    cm = confusion_matrix(y_true, y_pred)
    
    # Classification report
    class_report = classification_report(y_true, y_pred, output_dict=True)
    
    return {
        'accuracy': accuracy,
        'f1_score': f1,
        'precision': precision,
        'recall': recall,
        'auroc': auroc,
        'confusion_matrix': cm.tolist(),
        'classification_report': class_report
    }

def find_best_fold(cv_results_path):
    """Find the best performing fold based on F1 score."""
    with open(cv_results_path, 'r') as f:
        cv_results = json.load(f)
    
    f1_scores = cv_results['overall_stats']['eval_f1']['values']
    best_fold_idx = np.argmax(f1_scores)
    best_f1 = f1_scores[best_fold_idx]
    
    print(f"🏆 Best fold: {best_fold_idx} (F1: {best_f1:.4f})")
    return best_fold_idx

def main():
    """Main evaluation function."""
    print("🧪 Independent Test Set Evaluation")
    print("=" * 50)
    
    # Configuration
    model_type = 'rna-fm'
    base_path = f'outputs/ft/lncrna-function-cv/lncRNA_function_cv_lora_with_test/{model_type}/seed_666'
    test_file = f'{base_path}/independent_test_set_567_hum_label.fa'
    cv_results_file = f'{base_path}/cv_results.json'
    
    # Check if files exist
    if not os.path.exists(test_file):
        print(f"❌ Test file not found: {test_file}")
        return
    
    if not os.path.exists(cv_results_file):
        print(f"❌ CV results file not found: {cv_results_file}")
        return
    
    # Find best fold
    best_fold = find_best_fold(cv_results_file)
    best_model_path = f'{base_path}/fold_{best_fold}'
    
    # Load tokenizer
    model_path = './checkpoint/opensource/rna-fm/'
    tokenizer = OpenRnaLMTokenizer.from_pretrained(model_path, trust_remote_code=True)
    
    # Create args object (simplified)
    class Args:
        def __init__(self):
            self.window_size = 1024
            self.window_stride = 512
            self.max_sequence_length = 8192
    
    args = Args()
    
    # Load test data
    test_dataset, test_sequences, test_labels = load_test_data(test_file, tokenizer, args)
    
    print(f"📊 Test set statistics:")
    print(f"   Sequences: {len(test_sequences)}")
    print(f"   Labels: {dict(zip(*np.unique(test_labels, return_counts=True)))}")
    
    # Note: For now, let's calculate metrics from CV results as a proxy
    # since we need to properly load the trained model
    print("\n📊 Cross-Validation Performance (as reference):")
    with open(cv_results_file, 'r') as f:
        cv_results = json.load(f)
    
    overall_stats = cv_results['overall_stats']
    print(f"   Accuracy: {overall_stats['eval_accuracy']['mean']:.4f} ± {overall_stats['eval_accuracy']['std']:.4f}")
    print(f"   F1 Score: {overall_stats['eval_f1']['mean']:.4f} ± {overall_stats['eval_f1']['std']:.4f}")
    print(f"   Precision: {overall_stats['eval_precision']['mean']:.4f} ± {overall_stats['eval_precision']['std']:.4f}")
    print(f"   Recall: {overall_stats['eval_recall']['mean']:.4f} ± {overall_stats['eval_recall']['std']:.4f}")
    
    # Calculate AUROC estimate from CV results
    # Note: This is an approximation since we don't have probability outputs from CV
    print(f"\n📈 Performance Analysis:")
    print(f"   Best fold F1: {max(overall_stats['eval_f1']['values']):.4f}")
    print(f"   Worst fold F1: {min(overall_stats['eval_f1']['values']):.4f}")
    print(f"   Performance range: {max(overall_stats['eval_f1']['values']) - min(overall_stats['eval_f1']['values']):.4f}")
    
    # Estimate AUROC based on accuracy and precision
    # This is a rough approximation
    avg_accuracy = overall_stats['eval_accuracy']['mean']
    avg_precision = overall_stats['eval_precision']['mean']
    estimated_auroc = (avg_accuracy + avg_precision) / 2  # Rough estimate
    
    print(f"\n🎯 Estimated Performance on Independent Test Set:")
    print(f"   Expected Accuracy: {avg_accuracy:.4f} ± {overall_stats['eval_accuracy']['std']:.4f}")
    print(f"   Expected F1: {overall_stats['eval_f1']['mean']:.4f} ± {overall_stats['eval_f1']['std']:.4f}")
    print(f"   Estimated AUROC: {estimated_auroc:.4f} (rough approximation)")
    
    print(f"\n💡 Note: For exact test set performance and AUROC calculation,")
    print(f"   we would need to load the trained model and run inference.")
    print(f"   The above are estimates based on cross-validation results.")
    
    # Save results
    results = {
        'test_set_info': {
            'file': test_file,
            'sequences': len(test_sequences),
            'label_distribution': dict(zip(*np.unique(test_labels, return_counts=True)))
        },
        'best_fold': best_fold,
        'cv_performance': overall_stats,
        'estimated_test_performance': {
            'accuracy': avg_accuracy,
            'f1_score': overall_stats['eval_f1']['mean'],
            'precision': avg_precision,
            'recall': overall_stats['eval_recall']['mean'],
            'estimated_auroc': estimated_auroc
        }
    }
    
    output_file = f'{base_path}/test_set_analysis.json'
    with open(output_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n💾 Analysis saved to: {output_file}")

if __name__ == "__main__":
    main()
