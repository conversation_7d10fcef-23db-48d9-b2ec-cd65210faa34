#!/usr/bin/env python3
"""
Simple lncRNA functionality prediction using trained SpliceBERT model
"""

import os
import sys
import torch
import numpy as np
import pandas as pd
from datetime import datetime
from tqdm import tqdm

# Add paths
sys.path.append('.')
sys.path.append('downstream')

# Import required modules
from tokenizer.tokenization_opensource import OpenRnaLMTokenizer
from model.splicebert.modeling_splicebert import SpliceBertForSequenceClassification
from peft import PeftModel
from train_lncrna_function_lora import SlidingWindowModel

def load_fasta_sequences(fasta_path):
    """Load sequences from FASTA file."""
    sequences = []
    sequence_ids = []
    
    print(f"Loading sequences from {fasta_path}...")
    
    with open(fasta_path, 'r') as f:
        current_id = None
        current_seq = ""
        line_count = 0
        
        for line in f:
            line_count += 1
            line = line.strip()
            
            if line.startswith('>'):
                # Save previous sequence
                if current_id is not None and current_seq:
                    sequences.append(current_seq)
                    sequence_ids.append(current_id)
                
                # Start new sequence
                current_id = line[1:]  # Remove '>'
                current_seq = ""
            else:
                current_seq += line
            
            # Progress indicator for large files
            if line_count % 100000 == 0:
                print(f"  Processed {line_count:,} lines, found {len(sequences):,} sequences...")
        
        # Don't forget the last sequence
        if current_id is not None and current_seq:
            sequences.append(current_seq)
            sequence_ids.append(current_id)
    
    print(f"Loaded {len(sequences):,} sequences")
    return sequences, sequence_ids

def load_best_splicebert_model():
    """Load the best trained SpliceBERT model (fold 1 with F1=0.8190)."""
    print("Loading best SpliceBERT model (fold 1)...")
    
    # Original checkpoint path
    checkpoint_path = './checkpoint/opensource/splicebert-ms1024/'
    
    # Load tokenizer
    tokenizer = OpenRnaLMTokenizer.from_pretrained(
        checkpoint_path,
        model_max_length=1024,
        padding_side="right",
        use_fast=True,
        trust_remote_code=True,
    )
    
    # Load base model
    base_model = SpliceBertForSequenceClassification.from_pretrained(
        checkpoint_path,
        num_labels=2,
        problem_type="single_label_classification",
        trust_remote_code=True,
    )
    
    # Load LoRA weights from best fold
    lora_path = "outputs/cv_lora/splicebert-ms1024/fold_1"
    if os.path.exists(os.path.join(lora_path, "adapter_config.json")):
        print(f"  Loading LoRA weights from {lora_path}")
        model = PeftModel.from_pretrained(base_model, lora_path)
    else:
        print(f"  Warning: No LoRA weights found, using base model")
        model = base_model
    
    # Wrap with sliding window model
    wrapped_model = SlidingWindowModel(model, pooling_strategy='mean')
    wrapped_model.eval()
    
    return wrapped_model, tokenizer

def predict_batch(model, tokenizer, sequences, device='cuda'):
    """Predict functionality for a batch of sequences."""
    # Tokenize batch
    tokenized = tokenizer(
        sequences,
        truncation=True,
        padding=True,
        max_length=1024,
        return_tensors='pt'
    )
    
    # Move to device
    input_ids = tokenized['input_ids'].to(device)
    attention_mask = tokenized['attention_mask'].to(device)
    
    with torch.no_grad():
        outputs = model(
            input_ids=input_ids,
            attention_mask=attention_mask
        )
        
        logits = outputs['logits']
        probabilities = torch.softmax(logits, dim=-1)
        predictions = torch.argmax(logits, dim=-1)
        
        # Get confidence scores
        confidence_scores = torch.max(probabilities, dim=-1)[0]
        
        return predictions.cpu().numpy(), probabilities.cpu().numpy(), confidence_scores.cpu().numpy()

def predict_all_sequences(model, tokenizer, sequences, sequence_ids, batch_size=8, device='cuda'):
    """Predict functionality for all sequences with progress tracking."""
    print(f"Predicting functionality for {len(sequences):,} sequences...")
    print(f"Using batch size: {batch_size}")
    
    all_predictions = []
    all_probabilities = []
    all_confidence_scores = []
    
    # Process in batches with progress bar
    num_batches = (len(sequences) + batch_size - 1) // batch_size
    
    for i in tqdm(range(0, len(sequences), batch_size), total=num_batches, desc="Predicting"):
        batch_sequences = sequences[i:i+batch_size]
        
        try:
            predictions, probabilities, confidence_scores = predict_batch(
                model, tokenizer, batch_sequences, device
            )
            
            all_predictions.extend(predictions)
            all_probabilities.extend(probabilities)
            all_confidence_scores.extend(confidence_scores)
            
        except Exception as e:
            print(f"\nError processing batch {i//batch_size}: {e}")
            # Fill with default values for failed batch
            batch_size_actual = len(batch_sequences)
            all_predictions.extend([0] * batch_size_actual)
            all_probabilities.extend([[0.5, 0.5]] * batch_size_actual)
            all_confidence_scores.extend([0.5] * batch_size_actual)
    
    return np.array(all_predictions), np.array(all_probabilities), np.array(all_confidence_scores)

def analyze_and_save_results(sequence_ids, predictions, probabilities, confidence_scores, output_path):
    """Analyze results and save to file."""
    print("Analyzing results...")
    
    # Basic statistics
    total_count = len(predictions)
    functional_count = np.sum(predictions == 1)
    non_functional_count = np.sum(predictions == 0)
    
    # Create results DataFrame
    results_df = pd.DataFrame({
        'sequence_id': sequence_ids,
        'prediction': predictions,
        'prediction_label': ['functional' if p == 1 else 'non_functional' for p in predictions],
        'prob_non_functional': probabilities[:, 0],
        'prob_functional': probabilities[:, 1],
        'confidence_score': confidence_scores
    })
    
    # Sort by confidence score (descending)
    results_df = results_df.sort_values('confidence_score', ascending=False)
    
    # Save to CSV
    print(f"Saving results to {output_path}...")
    results_df.to_csv(output_path, index=False)
    
    # Print summary
    print(f"\n📊 Prediction Summary:")
    print("=" * 40)
    print(f"Total sequences: {total_count:,}")
    print(f"Functional lncRNAs: {functional_count:,} ({functional_count/total_count*100:.2f}%)")
    print(f"Non-functional lncRNAs: {non_functional_count:,} ({non_functional_count/total_count*100:.2f}%)")
    print(f"Average confidence: {np.mean(confidence_scores):.3f}")
    
    # High confidence predictions
    high_conf_threshold = 0.8
    high_conf_count = np.sum(confidence_scores >= high_conf_threshold)
    print(f"High confidence predictions (≥0.8): {high_conf_count:,} ({high_conf_count/total_count*100:.2f}%)")
    
    # Confidence by prediction type
    functional_conf = confidence_scores[predictions == 1]
    non_functional_conf = confidence_scores[predictions == 0]
    
    if len(functional_conf) > 0:
        print(f"Average confidence for functional predictions: {np.mean(functional_conf):.3f}")
    if len(non_functional_conf) > 0:
        print(f"Average confidence for non-functional predictions: {np.mean(non_functional_conf):.3f}")
    
    # Save summary
    summary_path = output_path.replace('.csv', '_summary.txt')
    with open(summary_path, 'w') as f:
        f.write("lncRNA Functionality Prediction Summary\n")
        f.write("=" * 50 + "\n")
        f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Model: SpliceBERT-MS1024 (best fold: 1)\n")
        f.write(f"Input file: data/human.lncRNA_longest.95243.fa\n\n")
        f.write(f"Total sequences: {total_count:,}\n")
        f.write(f"Functional lncRNAs: {functional_count:,} ({functional_count/total_count*100:.2f}%)\n")
        f.write(f"Non-functional lncRNAs: {non_functional_count:,} ({non_functional_count/total_count*100:.2f}%)\n")
        f.write(f"Average confidence: {np.mean(confidence_scores):.4f}\n")
        f.write(f"High confidence predictions (≥0.8): {high_conf_count:,} ({high_conf_count/total_count*100:.2f}%)\n")
        
        if len(functional_conf) > 0:
            f.write(f"Average confidence for functional predictions: {np.mean(functional_conf):.4f}\n")
        if len(non_functional_conf) > 0:
            f.write(f"Average confidence for non-functional predictions: {np.mean(non_functional_conf):.4f}\n")
    
    print(f"Summary saved to: {summary_path}")
    
    return results_df

def main():
    print("🧬 lncRNA Functionality Prediction")
    print("=" * 50)
    print(f"Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Configuration
    input_file = 'data/human.lncRNA_longest.95243.fa'
    output_file = 'outputs/lncrna_95243_predictions.csv'
    batch_size = 8  # Small batch size to avoid memory issues
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    
    print(f"Input file: {input_file}")
    print(f"Output file: {output_file}")
    print(f"Device: {device}")
    print(f"Batch size: {batch_size}")
    print()
    
    # Create output directory
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    
    # Load sequences
    sequences, sequence_ids = load_fasta_sequences(input_file)
    
    # Load trained model
    model, tokenizer = load_best_splicebert_model()
    model.to(device)
    
    # Make predictions
    predictions, probabilities, confidence_scores = predict_all_sequences(
        model, tokenizer, sequences, sequence_ids, batch_size, device
    )
    
    # Analyze and save results
    results_df = analyze_and_save_results(
        sequence_ids, predictions, probabilities, confidence_scores, output_file
    )
    
    print(f"\n✅ Prediction completed!")
    print(f"Results saved to: {output_file}")
    print(f"End time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
