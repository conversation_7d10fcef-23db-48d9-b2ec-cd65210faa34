# lncRNA Function Prediction with Sliding Windows

This implementation provides a comprehensive solution for predicting lncRNA functionality using RNA language models with sliding window processing for long sequences.

## Overview

- **Task**: Binary classification of lncRNA functionality (functional vs non-functional)
- **Models**: RNA-FM and SpliceBERT-MS1024 comparison
- **Key Feature**: Sliding window processing for sequences up to 8192 nucleotides
- **Data**: 5,698 lncRNA sequences with balanced labels

## Features

### Sliding Window Processing
- **Window Size**: 1024 nucleotides (matching model capacity)
- **Stride**: 512 nucleotides (50% overlap)
- **Max Length**: 8192 nucleotides (sequences longer than this are truncated)
- **Pooling Strategies**: Mean, Max, or Attention-based pooling

### Model Support
- **RNA-FM**: Single nucleotide tokenization, 1024 max length, absolute position embedding
- **SpliceBERT-MS1024**: Single nucleotide tokenization, 1024 max length, absolute position embedding

## File Structure

```
├── downstream/
│   └── train_lncrna_function.py          # Main training script with sliding windows
├── scripts/lncrna_function/
│   ├── rna_fm.sh                         # RNA-FM evaluation script
│   ├── splicebert_ms1024.sh              # SpliceBERT-MS1024 evaluation script
│   └── compare_models.sh                 # Compare both models
├── data/
│   ├── train.fa                          # Training data (4,558 sequences) - FASTA format
│   ├── val.fa                            # Validation data (570 sequences) - FASTA format
│   ├── test.fa                           # Test data (570 sequences) - FASTA format
│   ├── train.csv                         # Training data (CSV format, legacy)
│   ├── val.csv                           # Validation data (CSV format, legacy)
│   └── test.csv                          # Test data (CSV format, legacy)
└── test_lncrna_function.py               # Test script
```

## Data Format

The implementation supports both **FASTA** and **CSV** formats:

### FASTA Format (Recommended)
```
>seq_000000 label=1 length=9388
GCCAGGCACAGTGGCCTGTAATCCTAGCACTTTGGGAGGCCGAGGCGGGCAGATCACCTGAGGTCGGGAGCCCGAGACCA
GCCTGACCAACATGGTGAAACTCCGTCTCTACTAAAAATACAAAAAAAAATTAGAGGGACATGGTGGCACATGCCTGTAA
...
>seq_000001 label=0 length=4267
ACCACAGATGTTGGCAATACCAAGGCAGAATGGACATCTGCCCCCCGTTTCCACCTTAACGGTTTCTAATCCACAGACTT
...
```

### CSV Format (Legacy)
```
sequence,label
GCCAGGCACAGTGGCCTGTAATCCTAGCACTTTGGGAGGCCGAGGCGGGCAGATCACCTGAGGTC...,1
ACCACAGATGTTGGCAATACCAAGGCAGAATGGACATCTGCCCCCCGTTTCCACCTTAACGGTTT...,0
```

### Convert CSV to FASTA
If you have CSV data, convert it to FASTA format:
```bash
python convert_csv_to_fasta.py
```

## Usage

### 1. Test Implementation
```bash
python test_lncrna_function.py
```

### 2. Run Individual Models

**RNA-FM Model:**
```bash
bash scripts/lncrna_function/rna_fm.sh
```

**SpliceBERT-MS1024 Model:**
```bash
bash scripts/lncrna_function/splicebert_ms1024.sh
```

### 3. Compare Both Models
```bash
bash scripts/lncrna_function/compare_models.sh
```

## Data Statistics

- **Total sequences**: 5,698
- **Training set**: 4,558 sequences (50% functional, 50% non-functional)
- **Validation set**: 570 sequences (balanced)
- **Test set**: 570 sequences (balanced)
- **Sequence lengths**: 205-91,671 nucleotides (mean: 5,598, median: 3,877)
- **Sequences > 1024 nt**: 87.5% (require sliding windows)
- **Sequences > 8192 nt**: 18.8% (will be truncated)

## Implementation Details

### Sliding Window Strategy
1. **Window Creation**: Long sequences are split into overlapping windows
2. **Individual Processing**: Each window is processed by the base model
3. **Embedding Extraction**: Extract embeddings from each window
4. **Pooling**: Combine window embeddings using mean/max/attention pooling
5. **Classification**: Final classification based on pooled representation

### Training Configuration
- **Batch Size**: 4 (due to memory constraints with multiple windows)
- **Learning Rate**: 3e-5
- **Epochs**: 30 with early stopping (patience=10)
- **Optimization**: AdamW with warmup
- **Evaluation**: F1-score as primary metric

### Memory Optimization
- **Gradient Accumulation**: 4 steps
- **Mixed Precision**: FP16 training
- **Dynamic Batching**: Handle variable number of windows per sequence

## Expected Results

The sliding window approach allows processing of very long lncRNA sequences while maintaining the full context through overlapping windows. This should provide better performance than simple truncation methods.

## Troubleshooting

### Flash Attention Issues
If you encounter `flash_attn` import errors:
```bash
pip install flash-attn
```
Or modify the model files to use standard attention.

### Memory Issues
- Reduce batch size in the scripts
- Increase gradient accumulation steps
- Use smaller window sizes (e.g., 512)

### Model Loading Issues
Ensure model checkpoints are available in:
- `./checkpoint/opensource/rna-fm/`
- `./checkpoint/opensource/splicebert-ms1024/`

## Output

Results will be saved in:
- `./outputs/ft/lncrna-function/lncRNA_function/rna-fm/`
- `./outputs/ft/lncrna-function/lncRNA_function/splicebert-ms1024/`

Logs will be saved in:
- `./logs/lncrna-function/`

## Evaluation Metrics

- **Accuracy**: Overall classification accuracy
- **Precision**: Weighted precision across classes
- **Recall**: Weighted recall across classes
- **F1-Score**: Weighted F1-score (primary metric)

The implementation provides comprehensive evaluation on the test set after training completion.
