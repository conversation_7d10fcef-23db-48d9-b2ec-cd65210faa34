#!/usr/bin/env python3
"""
Memory-optimized training script for long sequences (9216 bp).
"""

import os
import sys
import gc
import torch
import logging
from dataclasses import dataclass, field
from typing import Optional
from transformers import Trainer, TrainingArguments, HfArgumentParser

# Set memory optimization environment variables
os.environ["PYTORCH_CUDA_ALLOC_CONF"] = "expandable_segments:True"

# Add paths
sys.path.append('.')
sys.path.append('downstream')

# Import custom modules
from sliding_window_dataset import SlidingWindowDataset, load_fasta_data
from sequence_level_model import SequenceLevelAggregationModel, SequenceLevelDataCollator

# Import models and tokenizers
try:
    from tokenizer.tokenization_opensource import OpenRnaLMTokenizer
    TOKENIZER_AVAILABLE = True
except ImportError:
    TOKENIZER_AVAILABLE = False
    print("Warning: OpenRnaLMTokenizer not available")

try:
    from model.rnafm.modeling_rnafm import RnaFmForSequenceClassification
    RNAFM_AVAILABLE = True
except ImportError:
    RNAFM_AVAILABLE = False
    print("Warning: RnaFm model not available")

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MemoryOptimizedTrainer(Trainer):
    """Memory-optimized trainer with aggressive garbage collection."""
    
    def training_step(self, model, inputs, num_items_in_batch=None):
        """Override training step with memory cleanup."""
        # Clear cache before training step
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        # Call parent training step
        loss = super().training_step(model, inputs, num_items_in_batch)
        
        # Clear cache after training step
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        # Force garbage collection every few steps
        if self.state.global_step % 5 == 0:
            gc.collect()
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
        
        return loss
    
    def prediction_step(self, model, inputs, prediction_loss_only, ignore_keys=None):
        """Override prediction step with memory cleanup."""
        # Clear cache before prediction
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        # Call parent prediction step
        result = super().prediction_step(model, inputs, prediction_loss_only, ignore_keys)
        
        # Clear cache after prediction
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        return result

def load_tokenizer(model_type: str, model_path: str):
    """Load the appropriate tokenizer."""
    if model_type == 'rna-fm':
        tokenizer_path = './checkpoint/opensource/rna-fm/'
    elif model_type == 'splicebert-ms1024':
        tokenizer_path = './checkpoint/opensource/splicebert-ms1024/'
    else:
        tokenizer_path = model_path

    tokenizer = OpenRnaLMTokenizer.from_pretrained(
        tokenizer_path,
        model_max_length=1024,
        padding_side="right",
        use_fast=True,
        trust_remote_code=True,
    )
    return tokenizer

def load_model(model_type: str, model_path: str, num_labels: int = 2):
    """Load the appropriate model."""
    if model_type == 'rna-fm':
        if not RNAFM_AVAILABLE:
            raise ImportError("RnaFm model not available")
        model = RnaFmForSequenceClassification.from_pretrained(
            model_path,
            num_labels=num_labels,
            problem_type="single_label_classification",
            trust_remote_code=True,
        )
    else:
        raise ValueError(f"Unsupported model type: {model_type}")
    
    return model

def main():
    """Memory-optimized training function."""
    
    # Clear any existing CUDA cache
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    
    # Force garbage collection
    gc.collect()
    
    # Test parameters - use command line args in real script
    model_type = "rna-fm"
    model_path = "./checkpoint/opensource/rna-fm"
    data_path = "data/cv_splits_565"
    train_file = "fold_0_train.fa"
    val_file = "fold_0_val.fa"
    output_dir = "./test_memory_output"
    
    logger.info("🧬 Memory-Optimized Training")
    logger.info("=" * 50)
    
    # Load tokenizer
    logger.info("Loading tokenizer...")
    tokenizer = load_tokenizer(model_type, model_path)
    
    # Load datasets
    logger.info("Loading datasets...")
    train_sequences, train_labels = load_fasta_data(os.path.join(data_path, train_file))
    val_sequences, val_labels = load_fasta_data(os.path.join(data_path, val_file))
    
    # Use smaller subset for memory test
    train_sequences = train_sequences[:50]  # Reduced from full dataset
    train_labels = train_labels[:50]
    val_sequences = val_sequences[:20]
    val_labels = val_labels[:20]
    
    logger.info(f"Training set: {len(train_sequences)} sequences")
    logger.info(f"Validation set: {len(val_sequences)} sequences")
    
    # Create datasets with full sequence length
    train_dataset = SlidingWindowDataset(
        sequences=train_sequences,
        labels=train_labels,
        tokenizer=tokenizer,
        window_size=1024,
        window_stride=512,
        max_length=9216  # Keep full length as required
    )
    
    val_dataset = SlidingWindowDataset(
        sequences=val_sequences,
        labels=val_labels,
        tokenizer=tokenizer,
        window_size=1024,
        window_stride=512,
        max_length=9216
    )
    
    # Load base model
    logger.info(f"Loading {model_type} model...")
    base_model = load_model(model_type, model_path, num_labels=2)
    
    # Wrap with sequence-level aggregation model
    model = SequenceLevelAggregationModel(
        base_model=base_model,
        pooling_strategy="mean",
        num_labels=2,
        dropout_rate=0.1
    )
    
    # Count parameters
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    logger.info(f"Model parameters: {total_params:,} total, {trainable_params:,} trainable")
    
    # Memory-optimized training arguments
    training_args = TrainingArguments(
        output_dir=output_dir,
        num_train_epochs=1,  # Just test 1 epoch
        per_device_train_batch_size=1,  # Minimum batch size
        per_device_eval_batch_size=1,
        gradient_accumulation_steps=2,  # Very small accumulation
        learning_rate=2e-5,
        warmup_steps=10,
        lr_scheduler_type="linear",
        max_grad_norm=1.0,
        gradient_checkpointing=True,  # Enable gradient checkpointing
        logging_steps=5,
        eval_steps=20,
        evaluation_strategy="steps",
        save_steps=50,
        save_strategy="steps",
        load_best_model_at_end=False,
        dataloader_num_workers=0,  # Disable multiprocessing
        dataloader_pin_memory=False,  # Disable pin memory
        seed=42,
        overwrite_output_dir=True,
        report_to=None,
        # No fp16 to avoid numerical issues
    )
    
    # Data collator
    data_collator = SequenceLevelDataCollator(tokenizer=tokenizer)
    
    # Initialize memory-optimized trainer
    trainer = MemoryOptimizedTrainer(
        model=model,
        args=training_args,
        train_dataset=train_dataset,
        eval_dataset=val_dataset,
        tokenizer=tokenizer,
        data_collator=data_collator,
    )
    
    # Clear cache before training
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    gc.collect()
    
    # Test training
    logger.info("Starting memory-optimized training...")
    try:
        train_result = trainer.train()
        logger.info("✅ Memory-optimized training completed successfully!")
        logger.info(f"Final training loss: {train_result.training_loss:.4f}")
        
    except Exception as e:
        logger.error(f"❌ Memory-optimized training failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
