#!/usr/bin/env python3
"""
Data splitting and cross-validation script for 565_hum_label.fa dataset
Splits data into 10% final test set and 90% for 5-fold cross-validation
"""

import os
import random
import numpy as np
from sklearn.model_selection import StratifiedKFold, train_test_split
from collections import defaultdict
import argparse

def set_seed(seed=42):
    """Set random seeds for reproducibility."""
    random.seed(seed)
    np.random.seed(seed)

def load_fasta_with_labels(fasta_path):
    """Load FASTA file with labels in separate lines."""
    sequences = []
    labels = []
    sequence_ids = []
    
    with open(fasta_path, 'r') as f:
        lines = f.readlines()
    
    i = 0
    seq_id = 0
    while i < len(lines):
        line = lines[i].strip()
        if line.startswith('label='):
            # Extract label
            label = int(line.split('=')[1])
            # Next line should be sequence
            if i + 1 < len(lines):
                sequence = lines[i + 1].strip()
                sequences.append(sequence)
                labels.append(label)
                sequence_ids.append(f"seq_{seq_id}")
                seq_id += 1
                i += 2
            else:
                break
        else:
            i += 1
    
    return sequences, labels, sequence_ids

def save_fasta_with_labels(sequences, labels, sequence_ids, output_path):
    """Save sequences and labels to FASTA format."""
    with open(output_path, 'w') as f:
        for seq_id, seq, label in zip(sequence_ids, sequences, labels):
            f.write(f"label={label}\n")
            f.write(f"{seq}\n")

def create_data_splits(sequences, labels, sequence_ids, test_size=0.1, cv_folds=5, seed=42):
    """Create train/test split and cross-validation folds."""
    set_seed(seed)
    
    # First split: 90% for CV, 10% for final test
    train_sequences, test_sequences, train_labels, test_labels, train_ids, test_ids = train_test_split(
        sequences, labels, sequence_ids, 
        test_size=test_size, 
        stratify=labels, 
        random_state=seed
    )
    
    print(f"Data split completed:")
    print(f"  Training set: {len(train_sequences)} samples")
    print(f"    - Label 0: {sum(1 for l in train_labels if l == 0)}")
    print(f"    - Label 1: {sum(1 for l in train_labels if l == 1)}")
    print(f"  Test set: {len(test_sequences)} samples")
    print(f"    - Label 0: {sum(1 for l in test_labels if l == 0)}")
    print(f"    - Label 1: {sum(1 for l in test_labels if l == 1)}")
    
    # Create cross-validation folds
    skf = StratifiedKFold(n_splits=cv_folds, shuffle=True, random_state=seed)
    cv_folds_data = []
    
    for fold_idx, (train_idx, val_idx) in enumerate(skf.split(train_sequences, train_labels)):
        fold_train_sequences = [train_sequences[i] for i in train_idx]
        fold_train_labels = [train_labels[i] for i in train_idx]
        fold_train_ids = [train_ids[i] for i in train_idx]
        
        fold_val_sequences = [train_sequences[i] for i in val_idx]
        fold_val_labels = [train_labels[i] for i in val_idx]
        fold_val_ids = [train_ids[i] for i in val_idx]
        
        cv_folds_data.append({
            'fold': fold_idx,
            'train': {
                'sequences': fold_train_sequences,
                'labels': fold_train_labels,
                'ids': fold_train_ids
            },
            'val': {
                'sequences': fold_val_sequences,
                'labels': fold_val_labels,
                'ids': fold_val_ids
            }
        })
        
        print(f"  Fold {fold_idx}:")
        print(f"    Train: {len(fold_train_sequences)} samples (Label 0: {sum(1 for l in fold_train_labels if l == 0)}, Label 1: {sum(1 for l in fold_train_labels if l == 1)})")
        print(f"    Val: {len(fold_val_sequences)} samples (Label 0: {sum(1 for l in fold_val_labels if l == 0)}, Label 1: {sum(1 for l in fold_val_labels if l == 1)})")
    
    return {
        'test': {
            'sequences': test_sequences,
            'labels': test_labels,
            'ids': test_ids
        },
        'cv_folds': cv_folds_data
    }

def save_data_splits(data_splits, output_dir):
    """Save data splits to files."""
    os.makedirs(output_dir, exist_ok=True)
    
    # Save final test set
    test_path = os.path.join(output_dir, 'final_test.fa')
    save_fasta_with_labels(
        data_splits['test']['sequences'],
        data_splits['test']['labels'],
        data_splits['test']['ids'],
        test_path
    )
    print(f"Final test set saved to: {test_path}")
    
    # Save cross-validation folds
    for fold_data in data_splits['cv_folds']:
        fold_idx = fold_data['fold']
        
        # Train fold
        train_path = os.path.join(output_dir, f'fold_{fold_idx}_train.fa')
        save_fasta_with_labels(
            fold_data['train']['sequences'],
            fold_data['train']['labels'],
            fold_data['train']['ids'],
            train_path
        )
        
        # Validation fold
        val_path = os.path.join(output_dir, f'fold_{fold_idx}_val.fa')
        save_fasta_with_labels(
            fold_data['val']['sequences'],
            fold_data['val']['labels'],
            fold_data['val']['ids'],
            val_path
        )
        
        print(f"Fold {fold_idx} saved: {train_path}, {val_path}")

def analyze_sequence_lengths(sequences, labels):
    """Analyze sequence length distribution."""
    lengths = [len(seq) for seq in sequences]
    
    print(f"\nSequence length analysis:")
    print(f"  Total sequences: {len(sequences)}")
    print(f"  Mean length: {np.mean(lengths):.1f}")
    print(f"  Median length: {np.median(lengths):.1f}")
    print(f"  Min length: {min(lengths)}")
    print(f"  Max length: {max(lengths)}")
    print(f"  Std deviation: {np.std(lengths):.1f}")
    
    # Length distribution by label
    label_0_lengths = [len(sequences[i]) for i in range(len(sequences)) if labels[i] == 0]
    label_1_lengths = [len(sequences[i]) for i in range(len(sequences)) if labels[i] == 1]
    
    print(f"  Label 0 mean length: {np.mean(label_0_lengths):.1f}")
    print(f"  Label 1 mean length: {np.mean(label_1_lengths):.1f}")
    
    # Count sequences that need truncation (>9216)
    long_sequences = sum(1 for length in lengths if length > 9216)
    print(f"  Sequences > 9216 bp (need truncation): {long_sequences} ({100*long_sequences/len(sequences):.1f}%)")

def main():
    parser = argparse.ArgumentParser(description='Split 565_hum_label.fa for cross-validation')
    parser.add_argument('--input', default='data/565_hum_label.fa', help='Input FASTA file')
    parser.add_argument('--output_dir', default='data/cv_splits', help='Output directory')
    parser.add_argument('--test_size', type=float, default=0.1, help='Test set size (default: 0.1)')
    parser.add_argument('--cv_folds', type=int, default=5, help='Number of CV folds (default: 5)')
    parser.add_argument('--seed', type=int, default=42, help='Random seed (default: 42)')
    
    args = parser.parse_args()
    
    print("🧬 lncRNA Function Prediction - Data Splitting")
    print("=" * 60)
    
    # Load data
    print(f"Loading data from: {args.input}")
    sequences, labels, sequence_ids = load_fasta_with_labels(args.input)
    
    # Analyze sequence lengths
    analyze_sequence_lengths(sequences, labels)
    
    # Create data splits
    print(f"\nCreating data splits (test_size={args.test_size}, cv_folds={args.cv_folds}, seed={args.seed})")
    data_splits = create_data_splits(
        sequences, labels, sequence_ids,
        test_size=args.test_size,
        cv_folds=args.cv_folds,
        seed=args.seed
    )
    
    # Save splits
    print(f"\nSaving data splits to: {args.output_dir}")
    save_data_splits(data_splits, args.output_dir)
    
    print(f"\n✅ Data splitting completed!")
    print(f"Files saved in: {args.output_dir}")

if __name__ == "__main__":
    main()
