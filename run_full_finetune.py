#!/usr/bin/env python3
"""
Run full parameter fine-tuning for lncRNA function prediction
Similar to LoRA fine-tuning but with full parameter updates
"""

import os
import sys
import subprocess
import argparse
from datetime import datetime

def run_full_finetune(
    model_type="splicebert-ms1024",
    data_path="data",
    output_dir="outputs/ft_full",
    num_train_epochs=3,
    learning_rate=2e-5,
    batch_size=4,
    gradient_accumulation_steps=4,
    warmup_ratio=0.1,
    weight_decay=0.01,
    freeze_embeddings=False,
    freeze_encoder_layers=0,
    gradient_checkpointing=True,
    window_size=1024,
    window_stride=512,
    max_sequence_length=9216,
    pooling_strategy="mean",
    patience=8,
    seed=42,
    run_name=None
):
    """Run full parameter fine-tuning with specified parameters."""
    
    if run_name is None:
        run_name = f"{model_type}_full_ft_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    # Create output directory
    model_output_dir = os.path.join(output_dir, model_type)
    os.makedirs(model_output_dir, exist_ok=True)
    
    print(f"🧬 Starting Full Parameter Fine-tuning")
    print(f"Model: {model_type}")
    print(f"Output directory: {model_output_dir}")
    print(f"Run name: {run_name}")
    print(f"Epochs: {num_train_epochs}")
    print(f"Learning rate: {learning_rate}")
    print(f"Batch size: {batch_size}")
    print(f"Gradient accumulation: {gradient_accumulation_steps}")
    print(f"Freeze embeddings: {freeze_embeddings}")
    print(f"Freeze encoder layers: {freeze_encoder_layers}")
    print(f"Gradient checkpointing: {gradient_checkpointing}")
    print()
    
    # Build command
    cmd = [
        "python", "train_lncrna_function_full.py",
        "--model_type", model_type,
        "--data_path", data_path,
        "--data_train_path", "train.fa",
        "--data_val_path", "val.fa",
        "--output_dir", model_output_dir,
        "--run_name", run_name,
        "--num_train_epochs", str(num_train_epochs),
        "--learning_rate", str(learning_rate),
        "--per_device_train_batch_size", str(batch_size),
        "--per_device_eval_batch_size", str(batch_size),
        "--gradient_accumulation_steps", str(gradient_accumulation_steps),
        "--warmup_ratio", str(warmup_ratio),
        "--weight_decay", str(weight_decay),
        "--logging_steps", "10",
        "--eval_steps", "100",
        "--save_steps", "500",
        "--evaluation_strategy", "steps",
        "--save_strategy", "steps",
        "--load_best_model_at_end", "True",
        "--metric_for_best_model", "eval_f1",
        "--greater_is_better", "True",
        "--window_size", str(window_size),
        "--window_stride", str(window_stride),
        "--max_sequence_length", str(max_sequence_length),
        "--pooling_strategy", pooling_strategy,
        "--patience", str(patience),
        "--seed", str(seed),
        "--dataloader_num_workers", "1",
        "--remove_unused_columns", "False",
        "--report_to", "none",
        "--save_model", "True",
        "--use_lora", "False",  # Disable LoRA for full fine-tuning
    ]
    
    # Add freezing options
    if freeze_embeddings:
        cmd.extend(["--freeze_embeddings", "True"])
    
    if freeze_encoder_layers > 0:
        cmd.extend(["--freeze_encoder_layers", str(freeze_encoder_layers)])
    
    if gradient_checkpointing:
        cmd.extend(["--gradient_checkpointing", "True"])
    
    # Add memory optimization flags
    cmd.extend([
        "--fp16", "True",  # Use mixed precision
        "--dataloader_pin_memory", "False",
        "--group_by_length", "False",
    ])
    
    print("Command to execute:")
    print(" ".join(cmd))
    print()
    
    # Run training
    try:
        result = subprocess.run(cmd, check=True, capture_output=False)
        print(f"✅ Training completed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Training failed with error: {e}")
        return False

def run_multiple_models(
    models=["rna-fm", "splicebert-ms1024"],
    data_path="data",
    output_dir="outputs/ft_full",
    **kwargs
):
    """Run full fine-tuning for multiple models."""
    
    print(f"🧬 Running Full Parameter Fine-tuning for Multiple Models")
    print(f"Models: {models}")
    print(f"Data path: {data_path}")
    print(f"Output directory: {output_dir}")
    print()
    
    results = {}
    
    for model_type in models:
        print(f"\n{'='*60}")
        print(f"Training {model_type}")
        print(f"{'='*60}")
        
        success = run_full_finetune(
            model_type=model_type,
            data_path=data_path,
            output_dir=output_dir,
            **kwargs
        )
        
        results[model_type] = success
        
        if success:
            print(f"✅ {model_type} training completed successfully")
        else:
            print(f"❌ {model_type} training failed")
    
    # Summary
    print(f"\n{'='*60}")
    print("Training Summary")
    print(f"{'='*60}")
    
    for model_type, success in results.items():
        status = "✅ Success" if success else "❌ Failed"
        print(f"{model_type}: {status}")
    
    return results

def main():
    parser = argparse.ArgumentParser(description="Run full parameter fine-tuning for lncRNA function prediction")
    
    # Model and data arguments
    parser.add_argument("--model_type", type=str, default="splicebert-ms1024", 
                       choices=["rna-fm", "splicebert-ms1024"],
                       help="Model type to train")
    parser.add_argument("--models", type=str, nargs="+", default=None,
                       help="Multiple models to train (overrides --model_type)")
    parser.add_argument("--data_path", type=str, default="data", 
                       help="Path to training data")
    parser.add_argument("--output_dir", type=str, default="outputs/ft_full",
                       help="Output directory")
    
    # Training arguments
    parser.add_argument("--num_train_epochs", type=int, default=3,
                       help="Number of training epochs")
    parser.add_argument("--learning_rate", type=float, default=2e-5,
                       help="Learning rate")
    parser.add_argument("--batch_size", type=int, default=4,
                       help="Batch size per device")
    parser.add_argument("--gradient_accumulation_steps", type=int, default=4,
                       help="Gradient accumulation steps")
    parser.add_argument("--warmup_ratio", type=float, default=0.1,
                       help="Warmup ratio")
    parser.add_argument("--weight_decay", type=float, default=0.01,
                       help="Weight decay")
    
    # Full fine-tuning specific arguments
    parser.add_argument("--freeze_embeddings", action="store_true",
                       help="Freeze embedding layers")
    parser.add_argument("--freeze_encoder_layers", type=int, default=0,
                       help="Number of encoder layers to freeze")
    parser.add_argument("--gradient_checkpointing", action="store_true", default=True,
                       help="Use gradient checkpointing")
    
    # Model configuration
    parser.add_argument("--window_size", type=int, default=1024,
                       help="Sliding window size")
    parser.add_argument("--window_stride", type=int, default=512,
                       help="Sliding window stride")
    parser.add_argument("--max_sequence_length", type=int, default=9216,
                       help="Maximum sequence length")
    parser.add_argument("--pooling_strategy", type=str, default="mean",
                       choices=["mean", "max", "attention"],
                       help="Pooling strategy for sliding windows")
    
    # Other arguments
    parser.add_argument("--patience", type=int, default=8,
                       help="Early stopping patience")
    parser.add_argument("--seed", type=int, default=42,
                       help="Random seed")
    parser.add_argument("--run_name", type=str, default=None,
                       help="Run name for logging")
    
    args = parser.parse_args()
    
    # Convert args to dict for passing to functions
    kwargs = {
        "num_train_epochs": args.num_train_epochs,
        "learning_rate": args.learning_rate,
        "batch_size": args.batch_size,
        "gradient_accumulation_steps": args.gradient_accumulation_steps,
        "warmup_ratio": args.warmup_ratio,
        "weight_decay": args.weight_decay,
        "freeze_embeddings": args.freeze_embeddings,
        "freeze_encoder_layers": args.freeze_encoder_layers,
        "gradient_checkpointing": args.gradient_checkpointing,
        "window_size": args.window_size,
        "window_stride": args.window_stride,
        "max_sequence_length": args.max_sequence_length,
        "pooling_strategy": args.pooling_strategy,
        "patience": args.patience,
        "seed": args.seed,
        "run_name": args.run_name,
    }
    
    if args.models:
        # Train multiple models
        run_multiple_models(
            models=args.models,
            data_path=args.data_path,
            output_dir=args.output_dir,
            **kwargs
        )
    else:
        # Train single model
        run_full_finetune(
            model_type=args.model_type,
            data_path=args.data_path,
            output_dir=args.output_dir,
            **kwargs
        )

if __name__ == "__main__":
    main()
