#!/usr/bin/env python3
"""
Debug script to check model outputs and identify the issue.
"""

import os
import sys
import torch
import numpy as np
import logging
from dataclasses import dataclass, field
from typing import Optional

# Add paths
sys.path.append('.')
sys.path.append('downstream')

# Import custom modules
from sliding_window_dataset import SlidingWindowDataset, load_fasta_data
from sequence_level_model import SequenceLevelAggregationModel, SequenceLevelDataCollator

# Import models and tokenizers
try:
    from tokenizer.tokenization_opensource import OpenRnaLMTokenizer
    TOKENIZER_AVAILABLE = True
except ImportError:
    TOKENIZER_AVAILABLE = False
    print("Warning: OpenRnaLMTokenizer not available")

try:
    from model.rnafm.modeling_rnafm import RnaFmForSequenceClassification
    RNAFM_AVAILABLE = True
except ImportError:
    RNAFM_AVAILABLE = False
    print("Warning: RnaFm model not available")

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def load_tokenizer(model_type: str, model_path: str):
    """Load the appropriate tokenizer."""
    if model_type == 'rna-fm':
        tokenizer_path = './checkpoint/opensource/rna-fm/'
    else:
        tokenizer_path = model_path

    tokenizer = OpenRnaLMTokenizer.from_pretrained(
        tokenizer_path,
        model_max_length=1024,
        padding_side="right",
        use_fast=True,
        trust_remote_code=True,
    )
    return tokenizer

def load_model(model_type: str, model_path: str, num_labels: int = 2):
    """Load the appropriate model."""
    if model_type == 'rna-fm':
        if not RNAFM_AVAILABLE:
            raise ImportError("RnaFm model not available")
        model = RnaFmForSequenceClassification.from_pretrained(
            model_path,
            num_labels=num_labels,
            problem_type="single_label_classification",
            trust_remote_code=True,
        )
    else:
        raise ValueError(f"Unsupported model type: {model_type}")
    
    return model

def debug_model_outputs():
    """Debug model outputs to identify the issue."""
    
    # Test parameters
    model_type = "rna-fm"
    model_path = "./checkpoint/opensource/rna-fm"
    data_path = "data/cv_splits_565"
    train_file = "fold_0_train.fa"
    val_file = "fold_0_val.fa"
    
    logger.info("🔍 Debugging Model Outputs")
    logger.info("=" * 50)
    
    # Load tokenizer
    logger.info("Loading tokenizer...")
    tokenizer = load_tokenizer(model_type, model_path)
    
    # Load datasets (small subset for debugging)
    logger.info("Loading datasets...")
    train_sequences, train_labels = load_fasta_data(os.path.join(data_path, train_file))
    val_sequences, val_labels = load_fasta_data(os.path.join(data_path, val_file))
    
    # Use small subset for debugging
    train_sequences = train_sequences[:5]
    train_labels = train_labels[:5]
    val_sequences = val_sequences[:3]
    val_labels = val_labels[:3]
    
    logger.info(f"Training set: {len(train_sequences)} sequences")
    logger.info(f"Training labels: {train_labels}")
    logger.info(f"Validation set: {len(val_sequences)} sequences")
    logger.info(f"Validation labels: {val_labels}")
    
    # Create datasets
    train_dataset = SlidingWindowDataset(
        sequences=train_sequences,
        labels=train_labels,
        tokenizer=tokenizer,
        window_size=1024,
        window_stride=512,
        max_length=9216
    )
    
    val_dataset = SlidingWindowDataset(
        sequences=val_sequences,
        labels=val_labels,
        tokenizer=tokenizer,
        window_size=1024,
        window_stride=512,
        max_length=9216
    )
    
    # Load base model
    logger.info(f"Loading {model_type} model...")
    base_model = load_model(model_type, model_path, num_labels=2)
    
    # Wrap with sequence-level aggregation model
    model = SequenceLevelAggregationModel(
        base_model=base_model,
        pooling_strategy="mean",
        num_labels=2,
        dropout_rate=0.1
    )
    
    # Set model to evaluation mode
    model.eval()
    
    # Data collator
    data_collator = SequenceLevelDataCollator(tokenizer=tokenizer)
    
    # Test a few samples
    logger.info("Testing model outputs...")
    
    with torch.no_grad():
        for i in range(min(3, len(val_dataset))):
            sample = val_dataset[i]
            logger.info(f"\n--- Sample {i} ---")
            logger.info(f"Label: {sample['labels']}")
            logger.info(f"Input shape: {sample['input_ids'].shape}")
            logger.info(f"Attention mask shape: {sample['attention_mask'].shape}")
            
            # Prepare batch
            batch = data_collator([sample])
            
            # Forward pass
            try:
                outputs = model(**batch)
                logger.info(f"Model output keys: {outputs.keys() if hasattr(outputs, 'keys') else 'No keys'}")
                
                if hasattr(outputs, 'logits'):
                    logits = outputs.logits
                    logger.info(f"Logits shape: {logits.shape}")
                    logger.info(f"Logits values: {logits}")
                    logger.info(f"Logits range: [{logits.min().item():.4f}, {logits.max().item():.4f}]")
                    
                    # Check for NaN or inf
                    if torch.isnan(logits).any():
                        logger.warning("⚠️  NaN detected in logits!")
                    if torch.isinf(logits).any():
                        logger.warning("⚠️  Inf detected in logits!")
                    
                    # Compute probabilities
                    probs = torch.softmax(logits, dim=-1)
                    logger.info(f"Probabilities: {probs}")
                    
                    # Compute predictions
                    predictions = torch.argmax(logits, dim=-1)
                    logger.info(f"Predictions: {predictions}")
                
                if hasattr(outputs, 'loss') and outputs.loss is not None:
                    loss = outputs.loss
                    logger.info(f"Loss: {loss.item()}")
                    
                    # Check for NaN or inf in loss
                    if torch.isnan(loss):
                        logger.warning("⚠️  NaN detected in loss!")
                    if torch.isinf(loss):
                        logger.warning("⚠️  Inf detected in loss!")
                else:
                    logger.info("No loss in output")
                    
            except Exception as e:
                logger.error(f"❌ Error during forward pass: {e}")
                import traceback
                traceback.print_exc()
    
    # Test batch processing
    logger.info("\n--- Testing Batch Processing ---")
    try:
        # Create a small batch
        batch_samples = [val_dataset[i] for i in range(min(2, len(val_dataset)))]
        batch = data_collator(batch_samples)
        
        logger.info(f"Batch input_ids shape: {batch['input_ids'].shape}")
        logger.info(f"Batch labels shape: {batch['labels'].shape}")
        logger.info(f"Batch labels: {batch['labels']}")
        
        with torch.no_grad():
            outputs = model(**batch)
            
            if hasattr(outputs, 'logits'):
                logits = outputs.logits
                logger.info(f"Batch logits shape: {logits.shape}")
                logger.info(f"Batch logits: {logits}")
                
                # Check predictions
                predictions = torch.argmax(logits, dim=-1)
                logger.info(f"Batch predictions: {predictions}")
                
                # Check if all predictions are the same
                if len(torch.unique(predictions)) == 1:
                    logger.warning("⚠️  All predictions are the same class!")
                
            if hasattr(outputs, 'loss') and outputs.loss is not None:
                loss = outputs.loss
                logger.info(f"Batch loss: {loss.item()}")
            
    except Exception as e:
        logger.error(f"❌ Error during batch processing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_model_outputs()
