#!/usr/bin/env python3
"""
5-fold cross-validation training script for 565_hum_label.fa lncRNA function prediction
Runs full fine-tuning for RNA-FM and SpliceBERT models
"""

import os
import sys
import json
import subprocess
import argparse
import pandas as pd
import numpy as np
from collections import defaultdict
from datetime import datetime

def run_single_fold_training(
    model_type: str,
    fold: int,
    model_path: str,
    data_dir: str,
    output_base_dir: str,
    config: dict
):
    """Run training for a single fold."""
    output_dir = os.path.join(output_base_dir, model_type, f"fold_{fold}")
    os.makedirs(output_dir, exist_ok=True)
    
    # Prepare command
    cmd = [
        "python", "train_full_finetune_565.py",
        "--model_name_or_path", model_path,
        "--data_path", data_dir,
        "--data_train_path", f"fold_{fold}_train.fa",
        "--data_val_path", f"fold_{fold}_val.fa",
        "--output_dir", output_dir,
        "--model_type", model_type,
        
        # Model configuration
        "--model_max_length", str(config["window_size"]),
        "--window_size", str(config["window_size"]),
        "--window_stride", str(config["window_stride"]),
        "--max_sequence_length", str(config["max_sequence_length"]),
        "--pooling_strategy", config["pooling_strategy"],
        
        # Training parameters
        "--per_device_train_batch_size", str(config["batch_size"]),
        "--per_device_eval_batch_size", str(config["batch_size"]),
        "--gradient_accumulation_steps", str(config["gradient_accumulation_steps"]),
        "--learning_rate", str(config["learning_rate"]),
        "--num_train_epochs", str(config["num_epochs"]),
        "--patience", str(config["patience"]),
        "--warmup_steps", str(config["warmup_steps"]),
        
        # Logging and evaluation
        "--logging_steps", str(config["logging_steps"]),
        "--eval_steps", str(config["eval_steps"]),
        "--save_steps", str(config["save_steps"]),
        "--evaluation_strategy", "steps",
        "--save_strategy", "steps",
        "--load_best_model_at_end", "True",
        "--metric_for_best_model", "eval_f1",
        "--greater_is_better", "True",
        
        # Other settings
        "--fp16",
        "--dataloader_num_workers", "4",
        "--seed", str(config["seed"]),
        "--overwrite_output_dir"
    ]
    
    print(f"🚀 Training {model_type} fold {fold}...")
    print(f"Command: {' '.join(cmd)}")
    
    # Run training
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=7200)  # 2 hour timeout
        
        if result.returncode == 0:
            print(f"✅ Successfully completed {model_type} fold {fold}")
            return True, result.stdout
        else:
            print(f"❌ Failed {model_type} fold {fold}")
            print(f"Error: {result.stderr}")
            return False, result.stderr
            
    except subprocess.TimeoutExpired:
        print(f"⏰ Timeout for {model_type} fold {fold}")
        return False, "Training timeout"
    except Exception as e:
        print(f"💥 Exception for {model_type} fold {fold}: {e}")
        return False, str(e)

def collect_fold_results(output_base_dir: str, model_types: list, num_folds: int):
    """Collect results from all folds."""
    results = defaultdict(list)
    
    for model_type in model_types:
        for fold in range(num_folds):
            fold_dir = os.path.join(output_base_dir, model_type, f"fold_{fold}")
            summary_file = os.path.join(fold_dir, "training_summary.txt")
            
            if os.path.exists(summary_file):
                # Parse training summary
                fold_results = {"model": model_type, "fold": fold}
                
                with open(summary_file, 'r') as f:
                    content = f.read()
                    
                    # Extract metrics (simple parsing)
                    lines = content.split('\n')
                    for line in lines:
                        if 'eval_accuracy:' in line:
                            fold_results['accuracy'] = float(line.split(':')[1].strip())
                        elif 'eval_precision:' in line:
                            fold_results['precision'] = float(line.split(':')[1].strip())
                        elif 'eval_recall:' in line:
                            fold_results['recall'] = float(line.split(':')[1].strip())
                        elif 'eval_f1:' in line:
                            fold_results['f1'] = float(line.split(':')[1].strip())
                        elif 'eval_auc:' in line:
                            fold_results['auc'] = float(line.split(':')[1].strip())
                        elif 'Training loss:' in line:
                            fold_results['train_loss'] = float(line.split(':')[1].strip())
                
                results[model_type].append(fold_results)
            else:
                print(f"⚠️  Missing results for {model_type} fold {fold}")
    
    return results

def compute_cv_statistics(results: dict):
    """Compute cross-validation statistics."""
    cv_stats = {}
    
    for model_type, fold_results in results.items():
        if not fold_results:
            continue
            
        # Convert to DataFrame for easier computation
        df = pd.DataFrame(fold_results)
        
        stats = {}
        metrics = ['accuracy', 'precision', 'recall', 'f1', 'auc', 'train_loss']
        
        for metric in metrics:
            if metric in df.columns:
                values = df[metric].dropna()
                if len(values) > 0:
                    stats[f'{metric}_mean'] = values.mean()
                    stats[f'{metric}_std'] = values.std()
                    stats[f'{metric}_min'] = values.min()
                    stats[f'{metric}_max'] = values.max()
        
        stats['num_folds'] = len(fold_results)
        cv_stats[model_type] = stats
    
    return cv_stats

def save_results(results: dict, cv_stats: dict, output_dir: str):
    """Save cross-validation results."""
    # Save detailed results
    all_results = []
    for model_type, fold_results in results.items():
        all_results.extend(fold_results)
    
    results_df = pd.DataFrame(all_results)
    results_df.to_csv(os.path.join(output_dir, "cv_detailed_results.csv"), index=False)
    
    # Save summary statistics
    summary_data = []
    for model_type, stats in cv_stats.items():
        row = {"model": model_type}
        row.update(stats)
        summary_data.append(row)
    
    summary_df = pd.DataFrame(summary_data)
    summary_df.to_csv(os.path.join(output_dir, "cv_summary_results.csv"), index=False)
    
    # Save JSON format
    with open(os.path.join(output_dir, "cv_results.json"), 'w') as f:
        json.dump({
            "detailed_results": results,
            "summary_statistics": cv_stats,
            "timestamp": datetime.now().isoformat()
        }, f, indent=2)
    
    print(f"📊 Results saved to: {output_dir}")

def print_results_summary(cv_stats: dict):
    """Print a summary of cross-validation results."""
    print("\n" + "="*80)
    print("🏆 CROSS-VALIDATION RESULTS SUMMARY")
    print("="*80)
    
    for model_type, stats in cv_stats.items():
        print(f"\n📈 {model_type.upper()}")
        print("-" * 40)
        
        metrics = ['accuracy', 'precision', 'recall', 'f1', 'auc']
        for metric in metrics:
            mean_key = f'{metric}_mean'
            std_key = f'{metric}_std'
            
            if mean_key in stats and std_key in stats:
                mean_val = stats[mean_key]
                std_val = stats[std_key]
                print(f"  {metric.capitalize():12}: {mean_val:.4f} ± {std_val:.4f}")
        
        print(f"  {'Folds':12}: {stats.get('num_folds', 0)}")

def main():
    parser = argparse.ArgumentParser(description='5-fold CV training for 565_hum_label.fa')
    parser.add_argument('--data_dir', default='data/cv_splits_565', help='Data directory')
    parser.add_argument('--output_dir', default='outputs/cv_full_finetune_565', help='Output directory')
    parser.add_argument('--models', nargs='+', default=['rna-fm', 'splicebert-ms1024'], help='Models to train')
    parser.add_argument('--num_folds', type=int, default=5, help='Number of CV folds')
    parser.add_argument('--config_file', default='cv_config_565.json', help='Configuration file')
    
    args = parser.parse_args()
    
    print("🧬 5-Fold Cross-Validation Training for 565_hum_label.fa")
    print("=" * 70)
    print(f"Data directory: {args.data_dir}")
    print(f"Output directory: {args.output_dir}")
    print(f"Models: {args.models}")
    print(f"Number of folds: {args.num_folds}")
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Load or create configuration
    if os.path.exists(args.config_file):
        with open(args.config_file, 'r') as f:
            config = json.load(f)
        print(f"Loaded configuration from: {args.config_file}")
    else:
        # Default configuration
        config = {
            "window_size": 1024,
            "window_stride": 512,
            "max_sequence_length": 9216,
            "pooling_strategy": "mean",
            "batch_size": 4,
            "gradient_accumulation_steps": 4,
            "learning_rate": 2e-5,
            "num_epochs": 20,
            "patience": 10,
            "warmup_steps": 100,
            "logging_steps": 50,
            "eval_steps": 100,
            "save_steps": 500,
            "seed": 42
        }
        
        # Save default configuration
        with open(args.config_file, 'w') as f:
            json.dump(config, f, indent=2)
        print(f"Created default configuration: {args.config_file}")
    
    # Model paths (update these according to your setup)
    model_paths = {
        "rna-fm": "./checkpoint/opensource/rna-fm",
        "splicebert-ms1024": "./checkpoint/opensource/splicebert-ms1024"
    }
    
    # Training loop
    training_results = {}
    
    for model_type in args.models:
        if model_type not in model_paths:
            print(f"⚠️  Unknown model type: {model_type}")
            continue
        
        model_path = model_paths[model_type]
        if not os.path.exists(model_path):
            print(f"⚠️  Model path not found: {model_path}")
            continue
        
        print(f"\n🚀 Starting training for {model_type}")
        print(f"Model path: {model_path}")
        
        model_results = []
        
        for fold in range(args.num_folds):
            success, output = run_single_fold_training(
                model_type=model_type,
                fold=fold,
                model_path=model_path,
                data_dir=args.data_dir,
                output_base_dir=args.output_dir,
                config=config
            )
            
            model_results.append({
                "fold": fold,
                "success": success,
                "output": output
            })
            
            if not success:
                print(f"❌ Failed to train {model_type} fold {fold}")
                # Continue with other folds
        
        training_results[model_type] = model_results
        print(f"✅ Completed training for {model_type}")
    
    # Collect and analyze results
    print("\n📊 Collecting results...")
    results = collect_fold_results(args.output_dir, args.models, args.num_folds)
    cv_stats = compute_cv_statistics(results)
    
    # Save results
    save_results(results, cv_stats, args.output_dir)
    
    # Print summary
    print_results_summary(cv_stats)
    
    print(f"\n🎉 Cross-validation completed!")
    print(f"Results saved in: {args.output_dir}")

if __name__ == "__main__":
    main()
