#!/usr/bin/env python3
"""
Full parameter fine-tuning script for lncRNA function prediction
Supports RNA-FM and SpliceBERT models with sliding window and sequence-level aggregation
"""

import os
import sys
import random
import logging
import numpy as np
import torch
import transformers
from dataclasses import dataclass, field
from typing import Optional, List
from torch.utils.data import Dataset
from transformers import Trainer, EarlyStoppingCallback
from sklearn.metrics import accuracy_score, precision_recall_fscore_support, roc_auc_score

# Add paths
sys.path.append('.')
sys.path.append('downstream')

# Import models and tokenizers
try:
    from tokenizer.tokenization_opensource import OpenRnaLMTokenizer
    TOKENIZER_AVAILABLE = True
except ImportError:
    TOKENIZER_AVAILABLE = False
    print("Warning: OpenRnaLMTokenizer not available")

try:
    from model.rnafm.modeling_rnafm import RnaFmForSequenceClassification
    RNAFM_AVAILABLE = True
except ImportError:
    RNAFM_AVAILABLE = False
    print("Warning: RnaFm model not available")

try:
    from model.splicebert.modeling_splicebert import SpliceBertForSequenceClassification
    SPLICEBERT_AVAILABLE = True
except ImportError:
    SPLICEBERT_AVAILABLE = False
    print("Warning: SpliceBert model not available")

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class ModelArguments:
    model_name_or_path: Optional[str] = field(default="")
    cache_dir: Optional[str] = field(default=None)

@dataclass
class DataArguments:
    data_path: str = field(default="", metadata={"help": "Path to the training data."})
    data_train_path: str = field(default="train.fa")
    data_val_path: str = field(default="val.fa")
    data_test_path: str = field(default="test.fa")
    kmer: int = field(default=-1)

@dataclass
class FullTrainingArguments(transformers.TrainingArguments):
    """Custom training arguments for full parameter fine-tuning."""
    run_name: str = field(default="run")
    model_max_length: int = field(default=1024, metadata={"help": "Maximum sequence length."})

    # Model configuration
    model_type: str = field(default="rna-fm")
    token_type: str = field(default="single")
    train_from_scratch: bool = field(default=False)
    save_model: bool = field(default=True)
    
    # Full fine-tuning configuration (no LoRA)
    use_lora: bool = field(default=False, metadata={"help": "Whether to use LoRA (disabled for full fine-tuning)"})
    
    # Sliding window configuration
    window_size: int = field(default=1024, metadata={"help": "Size of sliding window"})
    window_stride: int = field(default=512, metadata={"help": "Stride of sliding window"})
    max_sequence_length: int = field(default=9216, metadata={"help": "Maximum sequence length before truncation"})
    pooling_strategy: str = field(default="mean", metadata={"help": "Pooling strategy: mean, max, attention"})
    
    # Training configuration
    patience: int = field(default=8)
    num_workers: int = field(default=1)
    seed: int = field(default=42)
    attn_implementation: str = field(default="eager")
    
    # Full fine-tuning specific parameters
    freeze_embeddings: bool = field(default=False, metadata={"help": "Whether to freeze embedding layers"})
    freeze_encoder_layers: int = field(default=0, metadata={"help": "Number of encoder layers to freeze (0 = no freezing)"})
    gradient_checkpointing: bool = field(default=True, metadata={"help": "Use gradient checkpointing to save memory"})

def set_seed(seed):
    """Set random seeds for reproducibility."""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed_all(seed)

def load_fasta_data(file_path):
    """Load FASTA data with labels."""
    sequences = []
    labels = []
    
    with open(file_path, 'r') as f:
        lines = f.readlines()
    
    i = 0
    while i < len(lines):
        line = lines[i].strip()
        if line.startswith('>'):
            # Parse header for label
            header = line[1:]  # Remove '>'
            if '|label:' in header:
                label_str = header.split('|label:')[1].split('|')[0]
                label = int(label_str)
            else:
                # Try to infer from filename or default
                if 'functional' in file_path.lower():
                    label = 1
                elif 'non_functional' in file_path.lower():
                    label = 0
                else:
                    label = 0  # Default to non-functional
            
            # Get sequence
            i += 1
            sequence = ""
            while i < len(lines) and not lines[i].startswith('>'):
                sequence += lines[i].strip()
                i += 1
            
            if sequence:  # Only add if sequence is not empty
                sequences.append(sequence)
                labels.append(label)
        else:
            i += 1
    
    logger.info(f"Loaded {len(sequences)} sequences from {file_path}")
    logger.info(f"Label distribution: {np.bincount(labels)}")
    
    return sequences, labels

class SlidingWindowDataset(Dataset):
    """Dataset with sliding window support for long sequences."""
    
    def __init__(self, sequences, labels, tokenizer, window_size=1024, window_stride=512, 
                 max_sequence_length=9216, pooling_strategy="mean"):
        self.sequences = sequences
        self.labels = labels
        self.tokenizer = tokenizer
        self.window_size = window_size
        self.window_stride = window_stride
        self.max_sequence_length = max_sequence_length
        self.pooling_strategy = pooling_strategy
        
        logger.info(f"Dataset initialized with {len(sequences)} sequences")
        logger.info(f"Window size: {window_size}, stride: {window_stride}")
        logger.info(f"Max sequence length: {max_sequence_length}")
        logger.info(f"Pooling strategy: {pooling_strategy}")
    
    def __len__(self):
        return len(self.sequences)
    
    def __getitem__(self, idx):
        sequence = self.sequences[idx]
        label = self.labels[idx]
        
        # Truncate if too long
        if len(sequence) > self.max_sequence_length:
            sequence = sequence[:self.max_sequence_length]
        
        # Tokenize the sequence
        encoding = self.tokenizer(
            sequence,
            truncation=True,
            padding=True,
            max_length=self.window_size,
            return_tensors='pt'
        )
        
        return {
            'input_ids': encoding['input_ids'].squeeze(),
            'attention_mask': encoding['attention_mask'].squeeze(),
            'labels': torch.tensor(label, dtype=torch.long)
        }

class SlidingWindowModel(torch.nn.Module):
    """Wrapper model that handles sliding windows and aggregation."""
    
    def __init__(self, base_model, window_size=1024, window_stride=512, pooling_strategy="mean"):
        super().__init__()
        self.base_model = base_model
        self.window_size = window_size
        self.window_stride = window_stride
        self.pooling_strategy = pooling_strategy
        
        # For compatibility with Trainer
        self.config = base_model.config
        
    def forward(self, input_ids, attention_mask=None, labels=None, **kwargs):
        batch_size, seq_len = input_ids.shape
        
        # If sequence fits in one window, process directly
        if seq_len <= self.window_size:
            return self.base_model(
                input_ids=input_ids,
                attention_mask=attention_mask,
                labels=labels,
                **kwargs
            )
        
        # Process with sliding windows
        all_logits = []
        
        for i in range(0, seq_len - self.window_size + 1, self.window_stride):
            end_idx = min(i + self.window_size, seq_len)
            window_input_ids = input_ids[:, i:end_idx]
            window_attention_mask = attention_mask[:, i:end_idx] if attention_mask is not None else None
            
            # Pad if necessary
            if window_input_ids.shape[1] < self.window_size:
                pad_length = self.window_size - window_input_ids.shape[1]
                window_input_ids = torch.nn.functional.pad(window_input_ids, (0, pad_length), value=0)
                if window_attention_mask is not None:
                    window_attention_mask = torch.nn.functional.pad(window_attention_mask, (0, pad_length), value=0)
            
            # Get predictions for this window
            outputs = self.base_model(
                input_ids=window_input_ids,
                attention_mask=window_attention_mask,
                **kwargs
            )
            all_logits.append(outputs.logits)
        
        # Aggregate logits
        if len(all_logits) == 0:
            # Fallback: use the last window
            window_input_ids = input_ids[:, -self.window_size:]
            window_attention_mask = attention_mask[:, -self.window_size:] if attention_mask is not None else None
            outputs = self.base_model(
                input_ids=window_input_ids,
                attention_mask=window_attention_mask,
                **kwargs
            )
            aggregated_logits = outputs.logits
        else:
            stacked_logits = torch.stack(all_logits, dim=1)  # [batch_size, num_windows, num_classes]
            
            if self.pooling_strategy == "mean":
                aggregated_logits = torch.mean(stacked_logits, dim=1)
            elif self.pooling_strategy == "max":
                aggregated_logits = torch.max(stacked_logits, dim=1)[0]
            else:  # Default to mean
                aggregated_logits = torch.mean(stacked_logits, dim=1)
        
        # Calculate loss if labels provided
        loss = None
        if labels is not None:
            loss_fct = torch.nn.CrossEntropyLoss()
            loss = loss_fct(aggregated_logits.view(-1, self.base_model.config.num_labels), labels.view(-1))
        
        # Return in the expected format
        return type('ModelOutput', (), {
            'loss': loss,
            'logits': aggregated_logits,
            'hidden_states': None,
            'attentions': None
        })()

def freeze_model_layers(model, freeze_embeddings=False, freeze_encoder_layers=0):
    """Freeze specified layers of the model."""
    if freeze_embeddings:
        logger.info("Freezing embedding layers...")
        if hasattr(model, 'embeddings'):
            for param in model.embeddings.parameters():
                param.requires_grad = False
        elif hasattr(model, 'bert') and hasattr(model.bert, 'embeddings'):
            for param in model.bert.embeddings.parameters():
                param.requires_grad = False
    
    if freeze_encoder_layers > 0:
        logger.info(f"Freezing first {freeze_encoder_layers} encoder layers...")
        encoder = None
        if hasattr(model, 'encoder'):
            encoder = model.encoder
        elif hasattr(model, 'bert') and hasattr(model.bert, 'encoder'):
            encoder = model.bert.encoder
        elif hasattr(model, 'splicebert') and hasattr(model.splicebert, 'encoder'):
            encoder = model.splicebert.encoder
        
        if encoder and hasattr(encoder, 'layer'):
            for i in range(min(freeze_encoder_layers, len(encoder.layer))):
                for param in encoder.layer[i].parameters():
                    param.requires_grad = False
                logger.info(f"Frozen encoder layer {i}")

def compute_metrics(eval_pred):
    """Compute metrics for evaluation."""
    predictions, labels = eval_pred
    predictions = np.argmax(predictions, axis=1)
    
    accuracy = accuracy_score(labels, predictions)
    precision, recall, f1, _ = precision_recall_fscore_support(labels, predictions, average='binary')
    
    try:
        # For AUC, we need probabilities
        probs = torch.softmax(torch.tensor(eval_pred.predictions), dim=-1)[:, 1].numpy()
        auc = roc_auc_score(labels, probs)
    except:
        auc = 0.0
    
    return {
        'accuracy': accuracy,
        'f1': f1,
        'precision': precision,
        'recall': recall,
        'auc': auc
    }

def main():
    # Parse arguments
    parser = transformers.HfArgumentParser((ModelArguments, DataArguments, FullTrainingArguments))
    model_args, data_args, training_args = parser.parse_args_into_dataclasses()

    # Set seed
    set_seed(training_args.seed)

    # Setup logging
    logging.basicConfig(
        format="%(asctime)s - %(levelname)s - %(name)s - %(message)s",
        datefmt="%m/%d/%Y %H:%M:%S",
        level=logging.INFO,
    )

    print(f"🧬 Full Parameter Fine-tuning for lncRNA Function Prediction")
    print(f"Model: {training_args.model_type}")
    print(f"LoRA enabled: {training_args.use_lora}")
    print(f"Window size: {training_args.window_size}, stride: {training_args.window_stride}")
    print(f"Freeze embeddings: {training_args.freeze_embeddings}")
    print(f"Freeze encoder layers: {training_args.freeze_encoder_layers}")
    print(f"Gradient checkpointing: {training_args.gradient_checkpointing}")

    # Determine model path
    if training_args.model_type == "rna-fm":
        if not RNAFM_AVAILABLE:
            raise ImportError("RNA-FM model not available")
        model_path = model_args.model_name_or_path or "./checkpoint/opensource/rna-fm/"
    elif "splicebert" in training_args.model_type:
        if not SPLICEBERT_AVAILABLE:
            raise ImportError("SpliceBERT model not available")
        model_path = model_args.model_name_or_path or "./checkpoint/opensource/splicebert-ms1024/"
    else:
        raise ValueError(f"Unsupported model type: {training_args.model_type}")

    print(f"Loading model from: {model_path}")

    # Load tokenizer
    if not TOKENIZER_AVAILABLE:
        raise ImportError("Tokenizer not available")

    tokenizer = OpenRnaLMTokenizer.from_pretrained(
        model_path,
        model_max_length=training_args.model_max_length,
        padding_side="right",
        use_fast=True,
        trust_remote_code=True,
    )

    # Load base model
    if training_args.model_type == "rna-fm":
        base_model = RnaFmForSequenceClassification.from_pretrained(
            model_path,
            num_labels=2,
            problem_type="single_label_classification",
            trust_remote_code=True,
            attn_implementation=training_args.attn_implementation,
        )
    elif "splicebert" in training_args.model_type:
        base_model = SpliceBertForSequenceClassification.from_pretrained(
            model_path,
            num_labels=2,
            problem_type="single_label_classification",
            trust_remote_code=True,
            attn_implementation=training_args.attn_implementation,
        )

    # Enable gradient checkpointing if specified
    if training_args.gradient_checkpointing:
        base_model.gradient_checkpointing_enable()
        print("✅ Gradient checkpointing enabled")

    # Freeze layers if specified
    freeze_model_layers(
        base_model,
        freeze_embeddings=training_args.freeze_embeddings,
        freeze_encoder_layers=training_args.freeze_encoder_layers
    )

    # Count trainable parameters
    total_params = sum(p.numel() for p in base_model.parameters())
    trainable_params = sum(p.numel() for p in base_model.parameters() if p.requires_grad)
    print(f"📊 Model parameters: {total_params:,} total, {trainable_params:,} trainable ({trainable_params/total_params*100:.2f}%)")

    # Wrap with sliding window model
    model = SlidingWindowModel(
        base_model,
        window_size=training_args.window_size,
        window_stride=training_args.window_stride,
        pooling_strategy=training_args.pooling_strategy
    )

    # Load datasets
    train_sequences, train_labels = load_fasta_data(
        os.path.join(data_args.data_path, data_args.data_train_path)
    )
    val_sequences, val_labels = load_fasta_data(
        os.path.join(data_args.data_path, data_args.data_val_path)
    )

    train_dataset = SlidingWindowDataset(
        train_sequences, train_labels, tokenizer,
        window_size=training_args.window_size,
        window_stride=training_args.window_stride,
        max_sequence_length=training_args.max_sequence_length,
        pooling_strategy=training_args.pooling_strategy
    )

    val_dataset = SlidingWindowDataset(
        val_sequences, val_labels, tokenizer,
        window_size=training_args.window_size,
        window_stride=training_args.window_stride,
        max_sequence_length=training_args.max_sequence_length,
        pooling_strategy=training_args.pooling_strategy
    )

    # Initialize trainer
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=train_dataset,
        eval_dataset=val_dataset,
        compute_metrics=compute_metrics,
        callbacks=[EarlyStoppingCallback(early_stopping_patience=training_args.patience)],
    )

    # Train
    print("🚀 Starting training...")
    trainer.train()

    # Save model
    if training_args.save_model:
        print("💾 Saving model...")
        trainer.save_model()
        tokenizer.save_pretrained(training_args.output_dir)

    # Evaluate
    print("📊 Evaluating...")
    eval_results = trainer.evaluate()
    print("Evaluation results:")
    for key, value in eval_results.items():
        print(f"  {key}: {value:.4f}")

    print("✅ Training completed!")

if __name__ == "__main__":
    main()
