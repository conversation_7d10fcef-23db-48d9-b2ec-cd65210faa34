#!/usr/bin/env python3
"""
Simple analysis of 565_hum_label.fa dataset
"""

import os
import numpy as np
from collections import Counter

def load_fasta_with_labels(fasta_path):
    """Load FASTA file with labels in header."""
    sequences = []
    labels = []
    sequence_ids = []
    
    with open(fasta_path, 'r') as f:
        lines = f.readlines()
    
    i = 0
    seq_id = 0
    while i < len(lines):
        line = lines[i].strip()
        if line.startswith('label='):
            # Extract label
            label = int(line.split('=')[1])
            labels.append(label)
            
            # Get sequence (next line)
            if i + 1 < len(lines):
                sequence = lines[i + 1].strip()
                sequences.append(sequence)
                sequence_ids.append(f"seq_{seq_id}")
                seq_id += 1
            i += 2
        else:
            i += 1
    
    return sequences, labels, sequence_ids

def analyze_dataset(sequences, labels):
    """Analyze dataset statistics."""
    print("🧬 Dataset Analysis for 565_hum_label.fa")
    print("=" * 60)
    
    # Basic statistics
    total_sequences = len(sequences)
    label_counts = Counter(labels)
    
    print(f"📊 Basic Statistics:")
    print(f"   Total sequences: {total_sequences:,}")
    print(f"   Label distribution:")
    for label, count in sorted(label_counts.items()):
        percentage = count / total_sequences * 100
        print(f"     Label {label}: {count:,} ({percentage:.1f}%)")
    
    # Sequence length analysis
    seq_lengths = [len(seq) for seq in sequences]
    
    print(f"\n📏 Sequence Length Statistics:")
    print(f"   Min length: {min(seq_lengths):,} bp")
    print(f"   Max length: {max(seq_lengths):,} bp")
    print(f"   Mean length: {np.mean(seq_lengths):.1f} bp")
    print(f"   Median length: {np.median(seq_lengths):.1f} bp")
    print(f"   Std deviation: {np.std(seq_lengths):.1f} bp")
    
    # Length percentiles
    percentiles = [25, 50, 75, 90, 95, 99]
    print(f"\n📈 Length Percentiles:")
    for p in percentiles:
        value = np.percentile(seq_lengths, p)
        print(f"   {p}th percentile: {value:.0f} bp")
    
    # Analyze sequences > 1024 (need sliding window)
    long_sequences = [length for length in seq_lengths if length > 1024]
    very_long_sequences = [length for length in seq_lengths if length > 9216]
    
    print(f"\n🔍 Sliding Window Analysis:")
    print(f"   Sequences > 1024 bp: {len(long_sequences):,} ({len(long_sequences)/total_sequences*100:.1f}%)")
    print(f"   Sequences > 9216 bp: {len(very_long_sequences):,} ({len(very_long_sequences)/total_sequences*100:.1f}%)")
    
    if long_sequences:
        print(f"   Mean length of long sequences: {np.mean(long_sequences):.1f} bp")
        print(f"   Max windows needed (1024/512): {max(long_sequences)//512 + 1}")
    
    # Nucleotide composition
    print(f"\n🧬 Nucleotide Composition Analysis:")
    all_nucleotides = ''.join(sequences)
    nucleotide_counts = Counter(all_nucleotides.upper())
    total_nucleotides = sum(nucleotide_counts.values())
    
    for nucleotide in ['A', 'T', 'G', 'C']:
        count = nucleotide_counts.get(nucleotide, 0)
        percentage = count / total_nucleotides * 100
        print(f"   {nucleotide}: {count:,} ({percentage:.2f}%)")
    
    # Check for non-standard nucleotides
    standard_nucleotides = set(['A', 'T', 'G', 'C'])
    non_standard = set(nucleotide_counts.keys()) - standard_nucleotides
    if non_standard:
        print(f"   Non-standard nucleotides: {non_standard}")
    
    return {
        'total_sequences': total_sequences,
        'label_counts': label_counts,
        'seq_lengths': seq_lengths,
        'nucleotide_counts': nucleotide_counts
    }

def main():
    """Main analysis function."""
    fasta_path = 'data/565_hum_label.fa'
    
    if not os.path.exists(fasta_path):
        print(f"❌ Error: File not found: {fasta_path}")
        return
    
    # Load data
    print("Loading dataset...")
    sequences, labels, sequence_ids = load_fasta_with_labels(fasta_path)
    
    # Analyze
    stats = analyze_dataset(sequences, labels)
    
    # Save summary
    output_dir = 'analysis_results'
    os.makedirs(output_dir, exist_ok=True)
    
    with open(os.path.join(output_dir, 'dataset_summary.txt'), 'w') as f:
        f.write("565_hum_label.fa Dataset Summary\n")
        f.write("=" * 40 + "\n\n")
        f.write(f"Total sequences: {stats['total_sequences']:,}\n")
        f.write(f"Label distribution: {dict(stats['label_counts'])}\n")
        f.write(f"Sequence length range: {min(stats['seq_lengths'])}-{max(stats['seq_lengths'])} bp\n")
        f.write(f"Mean length: {np.mean(stats['seq_lengths']):.1f} bp\n")
        f.write(f"Sequences > 1024 bp: {sum(1 for l in stats['seq_lengths'] if l > 1024)}\n")
        f.write(f"Sequences > 9216 bp: {sum(1 for l in stats['seq_lengths'] if l > 9216)}\n")
    
    print(f"\n✅ Analysis completed! Results saved to: {output_dir}/")

if __name__ == "__main__":
    main()
