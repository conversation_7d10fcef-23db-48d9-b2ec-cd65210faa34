{"best_global_step": 200, "best_metric": 0.0, "best_model_checkpoint": null, "epoch": 1.0, "eval_steps": 200, "global_step": 204, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.09803921568627451, "grad_norm": NaN, "learning_rate": 2e-05, "loss": 884.389, "step": 20}, {"epoch": 0.19607843137254902, "grad_norm": NaN, "learning_rate": 4e-05, "loss": 0.0, "step": 40}, {"epoch": 0.29411764705882354, "grad_norm": NaN, "learning_rate": 4.675324675324675e-05, "loss": 0.0, "step": 60}, {"epoch": 0.39215686274509803, "grad_norm": NaN, "learning_rate": 4.025974025974026e-05, "loss": 0.0, "step": 80}, {"epoch": 0.49019607843137253, "grad_norm": NaN, "learning_rate": 3.376623376623377e-05, "loss": 0.0, "step": 100}, {"epoch": 0.5882352941176471, "grad_norm": NaN, "learning_rate": 2.7272727272727273e-05, "loss": 0.0, "step": 120}, {"epoch": 0.6862745098039216, "grad_norm": NaN, "learning_rate": 2.077922077922078e-05, "loss": 0.0, "step": 140}, {"epoch": 0.7843137254901961, "grad_norm": NaN, "learning_rate": 1.4285714285714285e-05, "loss": 0.0, "step": 160}, {"epoch": 0.8823529411764706, "grad_norm": NaN, "learning_rate": 7.792207792207792e-06, "loss": 0.0, "step": 180}, {"epoch": 0.9803921568627451, "grad_norm": NaN, "learning_rate": 1.2987012987012988e-06, "loss": 0.0, "step": 200}, {"epoch": 0.9803921568627451, "eval_accuracy": 0.5, "eval_auc": 0.0, "eval_f1": 0.0, "eval_loss": NaN, "eval_precision": 0.0, "eval_recall": 0.0, "eval_runtime": 42.1487, "eval_samples_per_second": 4.793, "eval_steps_per_second": 4.793, "eval_unique_labels": 2, "eval_unique_predictions": 1, "step": 200}], "logging_steps": 20, "max_steps": 204, "num_input_tokens_seen": 0, "num_train_epochs": 1, "save_steps": 500, "stateful_callbacks": {"EarlyStoppingCallback": {"args": {"early_stopping_patience": 10, "early_stopping_threshold": 0.001}, "attributes": {"early_stopping_patience_counter": 0}}, "TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": true}, "attributes": {}}}, "total_flos": 0.0, "train_batch_size": 1, "trial_name": null, "trial_params": null}