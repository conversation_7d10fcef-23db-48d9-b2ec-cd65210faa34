#!/usr/bin/env python3
"""
Example script for running full parameter fine-tuning
Demonstrates different configurations and use cases
"""

import os
from run_full_finetune import run_full_finetune, run_multiple_models

def example_basic_full_finetune():
    """Basic full parameter fine-tuning example."""
    print("🧬 Example 1: Basic Full Parameter Fine-tuning")
    print("=" * 60)
    
    success = run_full_finetune(
        model_type="splicebert-ms1024",
        data_path="data",
        output_dir="outputs/ft_full/basic",
        num_train_epochs=3,
        learning_rate=2e-5,
        batch_size=4,
        gradient_accumulation_steps=4,
        run_name="basic_full_ft_example"
    )
    
    return success

def example_memory_efficient_finetune():
    """Memory-efficient full fine-tuning with layer freezing."""
    print("\n🧬 Example 2: Memory-Efficient Full Fine-tuning")
    print("=" * 60)
    
    success = run_full_finetune(
        model_type="splicebert-ms1024",
        data_path="data",
        output_dir="outputs/ft_full/memory_efficient",
        num_train_epochs=5,
        learning_rate=1e-5,  # Lower learning rate for stability
        batch_size=2,        # Smaller batch size
        gradient_accumulation_steps=8,  # Compensate with more accumulation
        freeze_embeddings=True,         # Freeze embeddings to save memory
        freeze_encoder_layers=6,        # Freeze first 6 encoder layers
        gradient_checkpointing=True,    # Enable gradient checkpointing
        run_name="memory_efficient_full_ft"
    )
    
    return success

def example_aggressive_finetune():
    """Aggressive full fine-tuning with higher learning rate."""
    print("\n🧬 Example 3: Aggressive Full Fine-tuning")
    print("=" * 60)
    
    success = run_full_finetune(
        model_type="rna-fm",
        data_path="data",
        output_dir="outputs/ft_full/aggressive",
        num_train_epochs=2,
        learning_rate=5e-5,  # Higher learning rate
        batch_size=8,        # Larger batch size if memory allows
        gradient_accumulation_steps=2,
        warmup_ratio=0.2,    # More warmup
        weight_decay=0.05,   # Higher weight decay
        freeze_embeddings=False,
        freeze_encoder_layers=0,  # No freezing
        gradient_checkpointing=True,
        run_name="aggressive_full_ft"
    )
    
    return success

def example_multiple_models():
    """Train multiple models with full fine-tuning."""
    print("\n🧬 Example 4: Multiple Models Full Fine-tuning")
    print("=" * 60)
    
    results = run_multiple_models(
        models=["rna-fm", "splicebert-ms1024"],
        data_path="data",
        output_dir="outputs/ft_full/multi_model",
        num_train_epochs=3,
        learning_rate=2e-5,
        batch_size=4,
        gradient_accumulation_steps=4,
        freeze_embeddings=False,
        freeze_encoder_layers=0,
        gradient_checkpointing=True,
        patience=5,
        seed=42
    )
    
    return results

def example_custom_window_config():
    """Full fine-tuning with custom sliding window configuration."""
    print("\n🧬 Example 5: Custom Window Configuration")
    print("=" * 60)
    
    success = run_full_finetune(
        model_type="splicebert-ms1024",
        data_path="data",
        output_dir="outputs/ft_full/custom_window",
        num_train_epochs=4,
        learning_rate=3e-5,
        batch_size=4,
        gradient_accumulation_steps=4,
        window_size=512,           # Smaller window size
        window_stride=256,         # Smaller stride for more overlap
        max_sequence_length=4096,  # Shorter max length
        pooling_strategy="max",    # Use max pooling instead of mean
        run_name="custom_window_full_ft"
    )
    
    return success

def compare_configurations():
    """Compare different full fine-tuning configurations."""
    print("\n🧬 Configuration Comparison")
    print("=" * 60)
    
    configurations = [
        {
            "name": "Conservative",
            "params": {
                "learning_rate": 1e-5,
                "freeze_embeddings": True,
                "freeze_encoder_layers": 4,
                "num_train_epochs": 5,
                "batch_size": 2,
                "gradient_accumulation_steps": 8
            }
        },
        {
            "name": "Balanced",
            "params": {
                "learning_rate": 2e-5,
                "freeze_embeddings": False,
                "freeze_encoder_layers": 2,
                "num_train_epochs": 3,
                "batch_size": 4,
                "gradient_accumulation_steps": 4
            }
        },
        {
            "name": "Aggressive",
            "params": {
                "learning_rate": 5e-5,
                "freeze_embeddings": False,
                "freeze_encoder_layers": 0,
                "num_train_epochs": 2,
                "batch_size": 8,
                "gradient_accumulation_steps": 2
            }
        }
    ]
    
    results = {}
    
    for config in configurations:
        print(f"\n--- {config['name']} Configuration ---")
        
        success = run_full_finetune(
            model_type="splicebert-ms1024",
            data_path="data",
            output_dir=f"outputs/ft_full/compare_{config['name'].lower()}",
            gradient_checkpointing=True,
            run_name=f"compare_{config['name'].lower()}_full_ft",
            **config['params']
        )
        
        results[config['name']] = success
    
    print(f"\n--- Comparison Results ---")
    for name, success in results.items():
        status = "✅ Success" if success else "❌ Failed"
        print(f"{name}: {status}")
    
    return results

def main():
    """Run all examples."""
    print("🧬 Full Parameter Fine-tuning Examples")
    print("=" * 80)
    
    # Check if data directory exists
    if not os.path.exists("data"):
        print("❌ Data directory not found. Please ensure 'data' directory exists with train.fa and val.fa files.")
        return
    
    # Run examples
    examples = [
        ("Basic Full Fine-tuning", example_basic_full_finetune),
        ("Memory-Efficient Fine-tuning", example_memory_efficient_finetune),
        ("Aggressive Fine-tuning", example_aggressive_finetune),
        ("Multiple Models", example_multiple_models),
        ("Custom Window Config", example_custom_window_config),
    ]
    
    results = {}
    
    for name, func in examples:
        try:
            print(f"\n{'='*80}")
            print(f"Running: {name}")
            print(f"{'='*80}")
            
            result = func()
            results[name] = result
            
            if isinstance(result, bool):
                status = "✅ Success" if result else "❌ Failed"
            else:
                status = "✅ Completed"
            
            print(f"\n{name}: {status}")
            
        except Exception as e:
            print(f"\n❌ {name} failed with error: {e}")
            results[name] = False
    
    # Final summary
    print(f"\n{'='*80}")
    print("Final Summary")
    print(f"{'='*80}")
    
    for name, result in results.items():
        if isinstance(result, bool):
            status = "✅ Success" if result else "❌ Failed"
        elif isinstance(result, dict):
            success_count = sum(1 for v in result.values() if v)
            total_count = len(result)
            status = f"✅ {success_count}/{total_count} models succeeded"
        else:
            status = "✅ Completed"
        
        print(f"{name}: {status}")

if __name__ == "__main__":
    main()
