#!/usr/bin/env python3
"""
Final evaluation script for lncRNA function prediction
Evaluates trained models on the final test set and generates comparison report
"""

import os
import sys
import json
import torch
import numpy as np
import pandas as pd
from sklearn.metrics import accuracy_score, precision_recall_fscore_support, roc_auc_score, confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import argparse

# Add paths
sys.path.append('.')
sys.path.append('downstream')

# Import required modules
try:
    from tokenizer.tokenization_opensource import OpenRnaLMTokenizer
    TOKENIZER_AVAILABLE = True
except ImportError:
    TOKENIZER_AVAILABLE = False

try:
    from model.rnafm.modeling_rnafm import RnaFmForSequenceClassification
    RNAFM_AVAILABLE = True
except ImportError:
    RNAFM_AVAILABLE = False

try:
    from model.splicebert.modeling_splicebert import SpliceBertForSequenceClassification
    SPLICEBERT_AVAILABLE = True
except ImportError:
    SPLICEBERT_AVAILABLE = False

try:
    from peft import PeftModel
    PEFT_AVAILABLE = True
except ImportError:
    PEFT_AVAILABLE = False

from train_lncrna_function_lora import (
    load_fasta_data, create_sliding_windows, SlidingWindowDataset, 
    custom_data_collator, SlidingWindowModel
)

def load_best_model_from_cv(model_type, cv_results_path, cv_output_dir):
    """Load the best model from cross-validation results."""
    # Load CV results
    cv_results = pd.read_csv(cv_results_path)
    
    # Filter successful results for this model type
    model_results = cv_results[
        (cv_results['model_type'] == model_type) & 
        (cv_results['status'] == 'success')
    ]
    
    if len(model_results) == 0:
        raise ValueError(f"No successful results found for {model_type}")
    
    # Find best fold based on F1 score
    best_fold = model_results.loc[model_results['eval_f1'].idxmax()]
    best_fold_idx = int(best_fold['fold'])
    
    print(f"Best {model_type} model: Fold {best_fold_idx} (F1: {best_fold['eval_f1']:.4f})")
    
    # Load the best model
    model_path = os.path.join(cv_output_dir, model_type, f"fold_{best_fold_idx}")
    
    return model_path, best_fold

def load_model_for_inference(model_type, model_path, num_labels=2):
    """Load model for inference."""
    if not TOKENIZER_AVAILABLE:
        raise ImportError("OpenRnaLMTokenizer not available")
    
    # Load tokenizer
    tokenizer = OpenRnaLMTokenizer.from_pretrained(
        model_path,
        model_max_length=1024,
        padding_side="right",
        use_fast=True,
        trust_remote_code=True,
    )
    
    # Load base model
    if model_type == 'rna-fm':
        if not RNAFM_AVAILABLE:
            raise ImportError("RnaFm model not available")
        base_model = RnaFmForSequenceClassification.from_pretrained(
            model_path,
            num_labels=num_labels,
            problem_type="single_label_classification",
            trust_remote_code=True,
        )
    elif 'splicebert' in model_type:
        if not SPLICEBERT_AVAILABLE:
            raise ImportError("SpliceBert model not available")
        base_model = SpliceBertForSequenceClassification.from_pretrained(
            model_path,
            num_labels=num_labels,
            problem_type="single_label_classification",
            trust_remote_code=True,
        )
    else:
        raise ValueError(f"Unsupported model type: {model_type}")
    
    # Check if it's a PEFT model
    if PEFT_AVAILABLE and os.path.exists(os.path.join(model_path, "adapter_config.json")):
        print(f"Loading PEFT model from {model_path}")
        base_model = PeftModel.from_pretrained(base_model, model_path)
    
    # Wrap with sliding window model
    model = SlidingWindowModel(base_model, pooling_strategy='mean')
    model.eval()
    
    return model, tokenizer

def evaluate_model(model, tokenizer, test_data_path, device='cuda', batch_size=4):
    """Evaluate model on test data."""
    # Create test dataset
    class TestArgs:
        def __init__(self):
            self.window_size = 1024
            self.window_stride = 512
            self.max_sequence_length = 9216
            self.model_max_length = 1024
    
    test_args = TestArgs()
    test_dataset = SlidingWindowDataset(
        data_path=test_data_path,
        args=test_args,
        tokenizer=tokenizer
    )
    
    # Create data loader
    from torch.utils.data import DataLoader
    test_loader = DataLoader(
        test_dataset,
        batch_size=batch_size,
        shuffle=False,
        collate_fn=custom_data_collator
    )
    
    # Evaluation
    model.to(device)
    model.eval()
    
    all_predictions = []
    all_probabilities = []
    all_labels = []
    
    with torch.no_grad():
        for batch in test_loader:
            # Move batch to device
            input_ids = batch['input_ids'].to(device)
            attention_mask = batch['attention_mask'].to(device)
            window_mask = batch['window_mask'].to(device)
            labels = batch['labels'].to(device)
            
            # Forward pass
            outputs = model(
                input_ids=input_ids,
                attention_mask=attention_mask,
                window_mask=window_mask
            )
            
            logits = outputs['logits']
            probabilities = torch.softmax(logits, dim=-1)
            predictions = torch.argmax(logits, dim=-1)
            
            # Collect results
            all_predictions.extend(predictions.cpu().numpy())
            all_probabilities.extend(probabilities.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
    
    return np.array(all_predictions), np.array(all_probabilities), np.array(all_labels)

def calculate_metrics(predictions, probabilities, labels):
    """Calculate evaluation metrics."""
    # Basic metrics
    accuracy = accuracy_score(labels, predictions)
    precision, recall, f1, _ = precision_recall_fscore_support(labels, predictions, average='binary')
    
    # ROC AUC
    try:
        auc = roc_auc_score(labels, probabilities[:, 1])
    except:
        auc = 0.0
    
    # Confusion matrix
    cm = confusion_matrix(labels, predictions)
    
    return {
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1': f1,
        'auc': auc,
        'confusion_matrix': cm
    }

def plot_confusion_matrix(cm, model_name, output_dir):
    """Plot confusion matrix."""
    plt.figure(figsize=(8, 6))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                xticklabels=['Non-functional', 'Functional'],
                yticklabels=['Non-functional', 'Functional'])
    plt.title(f'Confusion Matrix - {model_name}')
    plt.ylabel('True Label')
    plt.xlabel('Predicted Label')
    
    plot_path = os.path.join(output_dir, f'{model_name}_confusion_matrix.png')
    plt.savefig(plot_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    return plot_path

def generate_comparison_report(results, output_dir):
    """Generate detailed comparison report."""
    report_path = os.path.join(output_dir, 'final_evaluation_report.txt')
    
    with open(report_path, 'w') as f:
        f.write("lncRNA Function Prediction - Final Evaluation Report\n")
        f.write("=" * 60 + "\n")
        f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        # Individual model results
        for model_name, metrics in results.items():
            f.write(f"{model_name} Results:\n")
            f.write("-" * 30 + "\n")
            f.write(f"  Accuracy:  {metrics['accuracy']:.4f}\n")
            f.write(f"  Precision: {metrics['precision']:.4f}\n")
            f.write(f"  Recall:    {metrics['recall']:.4f}\n")
            f.write(f"  F1 Score:  {metrics['f1']:.4f}\n")
            f.write(f"  AUC:       {metrics['auc']:.4f}\n")
            f.write(f"  Confusion Matrix:\n")
            cm = metrics['confusion_matrix']
            f.write(f"    TN: {cm[0,0]:4d}  FP: {cm[0,1]:4d}\n")
            f.write(f"    FN: {cm[1,0]:4d}  TP: {cm[1,1]:4d}\n")
            f.write("\n")
        
        # Comparison
        if len(results) > 1:
            f.write("Model Comparison:\n")
            f.write("-" * 30 + "\n")
            
            metrics_to_compare = ['accuracy', 'precision', 'recall', 'f1', 'auc']
            for metric in metrics_to_compare:
                f.write(f"{metric.capitalize()}:\n")
                for model_name in results.keys():
                    f.write(f"  {model_name}: {results[model_name][metric]:.4f}\n")
                
                # Find best model for this metric
                best_model = max(results.keys(), key=lambda x: results[x][metric])
                f.write(f"  Best: {best_model}\n\n")
    
    print(f"📋 Final evaluation report saved to: {report_path}")
    return report_path

def main():
    parser = argparse.ArgumentParser(description='Evaluate models on final test set')
    parser.add_argument('--test_data', default='data/cv_splits/final_test.fa', help='Final test data')
    parser.add_argument('--cv_results', default='outputs/cv_lora/cv_results.csv', help='CV results file')
    parser.add_argument('--cv_output_dir', default='outputs/cv_lora', help='CV output directory')
    parser.add_argument('--output_dir', default='outputs/final_evaluation', help='Output directory')
    parser.add_argument('--models', nargs='+', default=['rna-fm', 'splicebert-ms1024'], help='Models to evaluate')
    parser.add_argument('--batch_size', type=int, default=4, help='Batch size for evaluation')
    parser.add_argument('--device', default='cuda', help='Device to use')
    
    args = parser.parse_args()
    
    print("🧬 lncRNA Function Prediction - Final Evaluation")
    print("=" * 60)
    print(f"Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Check if test data exists
    if not os.path.exists(args.test_data):
        print(f"❌ Test data not found: {args.test_data}")
        return
    
    # Check if CV results exist
    if not os.path.exists(args.cv_results):
        print(f"❌ CV results not found: {args.cv_results}")
        return
    
    # Evaluate each model
    results = {}
    
    for model_type in args.models:
        print(f"\n🔄 Evaluating {model_type}")
        print("-" * 40)
        
        try:
            # Load best model from CV
            model_path, best_fold_info = load_best_model_from_cv(
                model_type, args.cv_results, args.cv_output_dir
            )
            
            # Load model for inference
            model, tokenizer = load_model_for_inference(model_type, model_path)
            
            # Evaluate on test set
            predictions, probabilities, labels = evaluate_model(
                model, tokenizer, args.test_data, args.device, args.batch_size
            )
            
            # Calculate metrics
            metrics = calculate_metrics(predictions, probabilities, labels)
            
            # Plot confusion matrix
            cm_path = plot_confusion_matrix(
                metrics['confusion_matrix'], model_type, args.output_dir
            )
            
            results[model_type] = metrics
            
            print(f"✅ {model_type} evaluation completed")
            print(f"   Accuracy: {metrics['accuracy']:.4f}")
            print(f"   F1 Score: {metrics['f1']:.4f}")
            print(f"   AUC: {metrics['auc']:.4f}")
            
        except Exception as e:
            print(f"❌ Error evaluating {model_type}: {e}")
            continue
    
    # Generate comparison report
    if results:
        report_path = generate_comparison_report(results, args.output_dir)
        
        # Save results to CSV
        results_df = pd.DataFrame(results).T
        results_csv = os.path.join(args.output_dir, 'final_results.csv')
        results_df.to_csv(results_csv)
        print(f"📄 Results saved to: {results_csv}")
    
    print(f"\n🎉 Final evaluation completed!")
    print(f"End time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Results saved in: {args.output_dir}")

if __name__ == "__main__":
    main()
