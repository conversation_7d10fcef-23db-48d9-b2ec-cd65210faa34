# lncRNA Function Prediction with LoRA Fine-tuning

This repository implements a comprehensive pipeline for lncRNA function prediction using LoRA (Low-Rank Adaptation) fine-tuning on RNA-FM and SpliceBERT models.

## 🎯 Experiment Overview

### Objective
Compare the performance of RNA-FM and SpliceBERT models for binary classification of lncRNA functionality using parameter-efficient LoRA fine-tuning.

### Key Features
- **LoRA Fine-tuning**: Parameter-efficient training (only 1-5% of parameters)
- **Sliding Window Processing**: Handles long sequences (up to 9216 bp) with 1024 bp windows
- **Sequence-level Aggregation**: Mean pooling of window embeddings for final prediction
- **5-fold Cross-validation**: Robust model evaluation and selection
- **Independent Test Set**: 10% held-out data for unbiased final evaluation

### Dataset
- **File**: `data/565_hum_label.fa`
- **Size**: 1,130 sequences (565 functional + 565 non-functional)
- **Format**: Balanced binary classification
- **Average Length**: ~2,000 nucleotides

## 📁 File Structure

```
├── data/
│   └── 565_hum_label.fa              # Main dataset
├── data_split_cv.py                  # Data splitting and CV preparation
├── train_lncrna_function_lora.py     # LoRA fine-tuning script
├── run_cv_training.py                # Cross-validation training pipeline
├── evaluate_final_test.py            # Final evaluation script
├── test_environment.py               # Environment testing
├── cv_config.json                    # Training configuration
├── run_lncrna_lora_experiment.sh     # Main execution script
└── README_lncRNA_LoRA_Experiment.md  # This file
```

## 🚀 Quick Start

### 1. Environment Setup

First, test your environment:
```bash
python test_environment.py
```

Install missing dependencies if needed:
```bash
pip install peft transformers torch pandas scikit-learn matplotlib seaborn
```

### 2. Run Complete Pipeline

Execute the full experiment:
```bash
bash run_lncrna_lora_experiment.sh
```

This will automatically:
1. Split data (10% test, 90% for 5-fold CV)
2. Train models with LoRA fine-tuning
3. Evaluate on final test set
4. Generate comparison reports

### 3. Manual Step-by-Step Execution

If you prefer manual control:

```bash
# Step 1: Data splitting
python data_split_cv.py --input data/565_hum_label.fa --output_dir data/cv_splits

# Step 2: Cross-validation training
python run_cv_training.py --data_dir data/cv_splits --output_dir outputs/cv_lora

# Step 3: Final evaluation
python evaluate_final_test.py --test_data data/cv_splits/final_test.fa --cv_results outputs/cv_lora/cv_results.csv --output_dir outputs/final_evaluation
```

## ⚙️ Configuration

### LoRA Parameters (`cv_config.json`)
```json
{
  "lora_r": 16,              # Low-rank dimension
  "lora_alpha": 32,          # Scaling factor
  "lora_dropout": 0.1,       # Dropout rate
  "lora_target_modules": "query,value,key,dense"
}
```

### Sliding Window Settings
```json
{
  "window_size": 1024,       # Window size (nucleotides)
  "window_stride": 512,      # Sliding stride (50% overlap)
  "max_sequence_length": 9216, # Truncation length
  "pooling_strategy": "mean" # Sequence-level aggregation
}
```

### Training Parameters
```json
{
  "num_epochs": 20,
  "batch_size": 4,
  "learning_rate": 1e-4,
  "patience": 8,             # Early stopping patience
  "gradient_accumulation_steps": 4
}
```

## 📊 Expected Results

### Performance Comparison
| Model | Accuracy | F1 Score | Precision | Recall | AUC |
|-------|----------|----------|-----------|--------|-----|
| RNA-FM | 78-82% | 0.78-0.82 | 0.76-0.84 | 0.75-0.85 | 0.85-0.90 |
| SpliceBERT | 76-80% | 0.76-0.80 | 0.74-0.82 | 0.73-0.83 | 0.83-0.88 |

### Training Efficiency
- **Parameters**: ~2M trainable (vs 100M+ full fine-tuning)
- **Training Time**: ~2 hours/fold (vs 4+ hours full fine-tuning)
- **Memory Usage**: ~6GB (vs 12GB+ full fine-tuning)

## 📈 Output Files

### Cross-Validation Results
- `outputs/cv_lora/cv_results.csv`: Detailed CV metrics
- `outputs/cv_lora/cv_summary_report.txt`: Summary statistics
- `outputs/cv_lora/{model}/fold_{i}/`: Trained model checkpoints

### Final Evaluation
- `outputs/final_evaluation/final_results.csv`: Test set performance
- `outputs/final_evaluation/final_evaluation_report.txt`: Detailed comparison
- `outputs/final_evaluation/{model}_confusion_matrix.png`: Confusion matrices

## 🔧 Technical Details

### Sliding Window Processing
1. **Sequence Truncation**: Long sequences (>9216 bp) are truncated
2. **Window Creation**: 1024 bp windows with 512 bp stride
3. **Tokenization**: Each window tokenized independently
4. **Encoding**: Base model encodes each window
5. **Aggregation**: Mean pooling across window embeddings
6. **Classification**: Final linear layer for binary prediction

### LoRA Implementation
- **Target Modules**: Query, Value, Key, Dense layers in attention
- **Rank**: 16 (balance between performance and efficiency)
- **Alpha**: 32 (scaling factor for LoRA weights)
- **Dropout**: 0.1 (regularization)

### Cross-Validation Strategy
- **Stratified 5-fold**: Maintains label balance across folds
- **Independent Test**: 10% held-out for final evaluation
- **Model Selection**: Best fold based on validation F1 score
- **Early Stopping**: Prevents overfitting with patience=8

## 🐛 Troubleshooting

### Common Issues

1. **CUDA Out of Memory**
   - Reduce batch size in `cv_config.json`
   - Use gradient accumulation to maintain effective batch size

2. **Model Loading Errors**
   - Check model checkpoint paths in `cv_config.json`
   - Ensure model files are properly downloaded

3. **Data Format Issues**
   - Verify `565_hum_label.fa` format (label lines followed by sequences)
   - Check for empty lines or malformed entries

4. **PEFT Library Issues**
   - Install latest version: `pip install peft`
   - Check compatibility with transformers version

### Performance Optimization

1. **Faster Training**
   - Use mixed precision (fp16=True)
   - Increase batch size if memory allows
   - Use multiple GPUs with DataParallel

2. **Better Results**
   - Tune LoRA hyperparameters (rank, alpha)
   - Experiment with different target modules
   - Adjust learning rate and warmup steps

## 📚 References

- **LoRA**: [LoRA: Low-Rank Adaptation of Large Language Models](https://arxiv.org/abs/2106.09685)
- **RNA-FM**: [Interpretable RNA Foundation Model from Unannotated Data](https://arxiv.org/abs/2204.00300)
- **SpliceBERT**: [Self-supervised learning on millions of primary RNA sequences](https://www.biorxiv.org/content/10.1101/2023.01.31.526427v1)

## 🤝 Contributing

Feel free to submit issues, feature requests, or pull requests to improve this pipeline.

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.
