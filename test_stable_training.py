#!/usr/bin/env python3
"""
Test script to verify stable training with improved parameters
"""

import os
import sys
import torch
import numpy as np

# Add paths
current_path = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_path)

def test_quick_training():
    """Test a few training steps to verify stability."""
    
    print("🧪 Testing stable frozen training...")
    
    try:
        # Run a quick test with 1 fold, 1 epoch, very small dataset
        cmd = """
python downstream/train_lncrna_function_frozen_cv.py \
    --model_type rnafm \
    --data_path data/567_hum_label.fa \
    --output_dir test_stable_output \
    --n_folds 1 \
    --num_train_epochs 1 \
    --learning_rate 5e-5 \
    --max_grad_norm 1.0 \
    --per_device_train_batch_size 2 \
    --eval_steps 10 \
    --logging_steps 5 \
    --freeze_pretrained \
    --freeze_embeddings \
    --freeze_encoder \
    --trainable_layers 0
"""
        
        print("🚀 Running quick stability test...")
        print("Command:", cmd.replace('\n', ' ').replace('\\', ''))
        
        import subprocess
        result = subprocess.run(cmd.split(), capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✅ Quick training test passed!")
            
            # Check for NaN in output
            if "nan" in result.stdout.lower():
                print("⚠️  Warning: NaN values detected in output")
                return False
            else:
                print("✅ No NaN values detected")
                return True
        else:
            print("❌ Quick training test failed!")
            print("Error:", result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("⏰ Test timed out (5 minutes)")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Main test function."""
    
    print("🧪 Stable Training Test")
    print("=" * 40)
    
    # Test stable training
    success = test_quick_training()
    
    if success:
        print("\n🎉 Stable training test passed!")
        print("✅ Ready to run full frozen training with improved parameters")
        print("\nRecommended command:")
        print("bash scripts/lncrna_function_cv/rna_fm_frozen_cv.sh")
    else:
        print("\n❌ Stable training test failed!")
        print("Please check the configuration and try again")

if __name__ == "__main__":
    main()
