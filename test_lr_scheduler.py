#!/usr/bin/env python3
"""
Test script to debug learning rate scheduler issues.
"""

import torch
import numpy as np
from transformers import get_linear_schedule_with_warmup
from torch.optim import Adam<PERSON>

def test_lr_scheduler():
    """Test learning rate scheduler behavior."""
    
    # Simulate training parameters
    num_training_samples = 816  # From the log
    batch_size = 1
    gradient_accumulation_steps = 16
    num_epochs = 20
    warmup_steps = 50
    learning_rate = 2e-5
    
    # Calculate total steps
    steps_per_epoch = num_training_samples // (batch_size * gradient_accumulation_steps)
    total_steps = steps_per_epoch * num_epochs
    
    print(f"Training configuration:")
    print(f"  num_training_samples: {num_training_samples}")
    print(f"  batch_size: {batch_size}")
    print(f"  gradient_accumulation_steps: {gradient_accumulation_steps}")
    print(f"  num_epochs: {num_epochs}")
    print(f"  steps_per_epoch: {steps_per_epoch}")
    print(f"  total_steps: {total_steps}")
    print(f"  warmup_steps: {warmup_steps}")
    print(f"  learning_rate: {learning_rate}")
    print()
    
    # Create a dummy model and optimizer
    model = torch.nn.Linear(10, 2)
    optimizer = AdamW(model.parameters(), lr=learning_rate)
    
    # Create scheduler
    scheduler = get_linear_schedule_with_warmup(
        optimizer,
        num_warmup_steps=warmup_steps,
        num_training_steps=total_steps
    )
    
    # Test learning rate progression
    lrs = []
    for step in range(min(200, total_steps)):  # Test first 200 steps
        current_lr = optimizer.param_groups[0]['lr']
        lrs.append(current_lr)
        
        if step < 10 or step % 20 == 0:
            print(f"Step {step:3d}: lr = {current_lr:.2e}")
        
        # Simulate training step
        optimizer.zero_grad()
        loss = torch.tensor(1.0, requires_grad=True)
        loss.backward()
        optimizer.step()
        scheduler.step()
    
    print(f"\nLearning rate progression:")
    print(f"  Initial LR: {lrs[0]:.2e}")
    print(f"  LR after warmup (step {warmup_steps}): {lrs[warmup_steps] if warmup_steps < len(lrs) else 'N/A'}")
    print(f"  Final LR: {lrs[-1]:.2e}")
    
    # Check if learning rate is ever 0
    zero_lr_steps = [i for i, lr in enumerate(lrs) if lr == 0.0]
    if zero_lr_steps:
        print(f"  WARNING: Learning rate is 0.0 at steps: {zero_lr_steps[:10]}...")
    else:
        print(f"  Good: Learning rate is never 0.0")

if __name__ == "__main__":
    test_lr_scheduler()
