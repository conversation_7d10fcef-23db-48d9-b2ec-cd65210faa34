#!/bin/bash

# lncRNA Function Prediction with LoRA Fine-tuning
# Complete pipeline: data splitting, cross-validation training, and final evaluation

set -e  # Exit on any error

echo "🧬 lncRNA Function Prediction - LoRA Fine-tuning Pipeline"
echo "=========================================================="
echo "Start time: $(date)"
echo ""

# Configuration
DATA_FILE="data/565_hum_label.fa"
CV_SPLITS_DIR="data/cv_splits"
CV_OUTPUT_DIR="outputs/cv_lora"
FINAL_OUTPUT_DIR="outputs/final_evaluation"
CONFIG_FILE="cv_config.json"
NUM_FOLDS=5
TEST_SIZE=0.1
SEED=42

# Check if input data exists
if [ ! -f "$DATA_FILE" ]; then
    echo "❌ Error: Input data file not found: $DATA_FILE"
    exit 1
fi

# Check if required scripts exist
REQUIRED_SCRIPTS=("data_split_cv.py" "train_lncrna_function_lora.py" "run_cv_training.py" "evaluate_final_test.py")
for script in "${REQUIRED_SCRIPTS[@]}"; do
    if [ ! -f "$script" ]; then
        echo "❌ Error: Required script not found: $script"
        exit 1
    fi
done

# Check if config file exists
if [ ! -f "$CONFIG_FILE" ]; then
    echo "❌ Error: Configuration file not found: $CONFIG_FILE"
    exit 1
fi

echo "✅ All required files found"
echo ""

# Step 1: Data splitting and cross-validation preparation
echo "📊 Step 1: Data Splitting and Cross-Validation Preparation"
echo "--------------------------------------------------------"
echo "Input file: $DATA_FILE"
echo "Output directory: $CV_SPLITS_DIR"
echo "Test size: $TEST_SIZE (10%)"
echo "CV folds: $NUM_FOLDS"
echo "Random seed: $SEED"
echo ""

python data_split_cv.py \
    --input "$DATA_FILE" \
    --output_dir "$CV_SPLITS_DIR" \
    --test_size $TEST_SIZE \
    --cv_folds $NUM_FOLDS \
    --seed $SEED

if [ $? -ne 0 ]; then
    echo "❌ Error: Data splitting failed"
    exit 1
fi

echo "✅ Data splitting completed"
echo ""

# Step 2: Cross-validation training
echo "🚀 Step 2: Cross-Validation Training"
echo "-----------------------------------"
echo "Data directory: $CV_SPLITS_DIR"
echo "Output directory: $CV_OUTPUT_DIR"
echo "Configuration: $CONFIG_FILE"
echo "Number of folds: $NUM_FOLDS"
echo ""

python run_cv_training.py \
    --data_dir "$CV_SPLITS_DIR" \
    --output_dir "$CV_OUTPUT_DIR" \
    --num_folds $NUM_FOLDS \
    --config_file "$CONFIG_FILE"

if [ $? -ne 0 ]; then
    echo "❌ Error: Cross-validation training failed"
    exit 1
fi

echo "✅ Cross-validation training completed"
echo ""

# Step 3: Final evaluation
echo "📈 Step 3: Final Evaluation"
echo "-------------------------"
echo "Test data: $CV_SPLITS_DIR/final_test.fa"
echo "CV results: $CV_OUTPUT_DIR/cv_results.csv"
echo "Output directory: $FINAL_OUTPUT_DIR"
echo ""

python evaluate_final_test.py \
    --test_data "$CV_SPLITS_DIR/final_test.fa" \
    --cv_results "$CV_OUTPUT_DIR/cv_results.csv" \
    --cv_output_dir "$CV_OUTPUT_DIR" \
    --output_dir "$FINAL_OUTPUT_DIR" \
    --models rna-fm splicebert-ms1024 \
    --batch_size 4

if [ $? -ne 0 ]; then
    echo "❌ Error: Final evaluation failed"
    exit 1
fi

echo "✅ Final evaluation completed"
echo ""

# Step 4: Generate summary
echo "📋 Step 4: Experiment Summary"
echo "----------------------------"

# Check if results files exist and display summary
if [ -f "$CV_OUTPUT_DIR/cv_summary_report.txt" ]; then
    echo "Cross-Validation Summary:"
    echo "========================"
    cat "$CV_OUTPUT_DIR/cv_summary_report.txt"
    echo ""
fi

if [ -f "$FINAL_OUTPUT_DIR/final_evaluation_report.txt" ]; then
    echo "Final Evaluation Summary:"
    echo "========================"
    cat "$FINAL_OUTPUT_DIR/final_evaluation_report.txt"
    echo ""
fi

# Display file locations
echo "📁 Output Files:"
echo "==============="
echo "Data splits: $CV_SPLITS_DIR/"
echo "CV results: $CV_OUTPUT_DIR/"
echo "Final evaluation: $FINAL_OUTPUT_DIR/"
echo ""

echo "🎉 lncRNA Function Prediction Pipeline Completed!"
echo "================================================="
echo "End time: $(date)"
echo ""

# Optional: Display disk usage
echo "💾 Disk Usage:"
echo "============="
du -sh "$CV_SPLITS_DIR" "$CV_OUTPUT_DIR" "$FINAL_OUTPUT_DIR" 2>/dev/null || echo "Could not calculate disk usage"
echo ""

echo "🔬 Experiment completed successfully!"
echo "Check the output directories for detailed results and trained models."
