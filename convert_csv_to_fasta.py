#!/usr/bin/env python3
"""
Convert CSV format lncRNA data to FASTA format
"""

import sys
import csv
import os

def csv_to_fasta(csv_file, fasta_file):
    """Convert CSV file to FASTA format"""
    
    # Increase CSV field size limit
    csv.field_size_limit(sys.maxsize)
    
    with open(csv_file, 'r') as f_in, open(fasta_file, 'w') as f_out:
        reader = csv.reader(f_in)
        header = next(reader)  # Skip header
        
        print(f"Converting {csv_file} to {fasta_file}...")
        
        count = 0
        for row in reader:
            if len(row) >= 2:
                sequence = row[0].upper().replace("U", "T")  # Convert to DNA
                label = row[1]
                
                # Create FASTA header with sequence ID and label
                seq_id = f"seq_{count:06d}"
                header_line = f">{seq_id} label={label} length={len(sequence)}"
                
                # Write FASTA entry
                f_out.write(header_line + "\n")
                
                # Write sequence in lines of 80 characters
                for i in range(0, len(sequence), 80):
                    f_out.write(sequence[i:i+80] + "\n")
                
                count += 1
                
                if count % 1000 == 0:
                    print(f"Processed {count} sequences...")
        
        print(f"✓ Converted {count} sequences to {fasta_file}")

def main():
    """Convert all CSV files to FASTA format"""
    
    # Define file mappings
    files_to_convert = [
        ('data/train.csv', 'data/train.fa'),
        ('data/val.csv', 'data/val.fa'),
        ('data/test.csv', 'data/test.fa')
    ]
    
    print("=" * 50)
    print("Converting lncRNA CSV files to FASTA format")
    print("=" * 50)
    
    for csv_file, fasta_file in files_to_convert:
        if os.path.exists(csv_file):
            csv_to_fasta(csv_file, fasta_file)
        else:
            print(f"Warning: {csv_file} not found, skipping...")
    
    print("\n" + "=" * 50)
    print("Conversion completed!")
    print("FASTA files created:")
    for _, fasta_file in files_to_convert:
        if os.path.exists(fasta_file):
            print(f"  ✓ {fasta_file}")
    print("=" * 50)

if __name__ == "__main__":
    main()
