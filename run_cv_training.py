#!/usr/bin/env python3
"""
Cross-validation training script for lncRNA function prediction
Runs 5-fold CV for both RNA-FM and SpliceBERT models with LoRA fine-tuning
"""

import os
import sys
import json
import subprocess
import argparse
from datetime import datetime
import pandas as pd
import numpy as np

def run_single_fold(model_type, fold_idx, data_dir, output_base_dir, model_path, config):
    """Run training for a single fold."""
    print(f"\n🔄 Training {model_type} - Fold {fold_idx}")
    print("-" * 50)
    
    # Prepare paths
    train_data = os.path.join(data_dir, f"fold_{fold_idx}_train.fa")
    val_data = os.path.join(data_dir, f"fold_{fold_idx}_val.fa")
    output_dir = os.path.join(output_base_dir, model_type, f"fold_{fold_idx}")
    
    # Check if data files exist
    if not os.path.exists(train_data) or not os.path.exists(val_data):
        print(f"❌ Data files not found for fold {fold_idx}")
        return None
    
    # Prepare training command
    cmd = [
        "python", "train_lncrna_function_lora.py",
        "--model_name_or_path", model_path,
        "--data_path", data_dir,
        "--data_train_path", f"fold_{fold_idx}_train.fa",
        "--data_val_path", f"fold_{fold_idx}_val.fa",
        "--output_dir", output_dir,
        "--run_name", f"{model_type}_fold_{fold_idx}",
        "--model_type", model_type,
        
        # Training parameters
        "--num_train_epochs", str(config["num_epochs"]),
        "--per_device_train_batch_size", str(config["batch_size"]),
        "--per_device_eval_batch_size", str(config["batch_size"]),
        "--gradient_accumulation_steps", str(config["gradient_accumulation_steps"]),
        "--learning_rate", str(config["learning_rate"]),
        "--weight_decay", str(config["weight_decay"]),
        "--warmup_steps", str(config["warmup_steps"]),
        
        # LoRA parameters
        "--use_lora", "True",
        "--lora_r", str(config["lora_r"]),
        "--lora_alpha", str(config["lora_alpha"]),
        "--lora_dropout", str(config["lora_dropout"]),
        "--lora_target_modules", config["lora_target_modules"],
        
        # Sliding window parameters
        "--window_size", str(config["window_size"]),
        "--window_stride", str(config["window_stride"]),
        "--max_sequence_length", str(config["max_sequence_length"]),
        "--pooling_strategy", config["pooling_strategy"],
        
        # Evaluation and saving
        "--evaluation_strategy", "steps",
        "--eval_steps", str(config["eval_steps"]),
        "--save_steps", str(config["save_steps"]),
        "--logging_steps", str(config["logging_steps"]),
        "--save_model", "True",
        "--load_best_model_at_end", "True",
        "--metric_for_best_model", "eval_f1",
        "--greater_is_better", "True",
        "--patience", str(config["patience"]),
        
        # Other parameters
        "--fp16", "True",
        "--dataloader_num_workers", str(config["dataloader_num_workers"]),
        "--seed", str(config["seed"]),
        "--overwrite_output_dir", "True",
    ]
    
    print(f"Command: {' '.join(cmd)}")
    
    try:
        # Run training
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=7200)  # 2 hour timeout
        
        if result.returncode == 0:
            print(f"✅ Fold {fold_idx} completed successfully")
            
            # Try to extract metrics from trainer_state.json
            trainer_state_path = os.path.join(output_dir, "trainer_state.json")
            if os.path.exists(trainer_state_path):
                with open(trainer_state_path, 'r') as f:
                    trainer_state = json.load(f)
                
                # Get best metrics
                best_metrics = {}
                if 'log_history' in trainer_state:
                    eval_logs = [log for log in trainer_state['log_history'] if 'eval_f1' in log]
                    if eval_logs:
                        best_log = max(eval_logs, key=lambda x: x.get('eval_f1', 0))
                        best_metrics = {
                            'eval_accuracy': best_log.get('eval_accuracy', 0),
                            'eval_f1': best_log.get('eval_f1', 0),
                            'eval_precision': best_log.get('eval_precision', 0),
                            'eval_recall': best_log.get('eval_recall', 0),
                            'epoch': best_log.get('epoch', 0)
                        }
                
                return {
                    'fold': fold_idx,
                    'model_type': model_type,
                    'status': 'success',
                    'output_dir': output_dir,
                    'metrics': best_metrics
                }
            else:
                return {
                    'fold': fold_idx,
                    'model_type': model_type,
                    'status': 'success',
                    'output_dir': output_dir,
                    'metrics': {}
                }
        else:
            print(f"❌ Fold {fold_idx} failed")
            print(f"Error: {result.stderr}")
            return {
                'fold': fold_idx,
                'model_type': model_type,
                'status': 'failed',
                'error': result.stderr,
                'metrics': {}
            }
            
    except subprocess.TimeoutExpired:
        print(f"❌ Fold {fold_idx} timed out")
        return {
            'fold': fold_idx,
            'model_type': model_type,
            'status': 'timeout',
            'metrics': {}
        }
    except Exception as e:
        print(f"❌ Fold {fold_idx} error: {e}")
        return {
            'fold': fold_idx,
            'model_type': model_type,
            'status': 'error',
            'error': str(e),
            'metrics': {}
        }

def run_cross_validation(model_configs, data_dir, output_base_dir, num_folds=5):
    """Run cross-validation for all models."""
    all_results = []
    
    for model_type, config in model_configs.items():
        print(f"\n🧬 Starting {model_type} Cross-Validation")
        print("=" * 60)
        
        model_results = []
        for fold_idx in range(num_folds):
            result = run_single_fold(
                model_type, fold_idx, data_dir, output_base_dir, 
                config["model_path"], config["training_config"]
            )
            if result:
                model_results.append(result)
                all_results.append(result)
        
        # Summarize results for this model
        successful_folds = [r for r in model_results if r['status'] == 'success' and r['metrics']]
        if successful_folds:
            metrics_df = pd.DataFrame([r['metrics'] for r in successful_folds])
            print(f"\n📊 {model_type} Summary:")
            print(f"  Successful folds: {len(successful_folds)}/{num_folds}")
            for metric in ['eval_accuracy', 'eval_f1', 'eval_precision', 'eval_recall']:
                if metric in metrics_df.columns:
                    mean_val = metrics_df[metric].mean()
                    std_val = metrics_df[metric].std()
                    print(f"  {metric}: {mean_val:.4f} ± {std_val:.4f}")
    
    return all_results

def save_results(results, output_file):
    """Save cross-validation results."""
    # Convert to DataFrame
    results_data = []
    for result in results:
        row = {
            'model_type': result['model_type'],
            'fold': result['fold'],
            'status': result['status']
        }
        if 'metrics' in result and result['metrics']:
            row.update(result['metrics'])
        if 'error' in result:
            row['error'] = result['error']
        results_data.append(row)
    
    df = pd.DataFrame(results_data)
    df.to_csv(output_file, index=False)
    print(f"📄 Results saved to: {output_file}")
    
    return df

def generate_summary_report(results_df, output_dir):
    """Generate a summary report."""
    report_path = os.path.join(output_dir, "cv_summary_report.txt")
    
    with open(report_path, 'w') as f:
        f.write("lncRNA Function Prediction - Cross-Validation Summary\n")
        f.write("=" * 60 + "\n")
        f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        # Overall statistics
        successful_results = results_df[results_df['status'] == 'success']
        f.write(f"Total experiments: {len(results_df)}\n")
        f.write(f"Successful experiments: {len(successful_results)}\n")
        f.write(f"Success rate: {len(successful_results)/len(results_df)*100:.1f}%\n\n")
        
        # Model-wise summary
        for model_type in results_df['model_type'].unique():
            model_results = successful_results[successful_results['model_type'] == model_type]
            if len(model_results) > 0:
                f.write(f"{model_type} Results:\n")
                f.write("-" * 30 + "\n")
                f.write(f"  Successful folds: {len(model_results)}/5\n")
                
                for metric in ['eval_accuracy', 'eval_f1', 'eval_precision', 'eval_recall']:
                    if metric in model_results.columns and not model_results[metric].isna().all():
                        mean_val = model_results[metric].mean()
                        std_val = model_results[metric].std()
                        f.write(f"  {metric}: {mean_val:.4f} ± {std_val:.4f}\n")
                f.write("\n")
        
        # Comparison
        if len(results_df['model_type'].unique()) > 1:
            f.write("Model Comparison:\n")
            f.write("-" * 30 + "\n")
            comparison_metrics = ['eval_accuracy', 'eval_f1', 'eval_precision', 'eval_recall']
            for metric in comparison_metrics:
                if metric in successful_results.columns:
                    f.write(f"{metric}:\n")
                    for model_type in successful_results['model_type'].unique():
                        model_data = successful_results[successful_results['model_type'] == model_type]
                        if not model_data[metric].isna().all():
                            mean_val = model_data[metric].mean()
                            f.write(f"  {model_type}: {mean_val:.4f}\n")
                    f.write("\n")
    
    print(f"📋 Summary report saved to: {report_path}")

def main():
    parser = argparse.ArgumentParser(description='Run cross-validation training')
    parser.add_argument('--data_dir', default='data/cv_splits', help='Directory with CV splits')
    parser.add_argument('--output_dir', default='outputs/cv_lora', help='Output directory')
    parser.add_argument('--num_folds', type=int, default=5, help='Number of CV folds')
    parser.add_argument('--config_file', default='cv_config.json', help='Configuration file')
    
    args = parser.parse_args()
    
    print("🧬 lncRNA Function Prediction - Cross-Validation Training")
    print("=" * 70)
    print(f"Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Load configuration
    if os.path.exists(args.config_file):
        with open(args.config_file, 'r') as f:
            model_configs = json.load(f)
        print(f"📁 Configuration loaded from: {args.config_file}")
    else:
        print(f"❌ Configuration file not found: {args.config_file}")
        print("Please create the configuration file first.")
        return
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Run cross-validation
    results = run_cross_validation(model_configs, args.data_dir, args.output_dir, args.num_folds)
    
    # Save results
    results_file = os.path.join(args.output_dir, "cv_results.csv")
    results_df = save_results(results, results_file)
    
    # Generate summary report
    generate_summary_report(results_df, args.output_dir)
    
    print(f"\n🎉 Cross-validation completed!")
    print(f"End time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Results saved in: {args.output_dir}")

if __name__ == "__main__":
    main()
