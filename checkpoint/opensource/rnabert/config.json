{"architectures": ["RnaBertForPretraining"], "attention_dropout": 0.0, "bos_token_id": 1, "eos_token_id": 2, "head": {"act": null, "bias": true, "dropout": 0.0, "hidden_size": null, "layer_norm_eps": 1e-12, "num_labels": 1, "problem_type": null, "transform": null, "transform_act": "gelu"}, "hidden_act": "gelu", "hidden_dropout": 0.0, "hidden_size": 120, "initializer_range": 0.02, "intermediate_size": 40, "layer_norm_eps": 1e-12, "lm_head": {"act": null, "bias": true, "dropout": 0.0, "hidden_size": 120, "layer_norm_eps": 1e-12, "transform": "nonlinear", "transform_act": "gelu"}, "mask_token_id": 4, "max_position_embeddings": 440, "model_type": "rnabert", "null_token_id": 5, "num_attention_heads": 12, "num_hidden_layers": 6, "pad_token_id": 0, "position_embedding_type": "absolute", "ss_vocab_size": 8, "torch_dtype": "float32", "transformers_version": "4.39.3", "type_vocab_size": 2, "unk_token_id": 3, "use_cache": true, "vocab_size": 25}