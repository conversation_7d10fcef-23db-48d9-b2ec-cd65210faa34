#!/usr/bin/env python3
"""
Actually evaluate the trained LoRA model on the independent test set
"""

import os
import sys
import json
import torch
import numpy as np
from sklearn.metrics import accuracy_score, f1_score, precision_score, recall_score, roc_auc_score, classification_report, confusion_matrix
from torch.utils.data import DataLoader
import transformers
from transformers import Trainer

# Add paths
sys.path.append('downstream')
sys.path.append('.')

def find_best_model():
    """Find the best performing fold and return its path."""
    cv_file = 'outputs/ft/lncrna-function-cv/lncRNA_function_cv_lora_with_test/rna-fm/seed_666/cv_results.json'
    
    with open(cv_file, 'r') as f:
        cv_results = json.load(f)
    
    f1_scores = cv_results['overall_stats']['eval_f1']['values']
    best_fold_idx = np.argmax(f1_scores)
    best_f1 = f1_scores[best_fold_idx]
    
    best_model_path = f'outputs/ft/lncrna-function-cv/lncRNA_function_cv_lora_with_test/rna-fm/seed_666/fold_{best_fold_idx}'
    
    print(f"🏆 Best model: Fold {best_fold_idx} (F1: {best_f1:.4f})")
    print(f"📂 Model path: {best_model_path}")
    
    return best_model_path, best_fold_idx

def load_test_data():
    """Load the independent test set."""
    from downstream.train_lncrna_function_cv import load_fasta_data
    
    test_file = 'outputs/ft/lncrna-function-cv/lncRNA_function_cv_lora_with_test/rna-fm/seed_666/independent_test_set_567_hum_label.fa'
    
    print(f"📂 Loading test data from: {test_file}")
    sequences, labels = load_fasta_data(test_file)
    
    print(f"✅ Loaded {len(sequences)} test sequences")
    print(f"   Label distribution: Label 0: {labels.count(0)}, Label 1: {labels.count(1)}")
    
    return sequences, labels

def evaluate_with_trainer():
    """Use the trainer to evaluate on test set."""
    
    # Import necessary modules
    from downstream.train_lncrna_function_cv import SlidingWindowDataset, load_model
    from tokenizer.tokenization_opensource import OpenRnaLMTokenizer
    
    # Configuration
    class Args:
        def __init__(self):
            self.window_size = 1024
            self.window_stride = 512
            self.max_sequence_length = 8192
            self.cache_dir = None
            self.use_lora = True
            self.lora_r = 16
            self.lora_alpha = 32
            self.lora_dropout = 0.1
            self.lora_target_modules = "query,value,key,dense"
    
    class ModelArgs:
        def __init__(self):
            self.model_name_or_path = './checkpoint/opensource/rna-fm/'
            self.use_lora = True
            self.lora_r = 16
            self.lora_alpha = 32
            self.lora_dropout = 0.1
            self.lora_target_modules = "query,value,key,dense"
    
    args = Args()
    model_args = ModelArgs()
    
    # Load tokenizer
    tokenizer = OpenRnaLMTokenizer.from_pretrained(
        './checkpoint/opensource/rna-fm/', 
        trust_remote_code=True
    )
    
    # Load test data
    test_sequences, test_labels = load_test_data()
    
    # Create test dataset
    test_dataset = SlidingWindowDataset(
        sequences=test_sequences,
        labels=test_labels,
        args=args,
        tokenizer=tokenizer,
        kmer=-1
    )
    
    # Find best model
    best_model_path, best_fold = find_best_model()
    
    # Load the trained model
    print(f"🔄 Loading trained model...")
    
    # Load base model first
    model = load_model(
        'rna-fm',
        './checkpoint/opensource/rna-fm/',
        2,  # num_labels
        args,
        model_args
    )
    
    # Load the fine-tuned weights
    checkpoint_path = os.path.join(best_model_path, 'pytorch_model.bin')
    if os.path.exists(checkpoint_path):
        print(f"📂 Loading checkpoint: {checkpoint_path}")
        state_dict = torch.load(checkpoint_path, map_location='cpu')
        model.load_state_dict(state_dict, strict=False)
    else:
        print(f"⚠️  Checkpoint not found, using last checkpoint in directory")
        # Find the latest checkpoint
        checkpoints = [d for d in os.listdir(best_model_path) if d.startswith('checkpoint-')]
        if checkpoints:
            latest_checkpoint = sorted(checkpoints, key=lambda x: int(x.split('-')[1]))[-1]
            checkpoint_path = os.path.join(best_model_path, latest_checkpoint)
            print(f"📂 Using checkpoint: {checkpoint_path}")
            
            # Load using transformers
            from transformers import AutoModel
            model = AutoModel.from_pretrained(checkpoint_path)
    
    # Move to GPU if available
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = model.to(device)
    model.eval()
    
    print(f"🔄 Running evaluation on {len(test_dataset)} test samples...")
    
    # Simple evaluation loop
    all_predictions = []
    all_probabilities = []
    all_labels = []
    
    # Create data loader
    data_collator = transformers.DataCollatorWithPadding(tokenizer=tokenizer)
    test_loader = DataLoader(test_dataset, batch_size=4, shuffle=False, collate_fn=data_collator)
    
    with torch.no_grad():
        for batch_idx, batch in enumerate(test_loader):
            if batch_idx % 10 == 0:
                print(f"   Processing batch {batch_idx+1}/{len(test_loader)}")
            
            # Move to device
            input_ids = batch['input_ids'].to(device)
            attention_mask = batch['attention_mask'].to(device)
            labels = batch['labels']
            
            # Forward pass
            try:
                outputs = model(input_ids=input_ids, attention_mask=attention_mask)
                logits = outputs.logits
                
                # Get predictions and probabilities
                probabilities = torch.softmax(logits, dim=-1)
                predictions = torch.argmax(logits, dim=-1)
                
                all_predictions.extend(predictions.cpu().numpy())
                all_probabilities.extend(probabilities.cpu().numpy())
                all_labels.extend(labels.numpy())
                
            except Exception as e:
                print(f"⚠️  Error in batch {batch_idx}: {e}")
                continue
    
    if len(all_predictions) == 0:
        print("❌ No predictions generated. There might be an issue with model loading.")
        return None
    
    # Convert to numpy arrays
    y_true = np.array(all_labels)
    y_pred = np.array(all_predictions)
    y_prob = np.array(all_probabilities)
    
    # Calculate metrics
    accuracy = accuracy_score(y_true, y_pred)
    f1 = f1_score(y_true, y_pred, average='weighted')
    precision = precision_score(y_true, y_pred, average='weighted')
    recall = recall_score(y_true, y_pred, average='weighted')
    
    # Calculate AUROC
    if y_prob.shape[1] >= 2:
        auroc = roc_auc_score(y_true, y_prob[:, 1])
    else:
        auroc = None
    
    # Confusion matrix
    cm = confusion_matrix(y_true, y_pred)
    
    # Results
    results = {
        'test_samples': len(y_true),
        'accuracy': accuracy,
        'f1_score': f1,
        'precision': precision,
        'recall': recall,
        'auroc': auroc,
        'confusion_matrix': cm.tolist(),
        'best_fold': best_fold,
        'predictions_count': len(all_predictions)
    }
    
    return results

def main():
    """Main evaluation function."""
    print("🧪 Actual Test Set Evaluation - RNA-FM LoRA")
    print("=" * 50)
    
    try:
        results = evaluate_with_trainer()
        
        if results is None:
            print("❌ Evaluation failed")
            return
        
        print(f"\n🎯 ACTUAL Test Set Performance:")
        print(f"   Test samples: {results['test_samples']}")
        print(f"   Predictions generated: {results['predictions_count']}")
        print(f"   Accuracy: {results['accuracy']:.4f}")
        print(f"   F1 Score: {results['f1_score']:.4f}")
        print(f"   Precision: {results['precision']:.4f}")
        print(f"   Recall: {results['recall']:.4f}")
        
        if results['auroc'] is not None:
            print(f"   AUROC: {results['auroc']:.4f}")
        else:
            print(f"   AUROC: Could not calculate")
        
        print(f"\n📊 Confusion Matrix:")
        cm = results['confusion_matrix']
        print(f"   True Negative: {cm[0][0]}, False Positive: {cm[0][1]}")
        print(f"   False Negative: {cm[1][0]}, True Positive: {cm[1][1]}")
        
        # Save results
        output_file = 'outputs/ft/lncrna-function-cv/lncRNA_function_cv_lora_with_test/rna-fm/seed_666/actual_test_results.json'
        with open(output_file, 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"\n💾 Results saved to: {output_file}")
        
    except Exception as e:
        print(f"❌ Error during evaluation: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
