#!/usr/bin/env python3
"""
Ensemble multiple frozen pre-trained models for 90%+ performance
"""

import numpy as np
import pandas as pd
from sklearn.ensemble import VotingClassifier, StackingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.model_selection import StratifiedKFold, cross_val_score
from sklearn.metrics import accuracy_score, f1_score, roc_auc_score
import joblib
import json

class MultiModelEnsemble:
    """Ensemble features from multiple frozen models."""
    
    def __init__(self):
        self.feature_extractors = {}
        self.base_classifiers = {}
        self.meta_classifier = None
        
    def add_model_features(self, model_name, features, best_classifier_name, best_classifier):
        """Add features and best classifier from a model."""
        self.feature_extractors[model_name] = features
        self.base_classifiers[model_name] = {
            'name': best_classifier_name,
            'classifier': best_classifier
        }
        print(f"✅ Added {model_name}: {features.shape[1]} features, {best_classifier_name} classifier")
    
    def create_ensemble_features(self):
        """Combine features from all models."""
        if not self.feature_extractors:
            raise ValueError("No model features added")
        
        # Concatenate features from all models
        all_features = []
        feature_names = []
        
        for model_name, features in self.feature_extractors.items():
            all_features.append(features)
            feature_names.extend([f"{model_name}_feat_{i}" for i in range(features.shape[1])])
        
        ensemble_features = np.hstack(all_features)
        
        print(f"🔗 Ensemble features created:")
        print(f"   Combined shape: {ensemble_features.shape}")
        print(f"   Individual contributions:")
        for model_name, features in self.feature_extractors.items():
            print(f"     {model_name}: {features.shape[1]} features")
        
        return ensemble_features, feature_names
    
    def create_prediction_ensemble(self, labels):
        """Create ensemble based on predictions from individual models."""
        if not self.base_classifiers:
            raise ValueError("No base classifiers added")
        
        print("🔄 Creating prediction-based ensemble...")
        
        # Get predictions from each model using cross-validation
        cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
        
        ensemble_predictions = {}
        ensemble_probabilities = {}
        
        for model_name, classifier_info in self.base_classifiers.items():
            features = self.feature_extractors[model_name]
            classifier = classifier_info['classifier']
            
            # Cross-validation predictions
            cv_preds = []
            cv_probs = []
            
            for train_idx, val_idx in cv.split(features, labels):
                X_train, X_val = features[train_idx], features[val_idx]
                y_train = labels[train_idx]
                
                # Train classifier
                classifier.fit(X_train, y_train)
                
                # Predict
                preds = classifier.predict(X_val)
                probs = classifier.predict_proba(X_val)[:, 1]  # Probability of positive class
                
                cv_preds.extend(preds)
                cv_probs.extend(probs)
            
            ensemble_predictions[model_name] = np.array(cv_preds)
            ensemble_probabilities[model_name] = np.array(cv_probs)
        
        return ensemble_predictions, ensemble_probabilities
    
    def evaluate_ensemble_strategies(self, labels):
        """Evaluate different ensemble strategies."""
        print("📊 Evaluating Ensemble Strategies")
        print("=" * 40)
        
        results = {}
        
        # Strategy 1: Feature concatenation
        try:
            ensemble_features, _ = self.create_ensemble_features()
            
            # Test with different classifiers
            from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
            from sklearn.svm import SVC
            
            feature_classifiers = {
                'Random Forest': RandomForestClassifier(n_estimators=200, random_state=42),
                'Gradient Boosting': GradientBoostingClassifier(n_estimators=200, random_state=42),
                'SVM': SVC(probability=True, random_state=42),
                'Logistic Regression': LogisticRegression(random_state=42)
            }
            
            cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
            
            for clf_name, classifier in feature_classifiers.items():
                scores = {
                    'accuracy': cross_val_score(classifier, ensemble_features, labels, cv=cv, scoring='accuracy'),
                    'f1': cross_val_score(classifier, ensemble_features, labels, cv=cv, scoring='f1_weighted'),
                    'roc_auc': cross_val_score(classifier, ensemble_features, labels, cv=cv, scoring='roc_auc')
                }
                
                results[f"Feature_Concat_{clf_name}"] = {
                    'accuracy': {'mean': scores['accuracy'].mean(), 'std': scores['accuracy'].std()},
                    'f1': {'mean': scores['f1'].mean(), 'std': scores['f1'].std()},
                    'roc_auc': {'mean': scores['roc_auc'].mean(), 'std': scores['roc_auc'].std()}
                }
                
                print(f"Feature Concat + {clf_name}:")
                print(f"   F1: {scores['f1'].mean():.4f} ± {scores['f1'].std():.4f}")
                print(f"   AUC: {scores['roc_auc'].mean():.4f} ± {scores['roc_auc'].std():.4f}")
        
        except Exception as e:
            print(f"❌ Feature concatenation failed: {e}")
        
        # Strategy 2: Prediction averaging
        try:
            ensemble_preds, ensemble_probs = self.create_prediction_ensemble(labels)
            
            # Simple averaging
            avg_probs = np.mean(list(ensemble_probs.values()), axis=0)
            avg_preds = (avg_probs > 0.5).astype(int)
            
            # Calculate metrics
            avg_accuracy = accuracy_score(labels, avg_preds)
            avg_f1 = f1_score(labels, avg_preds, average='weighted')
            avg_auc = roc_auc_score(labels, avg_probs)
            
            results['Prediction_Averaging'] = {
                'accuracy': {'mean': avg_accuracy, 'std': 0},
                'f1': {'mean': avg_f1, 'std': 0},
                'roc_auc': {'mean': avg_auc, 'std': 0}
            }
            
            print(f"Prediction Averaging:")
            print(f"   F1: {avg_f1:.4f}")
            print(f"   AUC: {avg_auc:.4f}")
            
            # Weighted averaging (based on individual performance)
            # You would calculate weights based on individual model performance
            weights = np.array([0.4, 0.6])  # Example weights for 2 models
            if len(ensemble_probs) == len(weights):
                weighted_probs = np.average(list(ensemble_probs.values()), axis=0, weights=weights)
                weighted_preds = (weighted_probs > 0.5).astype(int)
                
                weighted_accuracy = accuracy_score(labels, weighted_preds)
                weighted_f1 = f1_score(labels, weighted_preds, average='weighted')
                weighted_auc = roc_auc_score(labels, weighted_probs)
                
                results['Weighted_Averaging'] = {
                    'accuracy': {'mean': weighted_accuracy, 'std': 0},
                    'f1': {'mean': weighted_f1, 'std': 0},
                    'roc_auc': {'mean': weighted_auc, 'std': 0}
                }
                
                print(f"Weighted Averaging:")
                print(f"   F1: {weighted_f1:.4f}")
                print(f"   AUC: {weighted_auc:.4f}")
        
        except Exception as e:
            print(f"❌ Prediction ensemble failed: {e}")
        
        return results
    
    def find_best_strategy(self, results):
        """Find the best ensemble strategy."""
        best_strategy = None
        best_f1 = 0
        
        for strategy, metrics in results.items():
            f1_score = metrics['f1']['mean']
            if f1_score > best_f1:
                best_f1 = f1_score
                best_strategy = strategy
        
        return best_strategy, best_f1

def create_comprehensive_ensemble():
    """Create a comprehensive ensemble for 90%+ performance."""
    print("🚀 Comprehensive Multi-Model Ensemble")
    print("=" * 50)
    
    # Simulate having features from multiple models
    # In practice, you would load these from frozen_pretrain_classification.py results
    
    np.random.seed(42)
    n_samples = 1134
    
    # Simulate features from different models
    rna_fm_features = np.random.randn(n_samples, 768)
    splicebert_features = np.random.randn(n_samples, 768)
    labels = np.random.randint(0, 2, n_samples)
    
    # Create ensemble
    ensemble = MultiModelEnsemble()
    
    # Add model features (in practice, load from saved results)
    from sklearn.ensemble import RandomForestClassifier
    from sklearn.neural_network import MLPClassifier
    
    ensemble.add_model_features(
        'RNA-FM', 
        rna_fm_features, 
        'Random Forest',
        RandomForestClassifier(n_estimators=200, random_state=42)
    )
    
    ensemble.add_model_features(
        'SpliceBERT', 
        splicebert_features, 
        'MLP',
        MLPClassifier(hidden_layer_sizes=(256, 128), random_state=42)
    )
    
    # Evaluate ensemble strategies
    results = ensemble.evaluate_ensemble_strategies(labels)
    
    # Find best strategy
    best_strategy, best_f1 = ensemble.find_best_strategy(results)
    
    print(f"\n🏆 Best Ensemble Strategy: {best_strategy}")
    print(f"   F1 Score: {best_f1:.4f}")
    
    if best_f1 >= 0.90:
        print(f"   🎯 TARGET ACHIEVED: {best_f1:.1%} ≥ 90%")
    else:
        print(f"   📈 Progress: {best_f1:.1%} (need {0.90 - best_f1:.1%} more)")
    
    # Save results
    with open('ensemble_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n💾 Results saved to: ensemble_results.json")
    
    return results, best_strategy, best_f1

if __name__ == "__main__":
    results, best_strategy, best_f1 = create_comprehensive_ensemble()
    
    print(f"\n💡 Recommendations for 90%+ Performance:")
    print(f"   1. Extract features from both RNA-FM and SpliceBERT (frozen)")
    print(f"   2. Train multiple classifiers on each feature set")
    print(f"   3. Use {best_strategy.replace('_', ' ').lower()} for final ensemble")
    print(f"   4. Expected performance: {best_f1:.1%}")
    
    if best_f1 < 0.90:
        print(f"\n🔧 Additional strategies to try:")
        print(f"   • Add more pre-trained models (DNABERT, Nucleotide Transformer)")
        print(f"   • Use stacking with meta-learner")
        print(f"   • Apply feature selection on concatenated features")
        print(f"   • Use advanced ensemble methods (XGBoost, LightGBM)")
