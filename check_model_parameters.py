#!/usr/bin/env python3
"""
Check model parameters to determine if full fine-tuning or parameter freezing was used
"""

import sys
import torch
import json
from collections import defaultdict

sys.path.append('.')
from tokenizer.tokenization_opensource import OpenRnaLMTokenizer
from model.rnafm.modeling_rnafm import RnaFmForSequenceClassification
from model.splicebert.modeling_splicebert import SpliceBertForSequenceClassification

def analyze_model_parameters(model_type, model_path, checkpoint_path=None):
    """Analyze model parameters to check training configuration"""
    
    print(f"\n🔍 Analyzing {model_type.upper()} Model Parameters")
    print("="*60)
    
    # Load base model
    print(f"📂 Loading base model from: {model_path}")
    
    if model_type == 'rna-fm':
        base_model = RnaFmForSequenceClassification.from_pretrained(
            model_path,
            num_labels=2,
            problem_type="single_label_classification",
            trust_remote_code=True,
        )
    elif model_type == 'splicebert-ms1024':
        base_model = SpliceBertForSequenceClassification.from_pretrained(
            model_path,
            num_labels=2,
            problem_type="single_label_classification",
            trust_remote_code=True,
        )
    
    # Analyze parameter structure
    total_params = 0
    trainable_params = 0
    frozen_params = 0
    
    param_groups = defaultdict(lambda: {'total': 0, 'trainable': 0, 'frozen': 0})
    
    print(f"\n📊 Parameter Analysis:")
    print(f"{'Layer/Module':<40} {'Total':<10} {'Trainable':<12} {'Frozen':<10} {'Status':<15}")
    print("-" * 90)
    
    for name, param in base_model.named_parameters():
        param_count = param.numel()
        total_params += param_count
        
        # Determine parameter group
        if 'embeddings' in name:
            group = 'embeddings'
        elif 'encoder' in name or 'transformer' in name:
            group = 'encoder'
        elif 'classifier' in name or 'head' in name:
            group = 'classifier'
        elif 'pooler' in name:
            group = 'pooler'
        else:
            group = 'other'
        
        param_groups[group]['total'] += param_count
        
        if param.requires_grad:
            trainable_params += param_count
            param_groups[group]['trainable'] += param_count
            status = "✅ Trainable"
        else:
            frozen_params += param_count
            param_groups[group]['frozen'] += param_count
            status = "❄️  Frozen"
        
        # Print first few parameters of each group for verification
        if param_groups[group]['total'] == param_count:  # First parameter in this group
            print(f"{name:<40} {param_count:<10} {param_count if param.requires_grad else 0:<12} {0 if param.requires_grad else param_count:<10} {status:<15}")
    
    print("-" * 90)
    
    # Summary by parameter groups
    print(f"\n📈 Parameter Groups Summary:")
    print(f"{'Group':<15} {'Total':<12} {'Trainable':<12} {'Frozen':<12} {'Trainable %':<12}")
    print("-" * 70)
    
    for group, counts in param_groups.items():
        trainable_pct = (counts['trainable'] / counts['total'] * 100) if counts['total'] > 0 else 0
        print(f"{group:<15} {counts['total']:<12,} {counts['trainable']:<12,} {counts['frozen']:<12,} {trainable_pct:<12.1f}%")
    
    print("-" * 70)
    print(f"{'TOTAL':<15} {total_params:<12,} {trainable_params:<12,} {frozen_params:<12,} {(trainable_params/total_params*100):<12.1f}%")
    
    # Determine training type
    print(f"\n🎯 Training Configuration Analysis:")
    
    if frozen_params == 0:
        training_type = "🔥 FULL PARAMETER FINE-TUNING"
        description = "All model parameters are trainable. This is end-to-end fine-tuning."
    elif trainable_params == 0:
        training_type = "❄️  COMPLETELY FROZEN (Feature Extraction)"
        description = "No parameters are trainable. Model is used as a feature extractor."
    else:
        training_type = "🔄 PARTIAL FINE-TUNING"
        description = f"Mixed approach: {trainable_params:,} trainable, {frozen_params:,} frozen parameters."
    
    print(f"Training Type: {training_type}")
    print(f"Description: {description}")
    
    # Check if classifier head is trainable (most important for downstream tasks)
    classifier_trainable = param_groups['classifier']['trainable'] > 0
    print(f"Classifier Head: {'✅ Trainable' if classifier_trainable else '❄️  Frozen'}")
    
    # Load fine-tuned checkpoint if provided
    if checkpoint_path:
        print(f"\n🔄 Loading fine-tuned checkpoint: {checkpoint_path}")
        try:
            if model_type == 'rna-fm':
                finetuned_model = RnaFmForSequenceClassification.from_pretrained(
                    checkpoint_path,
                    num_labels=2,
                    problem_type="single_label_classification",
                    trust_remote_code=True,
                )
            elif model_type == 'splicebert-ms1024':
                finetuned_model = SpliceBertForSequenceClassification.from_pretrained(
                    checkpoint_path,
                    num_labels=2,
                    problem_type="single_label_classification",
                    trust_remote_code=True,
                )
            
            # Compare parameters to see what changed
            changed_params = 0
            unchanged_params = 0
            
            for (name1, param1), (name2, param2) in zip(base_model.named_parameters(), finetuned_model.named_parameters()):
                if name1 == name2:
                    if torch.allclose(param1, param2, atol=1e-6):
                        unchanged_params += 1
                    else:
                        changed_params += 1
            
            print(f"Parameter Comparison:")
            print(f"  Changed parameters: {changed_params}")
            print(f"  Unchanged parameters: {unchanged_params}")
            
            if changed_params > 0:
                print(f"  ✅ Confirmation: Parameters were updated during fine-tuning")
            else:
                print(f"  ⚠️  Warning: No parameters appear to have changed")
                
        except Exception as e:
            print(f"  ❌ Error loading checkpoint: {e}")
    
    return {
        'total_params': total_params,
        'trainable_params': trainable_params,
        'frozen_params': frozen_params,
        'training_type': training_type,
        'param_groups': dict(param_groups)
    }

def main():
    """Main analysis function"""
    
    print("🧬 RNA Model Parameter Analysis")
    print("="*60)
    
    models = [
        {
            'type': 'rna-fm',
            'base_path': './checkpoint/opensource/rna-fm/',
            'checkpoint_path': './outputs/ft/lncrna-function/lncRNA_function/rna-fm/seed_666/checkpoint-500'
        },
        {
            'type': 'splicebert-ms1024',
            'base_path': './checkpoint/opensource/splicebert-ms1024/',
            'checkpoint_path': './outputs/ft/lncrna-function/lncRNA_function/splicebert-ms1024/seed_666/checkpoint-1500'
        }
    ]
    
    results = {}
    
    for model_info in models:
        try:
            result = analyze_model_parameters(
                model_info['type'],
                model_info['base_path'],
                model_info['checkpoint_path']
            )
            results[model_info['type']] = result
        except Exception as e:
            print(f"❌ Error analyzing {model_info['type']}: {e}")
    
    # Final comparison
    if len(results) == 2:
        print(f"\n{'='*60}")
        print("🏆 FINAL COMPARISON")
        print(f"{'='*60}")
        
        for model_type, result in results.items():
            print(f"\n{model_type.upper()}:")
            print(f"  Total Parameters: {result['total_params']:,}")
            print(f"  Trainable Parameters: {result['trainable_params']:,}")
            print(f"  Training Type: {result['training_type']}")
    
    # Save results
    with open('parameter_analysis.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n✅ Analysis complete! Results saved to parameter_analysis.json")

if __name__ == "__main__":
    main()
