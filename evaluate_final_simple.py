#!/usr/bin/env python3
"""
Simplified final evaluation script for lncRNA function prediction
Uses original model checkpoints with trained LoRA weights
"""

import os
import sys
import torch
import numpy as np
import pandas as pd
from sklearn.metrics import accuracy_score, precision_recall_fscore_support, roc_auc_score, confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import argparse

# Add paths
sys.path.append('.')
sys.path.append('downstream')

# Import required modules
from tokenizer.tokenization_opensource import OpenRnaLMTokenizer
from model.rnafm.modeling_rnafm import RnaFmForSequenceClassification
from model.splicebert.modeling_splicebert import SpliceBertForSequenceClassification
from peft import PeftModel
from train_lncrna_function_lora import load_fasta_data, SlidingWindowModel

def load_best_model_info(cv_results_path):
    """Load CV results and find best models."""
    cv_results = pd.read_csv(cv_results_path)
    
    best_models = {}
    for model_type in cv_results['model_type'].unique():
        model_results = cv_results[
            (cv_results['model_type'] == model_type) & 
            (cv_results['status'] == 'success')
        ]
        
        if len(model_results) > 0:
            best_fold = model_results.loc[model_results['eval_f1'].idxmax()]
            best_models[model_type] = {
                'fold': int(best_fold['fold']),
                'f1': best_fold['eval_f1'],
                'accuracy': best_fold['eval_accuracy'],
                'precision': best_fold['eval_precision'],
                'recall': best_fold['eval_recall']
            }
    
    return best_models

def load_model_for_evaluation(model_type, fold_idx, original_checkpoint_path):
    """Load model with LoRA weights for evaluation."""
    print(f"  Loading {model_type} model from fold {fold_idx}...")
    
    # Load tokenizer from original checkpoint
    tokenizer = OpenRnaLMTokenizer.from_pretrained(
        original_checkpoint_path,
        model_max_length=1024,
        padding_side="right",
        use_fast=True,
        trust_remote_code=True,
    )
    
    # Load base model from original checkpoint
    if model_type == 'rna-fm':
        base_model = RnaFmForSequenceClassification.from_pretrained(
            original_checkpoint_path,
            num_labels=2,
            problem_type="single_label_classification",
            trust_remote_code=True,
        )
    elif 'splicebert' in model_type:
        base_model = SpliceBertForSequenceClassification.from_pretrained(
            original_checkpoint_path,
            num_labels=2,
            problem_type="single_label_classification",
            trust_remote_code=True,
        )
    else:
        raise ValueError(f"Unsupported model type: {model_type}")
    
    # Load LoRA weights
    lora_path = f"outputs/cv_lora/{model_type}/fold_{fold_idx}"
    if os.path.exists(os.path.join(lora_path, "adapter_config.json")):
        print(f"    Loading LoRA weights from {lora_path}")
        model = PeftModel.from_pretrained(base_model, lora_path)
    else:
        print(f"    Warning: No LoRA weights found at {lora_path}, using base model")
        model = base_model
    
    # Wrap with sliding window model (simplified version)
    wrapped_model = SlidingWindowModel(model, pooling_strategy='mean')
    wrapped_model.eval()
    
    return wrapped_model, tokenizer

def evaluate_on_test_set(model, tokenizer, test_data_path, device='cuda', batch_size=4):
    """Evaluate model on test data."""
    print(f"  Loading test data from {test_data_path}")
    
    # Load test data
    sequences, labels = load_fasta_data(test_data_path)
    print(f"  Test set: {len(sequences)} sequences")
    
    model.to(device)
    model.eval()
    
    all_predictions = []
    all_probabilities = []
    all_labels = []
    
    # Process in batches
    for i in range(0, len(sequences), batch_size):
        batch_sequences = sequences[i:i+batch_size]
        batch_labels = labels[i:i+batch_size]
        
        # Tokenize batch
        tokenized = tokenizer(
            batch_sequences,
            truncation=True,
            padding=True,
            max_length=1024,
            return_tensors='pt'
        )
        
        # Move to device
        input_ids = tokenized['input_ids'].to(device)
        attention_mask = tokenized['attention_mask'].to(device)
        
        with torch.no_grad():
            outputs = model(
                input_ids=input_ids,
                attention_mask=attention_mask
            )
            
            logits = outputs['logits']
            probabilities = torch.softmax(logits, dim=-1)
            predictions = torch.argmax(logits, dim=-1)
            
            # Collect results
            all_predictions.extend(predictions.cpu().numpy())
            all_probabilities.extend(probabilities.cpu().numpy())
            all_labels.extend(batch_labels)
    
    return np.array(all_predictions), np.array(all_probabilities), np.array(all_labels)

def calculate_metrics(predictions, probabilities, labels):
    """Calculate evaluation metrics."""
    # Basic metrics
    accuracy = accuracy_score(labels, predictions)
    precision, recall, f1, _ = precision_recall_fscore_support(labels, predictions, average='binary')
    
    # ROC AUC
    try:
        auc = roc_auc_score(labels, probabilities[:, 1])
    except:
        auc = 0.0
    
    # Confusion matrix
    cm = confusion_matrix(labels, predictions)
    
    return {
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1': f1,
        'auc': auc,
        'confusion_matrix': cm
    }

def plot_confusion_matrix(cm, model_name, output_dir):
    """Plot confusion matrix."""
    plt.figure(figsize=(8, 6))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                xticklabels=['Non-functional', 'Functional'],
                yticklabels=['Non-functional', 'Functional'])
    plt.title(f'Confusion Matrix - {model_name}')
    plt.ylabel('True Label')
    plt.xlabel('Predicted Label')
    
    plot_path = os.path.join(output_dir, f'{model_name}_confusion_matrix.png')
    plt.savefig(plot_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    return plot_path

def generate_final_report(cv_best_models, test_results, output_dir):
    """Generate comprehensive final report."""
    report_path = os.path.join(output_dir, 'final_evaluation_report.txt')
    
    with open(report_path, 'w') as f:
        f.write("lncRNA Function Prediction - Final Evaluation Report\n")
        f.write("=" * 60 + "\n")
        f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        f.write("Cross-Validation Best Models:\n")
        f.write("-" * 40 + "\n")
        for model_name, info in cv_best_models.items():
            f.write(f"{model_name} (Fold {info['fold']}):\n")
            f.write(f"  CV Accuracy:  {info['accuracy']:.4f}\n")
            f.write(f"  CV Precision: {info['precision']:.4f}\n")
            f.write(f"  CV Recall:    {info['recall']:.4f}\n")
            f.write(f"  CV F1 Score:  {info['f1']:.4f}\n\n")
        
        f.write("Final Test Set Results:\n")
        f.write("-" * 40 + "\n")
        for model_name, metrics in test_results.items():
            f.write(f"{model_name}:\n")
            f.write(f"  Test Accuracy:  {metrics['accuracy']:.4f}\n")
            f.write(f"  Test Precision: {metrics['precision']:.4f}\n")
            f.write(f"  Test Recall:    {metrics['recall']:.4f}\n")
            f.write(f"  Test F1 Score:  {metrics['f1']:.4f}\n")
            f.write(f"  Test AUC:       {metrics['auc']:.4f}\n")
            f.write(f"  Confusion Matrix:\n")
            cm = metrics['confusion_matrix']
            f.write(f"    TN: {cm[0,0]:4d}  FP: {cm[0,1]:4d}\n")
            f.write(f"    FN: {cm[1,0]:4d}  TP: {cm[1,1]:4d}\n\n")
        
        # Model comparison
        if len(test_results) > 1:
            f.write("Final Model Comparison:\n")
            f.write("-" * 40 + "\n")
            
            metrics_to_compare = ['accuracy', 'precision', 'recall', 'f1', 'auc']
            for metric in metrics_to_compare:
                f.write(f"{metric.capitalize()}:\n")
                for model_name in test_results.keys():
                    f.write(f"  {model_name}: {test_results[model_name][metric]:.4f}\n")
                
                # Find best model for this metric
                best_model = max(test_results.keys(), key=lambda x: test_results[x][metric])
                f.write(f"  Best: {best_model}\n\n")
    
    print(f"📋 Final evaluation report saved to: {report_path}")
    return report_path

def main():
    parser = argparse.ArgumentParser(description='Simplified final evaluation')
    parser.add_argument('--test_data', default='data/cv_splits/final_test.fa', help='Final test data')
    parser.add_argument('--cv_results', default='outputs/cv_lora/cv_results.csv', help='CV results file')
    parser.add_argument('--output_dir', default='outputs/final_evaluation', help='Output directory')
    parser.add_argument('--batch_size', type=int, default=4, help='Batch size for evaluation')
    parser.add_argument('--device', default='cuda', help='Device to use')
    
    args = parser.parse_args()
    
    print("🧬 lncRNA Function Prediction - Final Evaluation (Simplified)")
    print("=" * 70)
    print(f"Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Load best model information from CV
    print("\n📊 Loading cross-validation results...")
    cv_best_models = load_best_model_info(args.cv_results)
    
    for model_name, info in cv_best_models.items():
        print(f"  Best {model_name}: Fold {info['fold']} (F1: {info['f1']:.4f})")
    
    # Model checkpoint paths
    model_checkpoints = {
        'rna-fm': './checkpoint/opensource/rna-fm/',
        'splicebert-ms1024': './checkpoint/opensource/splicebert-ms1024/'
    }
    
    # Evaluate each model
    test_results = {}
    
    for model_type, checkpoint_path in model_checkpoints.items():
        if model_type not in cv_best_models:
            print(f"\n⚠️  No CV results found for {model_type}, skipping...")
            continue
            
        print(f"\n🔄 Evaluating {model_type}")
        print("-" * 50)
        
        try:
            # Load best model
            best_fold = cv_best_models[model_type]['fold']
            model, tokenizer = load_model_for_evaluation(model_type, best_fold, checkpoint_path)
            
            # Evaluate on test set
            print(f"  Evaluating on test set...")
            predictions, probabilities, labels = evaluate_on_test_set(
                model, tokenizer, args.test_data, args.device, args.batch_size
            )
            
            # Calculate metrics
            metrics = calculate_metrics(predictions, probabilities, labels)
            
            # Plot confusion matrix
            cm_path = plot_confusion_matrix(
                metrics['confusion_matrix'], model_type, args.output_dir
            )
            
            test_results[model_type] = metrics
            
            print(f"  ✅ {model_type} evaluation completed")
            print(f"     Test Accuracy: {metrics['accuracy']:.4f}")
            print(f"     Test F1 Score: {metrics['f1']:.4f}")
            print(f"     Test AUC: {metrics['auc']:.4f}")
            
        except Exception as e:
            print(f"  ❌ Error evaluating {model_type}: {e}")
            import traceback
            traceback.print_exc()
            continue
    
    # Generate final report
    if test_results:
        print(f"\n📋 Generating final report...")
        report_path = generate_final_report(cv_best_models, test_results, args.output_dir)
        
        # Save results to CSV
        results_df = pd.DataFrame(test_results).T
        results_csv = os.path.join(args.output_dir, 'final_test_results.csv')
        results_df.to_csv(results_csv)
        print(f"📄 Results saved to: {results_csv}")
        
        # Print summary
        print(f"\n🎯 Final Test Set Performance Summary:")
        print("=" * 50)
        for model_name, metrics in test_results.items():
            print(f"{model_name}:")
            print(f"  Accuracy: {metrics['accuracy']:.4f}")
            print(f"  F1 Score: {metrics['f1']:.4f}")
            print(f"  AUC:      {metrics['auc']:.4f}")
            print()
    
    print(f"🎉 Final evaluation completed!")
    print(f"End time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Results saved in: {args.output_dir}")

if __name__ == "__main__":
    main()
