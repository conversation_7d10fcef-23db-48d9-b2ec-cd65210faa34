#!/usr/bin/env python3
"""
Quick test script to verify training setup.
"""

import os
import sys
import torch
import logging
from dataclasses import dataclass, field
from typing import Optional
from transformers import Trainer, TrainingArguments, HfArgumentParser

# Add paths
sys.path.append('.')
sys.path.append('downstream')

# Import custom modules
from sliding_window_dataset import SlidingWindowDataset, load_fasta_data
from sequence_level_model import SequenceLevelAggregationModel, SequenceLevelDataCollator

# Import models and tokenizers
try:
    from tokenizer.tokenization_opensource import OpenRnaLMTokenizer
    TOKENIZER_AVAILABLE = True
except ImportError:
    TOKENIZER_AVAILABLE = False
    print("Warning: OpenRnaLMTokenizer not available")

try:
    from model.rnafm.modeling_rnafm import RnaFmForSequenceClassification
    RNAFM_AVAILABLE = True
except ImportError:
    RNAFM_AVAILABLE = False
    print("Warning: RnaFm model not available")

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def load_tokenizer(model_type: str, model_path: str):
    """Load the appropriate tokenizer."""
    if model_type == 'rna-fm':
        tokenizer_path = './checkpoint/opensource/rna-fm/'
    elif model_type == 'splicebert-ms1024':
        tokenizer_path = './checkpoint/opensource/splicebert-ms1024/'
    else:
        tokenizer_path = model_path

    tokenizer = OpenRnaLMTokenizer.from_pretrained(
        tokenizer_path,
        model_max_length=1024,
        padding_side="right",
        use_fast=True,
        trust_remote_code=True,
    )
    return tokenizer

def load_model(model_type: str, model_path: str, num_labels: int = 2):
    """Load the appropriate model."""
    if model_type == 'rna-fm':
        if not RNAFM_AVAILABLE:
            raise ImportError("RnaFm model not available")
        model = RnaFmForSequenceClassification.from_pretrained(
            model_path,
            num_labels=num_labels,
            problem_type="single_label_classification",
            trust_remote_code=True,
        )
    else:
        raise ValueError(f"Unsupported model type: {model_type}")
    
    return model

def main():
    """Quick test training function."""
    
    # Test parameters
    model_type = "rna-fm"
    model_path = "./checkpoint/opensource/rna-fm"
    data_path = "data/cv_splits_565"
    train_file = "fold_0_train.fa"
    val_file = "fold_0_val.fa"
    
    logger.info("🧪 Quick Training Test")
    logger.info("=" * 50)
    
    # Load tokenizer
    logger.info("Loading tokenizer...")
    tokenizer = load_tokenizer(model_type, model_path)
    
    # Load datasets (small subset for quick test)
    logger.info("Loading datasets...")
    train_sequences, train_labels = load_fasta_data(os.path.join(data_path, train_file))
    val_sequences, val_labels = load_fasta_data(os.path.join(data_path, val_file))
    
    # Use only first 10 samples for quick test
    train_sequences = train_sequences[:10]
    train_labels = train_labels[:10]
    val_sequences = val_sequences[:5]
    val_labels = val_labels[:5]
    
    logger.info(f"Training set: {len(train_sequences)} sequences")
    logger.info(f"Validation set: {len(val_sequences)} sequences")
    
    # Create datasets
    train_dataset = SlidingWindowDataset(
        sequences=train_sequences,
        labels=train_labels,
        tokenizer=tokenizer,
        window_size=1024,
        window_stride=512,
        max_length=9216
    )

    val_dataset = SlidingWindowDataset(
        sequences=val_sequences,
        labels=val_labels,
        tokenizer=tokenizer,
        window_size=1024,
        window_stride=512,
        max_length=9216
    )
    
    # Load base model
    logger.info(f"Loading {model_type} model...")
    base_model = load_model(model_type, model_path, num_labels=2)
    
    # Wrap with sequence-level aggregation model
    model = SequenceLevelAggregationModel(
        base_model=base_model,
        pooling_strategy="mean",
        num_labels=2,
        dropout_rate=0.1
    )
    
    # Count parameters
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    logger.info(f"Model parameters: {total_params:,} total, {trainable_params:,} trainable")
    
    # Training arguments
    training_args = TrainingArguments(
        output_dir="./test_output",
        num_train_epochs=1,  # Just 1 epoch for quick test
        per_device_train_batch_size=1,
        per_device_eval_batch_size=1,
        gradient_accumulation_steps=2,  # Smaller for quick test
        learning_rate=2e-5,
        warmup_steps=5,  # Very small warmup for quick test
        lr_scheduler_type="linear",
        max_grad_norm=1.0,
        logging_steps=1,
        eval_steps=5,
        evaluation_strategy="steps",
        save_steps=10,
        save_strategy="steps",
        load_best_model_at_end=False,
        dataloader_num_workers=0,  # Disable multiprocessing for debugging
        seed=42,
        overwrite_output_dir=True,
        report_to=None,  # Disable wandb/tensorboard
    )
    
    # Data collator
    data_collator = SequenceLevelDataCollator(tokenizer=tokenizer)
    
    # Initialize trainer
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=train_dataset,
        eval_dataset=val_dataset,
        tokenizer=tokenizer,
        data_collator=data_collator,
    )
    
    # Test a few training steps
    logger.info("Starting quick training test...")
    try:
        train_result = trainer.train()
        logger.info("✅ Training test completed successfully!")
        logger.info(f"Final training loss: {train_result.training_loss:.4f}")
        
        # Test evaluation
        eval_result = trainer.evaluate()
        logger.info("✅ Evaluation test completed successfully!")
        logger.info(f"Eval loss: {eval_result['eval_loss']:.4f}")
        
    except Exception as e:
        logger.error(f"❌ Training test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
