#!/usr/bin/env python3
"""
Test script to verify LoRA setup and compare parameter counts
"""

import os
import sys
import torch

# Add the downstream directory to path
sys.path.append('downstream')
sys.path.append('.')

def test_peft_installation():
    """Test if PEFT library is available."""
    print("🧪 Testing PEFT installation...")
    
    try:
        import peft
        from peft import LoraConfig, get_peft_model, TaskType
        print("✅ PEFT library is available")
        print(f"   Version: {peft.__version__}")
        return True
    except ImportError as e:
        print(f"❌ PEFT library not available: {e}")
        print("💡 Install with: pip install peft")
        return False

def test_model_loading():
    """Test loading models with and without LoRA."""
    print("\n🧪 Testing model loading...")
    
    try:
        from model.rnafm.modeling_rnafm import RnaFmForSequenceClassification
        from model.splicebert.modeling_splicebert import SpliceBertForSequenceClassification
        print("✅ Model classes imported successfully")
        return True
    except ImportError as e:
        print(f"❌ Model import failed: {e}")
        return False

def test_lora_application():
    """Test applying LoRA to a model."""
    print("\n🧪 Testing LoRA application...")
    
    try:
        from peft import LoraConfig, get_peft_model, TaskType
        from model.rnafm.modeling_rnafm import RnaFmForSequenceClassification
        
        # Load a small model for testing
        model_path = "./checkpoint/opensource/rna-fm/"
        if not os.path.exists(model_path):
            print(f"❌ Model path not found: {model_path}")
            return False
        
        print(f"📂 Loading model from: {model_path}")
        model = RnaFmForSequenceClassification.from_pretrained(
            model_path,
            num_labels=2,
            problem_type="single_label_classification",
            trust_remote_code=True,
        )
        
        # Count original parameters
        original_params = sum(p.numel() for p in model.parameters())
        original_trainable = sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        print(f"📊 Original model:")
        print(f"   Total parameters: {original_params:,}")
        print(f"   Trainable parameters: {original_trainable:,}")
        
        # Apply LoRA
        lora_config = LoraConfig(
            task_type=TaskType.SEQ_CLS,
            r=16,
            lora_alpha=32,
            lora_dropout=0.1,
            target_modules=["query", "value", "key", "dense"],
            bias="none",
        )
        
        lora_model = get_peft_model(model, lora_config)
        
        # Count LoRA parameters
        lora_total = sum(p.numel() for p in lora_model.parameters())
        lora_trainable = sum(p.numel() for p in lora_model.parameters() if p.requires_grad)
        
        print(f"📊 LoRA model:")
        print(f"   Total parameters: {lora_total:,}")
        print(f"   Trainable parameters: {lora_trainable:,}")
        print(f"   Trainable ratio: {100 * lora_trainable / lora_total:.2f}%")
        print(f"   Parameter reduction: {100 * (1 - lora_trainable / original_trainable):.1f}%")
        
        # Test forward pass
        print("🔄 Testing forward pass...")
        dummy_input = torch.randint(0, 10, (1, 100))
        dummy_attention = torch.ones(1, 100)
        
        with torch.no_grad():
            output = lora_model(input_ids=dummy_input, attention_mask=dummy_attention)
        
        print(f"✅ Forward pass successful, output shape: {output.logits.shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ LoRA application failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_target_modules():
    """Test different target module configurations."""
    print("\n🧪 Testing target module configurations...")
    
    try:
        from model.rnafm.modeling_rnafm import RnaFmForSequenceClassification
        
        model_path = "./checkpoint/opensource/rna-fm/"
        model = RnaFmForSequenceClassification.from_pretrained(
            model_path,
            num_labels=2,
            problem_type="single_label_classification",
            trust_remote_code=True,
        )
        
        # Print model structure to identify target modules
        print("🔍 Model structure analysis:")
        module_names = []
        for name, module in model.named_modules():
            if hasattr(module, 'weight') and len(module.weight.shape) == 2:
                module_names.append(name)
        
        print(f"   Found {len(module_names)} potential target modules")
        print("   Sample modules:")
        for name in module_names[:10]:  # Show first 10
            print(f"     - {name}")
        
        # Test common target module patterns
        common_patterns = [
            ["query", "value"],
            ["query", "value", "key"],
            ["query", "value", "key", "dense"],
            ["attention.self.query", "attention.self.value"],
        ]
        
        print("\n🎯 Testing target module patterns:")
        for i, pattern in enumerate(common_patterns):
            # Find actual matching modules
            matching_modules = []
            for name in module_names:
                for target in pattern:
                    if target in name:
                        matching_modules.append(name)
                        break
            
            print(f"   Pattern {i+1}: {pattern}")
            print(f"     Matches: {len(matching_modules)} modules")
            if matching_modules:
                print(f"     Examples: {matching_modules[:3]}")
        
        return True
        
    except Exception as e:
        print(f"❌ Target module test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🧬 LoRA Setup Test for lncRNA Function Prediction")
    print("=" * 60)
    
    all_tests_passed = True
    
    # Test PEFT installation
    if not test_peft_installation():
        all_tests_passed = False
        print("\n💡 To install PEFT:")
        print("   pip install peft")
        return
    
    # Test model loading
    if not test_model_loading():
        all_tests_passed = False
    
    # Test LoRA application
    if not test_lora_application():
        all_tests_passed = False
    
    # Test target modules
    if not test_target_modules():
        all_tests_passed = False
    
    print("\n" + "=" * 60)
    if all_tests_passed:
        print("🎉 All LoRA tests passed! Ready to run LoRA fine-tuning.")
        print("\nTo run LoRA experiments:")
        print("  bash scripts/lncrna_function_cv/run_lora_vs_full_comparison.sh")
        print("\nOr run individual models:")
        print("  bash scripts/lncrna_function_cv/rna_fm_cv_lora.sh")
        print("  bash scripts/lncrna_function_cv/splicebert_ms1024_cv_lora.sh")
    else:
        print("❌ Some tests failed. Please fix the issues before running LoRA fine-tuning.")
    
    print("\n📋 Expected benefits of LoRA:")
    print("- Reduced overfitting on small datasets (1134 sequences)")
    print("- 90-95% reduction in trainable parameters")
    print("- Faster training and lower memory usage")
    print("- Potentially better generalization")
    print("- Easier to deploy and store (only LoRA weights needed)")

if __name__ == "__main__":
    main()
