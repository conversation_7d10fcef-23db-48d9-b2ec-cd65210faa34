#!/usr/bin/env python3
"""
Predict lncRNA function using trained RNA-FM and SpliceBERT-MS1024 models
"""

import os
import sys
import torch
import numpy as np
import pandas as pd
from tqdm import tqdm
import json
from datetime import datetime

sys.path.append('.')
from downstream.train_lncrna_function import SlidingWindowDataset, load_fasta_data
from tokenizer.tokenization_opensource import OpenRnaLMTokenizer
from model.rnafm.modeling_rnafm import RnaFmForSequenceClassification
from model.splicebert.modeling_splicebert import SpliceBertForSequenceClassification

def load_unlabeled_fasta(fasta_path):
    """Load FASTA file without labels"""
    sequences = []
    sequence_ids = []
    
    print(f"📂 Loading FASTA file: {fasta_path}")
    
    with open(fasta_path, 'r') as f:
        current_id = None
        current_seq = ""
        
        for line in f:
            line = line.strip()
            if line.startswith('>'):
                # Save previous sequence
                if current_id is not None and current_seq:
                    sequences.append(current_seq)
                    sequence_ids.append(current_id)
                
                # Start new sequence
                current_id = line[1:]  # Remove '>' character
                current_seq = ""
            else:
                current_seq += line
        
        # Save last sequence
        if current_id is not None and current_seq:
            sequences.append(current_seq)
            sequence_ids.append(current_id)
    
    print(f"✅ Loaded {len(sequences)} sequences")
    return sequence_ids, sequences

def load_model_and_predict(model_type, model_path, sequences, batch_size=8):
    """Load model and predict on sequences"""
    
    print(f"\n🔄 Loading {model_type.upper()} model from: {model_path}")
    
    # Load tokenizer
    if model_type == 'rna-fm':
        tokenizer = OpenRnaLMTokenizer.from_pretrained(
            './checkpoint/opensource/rna-fm/',
            model_max_length=1024,
            padding_side="right",
            use_fast=True,
            trust_remote_code=True,
        )
        model = RnaFmForSequenceClassification.from_pretrained(
            model_path,
            num_labels=2,
            problem_type="single_label_classification",
            trust_remote_code=True,
        )
    elif model_type == 'splicebert-ms1024':
        tokenizer = OpenRnaLMTokenizer.from_pretrained(
            './checkpoint/opensource/splicebert-ms1024/',
            model_max_length=1024,
            padding_side="right",
            use_fast=True,
            trust_remote_code=True,
        )
        model = SpliceBertForSequenceClassification.from_pretrained(
            model_path,
            num_labels=2,
            problem_type="single_label_classification",
            trust_remote_code=True,
        )
    
    model.eval()
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model.to(device)
    print(f"✅ Model loaded on device: {device}")
    
    # Predict in batches
    predictions = []
    probabilities = []
    
    print(f"🔮 Starting prediction on {len(sequences)} sequences...")
    
    with torch.no_grad():
        for i in tqdm(range(0, len(sequences), batch_size), desc=f"Predicting with {model_type}"):
            batch_sequences = sequences[i:i+batch_size]
            batch_predictions = []
            batch_probabilities = []
            
            for seq in batch_sequences:
                # Handle long sequences with sliding window approach
                if len(seq) > 1024:
                    # Use sliding window for long sequences
                    window_size = 1024
                    stride = 512
                    window_predictions = []
                    window_probabilities = []
                    
                    for start in range(0, len(seq), stride):
                        end = min(start + window_size, len(seq))
                        window_seq = seq[start:end]
                        
                        if len(window_seq) < 50:  # Skip very short windows
                            continue
                        
                        # Tokenize window
                        inputs = tokenizer(
                            window_seq,
                            padding='max_length',
                            max_length=1024,
                            truncation=True,
                            return_tensors='pt'
                        )
                        
                        inputs = {k: v.to(device) for k, v in inputs.items()}
                        
                        # Forward pass
                        outputs = model(**inputs)
                        logits = outputs.logits
                        probs = torch.softmax(logits, dim=-1)
                        
                        window_predictions.append(torch.argmax(logits, dim=-1).cpu().item())
                        window_probabilities.append(probs[0, 1].cpu().item())
                    
                    # Aggregate window predictions (mean probability)
                    if window_probabilities:
                        avg_prob = np.mean(window_probabilities)
                        final_pred = 1 if avg_prob > 0.5 else 0
                        batch_predictions.append(final_pred)
                        batch_probabilities.append(avg_prob)
                    else:
                        batch_predictions.append(0)
                        batch_probabilities.append(0.0)
                
                else:
                    # Direct prediction for short sequences
                    inputs = tokenizer(
                        seq,
                        padding='max_length',
                        max_length=1024,
                        truncation=True,
                        return_tensors='pt'
                    )
                    
                    inputs = {k: v.to(device) for k, v in inputs.items()}
                    
                    # Forward pass
                    outputs = model(**inputs)
                    logits = outputs.logits
                    probs = torch.softmax(logits, dim=-1)
                    
                    batch_predictions.append(torch.argmax(logits, dim=-1).cpu().item())
                    batch_probabilities.append(probs[0, 1].cpu().item())
            
            predictions.extend(batch_predictions)
            probabilities.extend(batch_probabilities)
    
    print(f"✅ Prediction completed!")
    return np.array(predictions), np.array(probabilities)

def save_predictions(sequence_ids, sequences, predictions_dict, output_dir='predictions'):
    """Save predictions in multiple formats"""
    
    os.makedirs(output_dir, exist_ok=True)
    
    # Create comprehensive results DataFrame
    results_data = []
    
    for i, (seq_id, seq) in enumerate(zip(sequence_ids, sequences)):
        row = {
            'sequence_id': seq_id,
            'sequence_length': len(seq),
            'sequence': seq,
            'rna_fm_prediction': predictions_dict['rna-fm']['predictions'][i],
            'rna_fm_probability': predictions_dict['rna-fm']['probabilities'][i],
            'splicebert_prediction': predictions_dict['splicebert-ms1024']['predictions'][i],
            'splicebert_probability': predictions_dict['splicebert-ms1024']['probabilities'][i],
        }
        
        # Consensus prediction (both models agree)
        if row['rna_fm_prediction'] == row['splicebert_prediction']:
            row['consensus_prediction'] = row['rna_fm_prediction']
            row['consensus_confidence'] = 'High'
        else:
            # Use higher probability model
            if row['rna_fm_probability'] > row['splicebert_probability']:
                row['consensus_prediction'] = row['rna_fm_prediction']
                row['consensus_confidence'] = 'Medium'
            else:
                row['consensus_prediction'] = row['splicebert_prediction']
                row['consensus_confidence'] = 'Medium'
        
        results_data.append(row)
    
    df = pd.DataFrame(results_data)
    
    # Save comprehensive results
    csv_path = os.path.join(output_dir, 'lncrna_function_predictions.csv')
    df.to_csv(csv_path, index=False)
    print(f"✅ Comprehensive results saved: {csv_path}")
    
    # Save FASTA files with predictions
    for model_name in ['rna_fm', 'splicebert', 'consensus']:
        pred_col = f'{model_name}_prediction'
        if model_name == 'consensus':
            pred_col = 'consensus_prediction'
        
        # Functional lncRNAs
        functional_path = os.path.join(output_dir, f'{model_name}_functional_lncrnas.fa')
        with open(functional_path, 'w') as f:
            for _, row in df.iterrows():
                if row[pred_col] == 1:
                    f.write(f">{row['sequence_id']}_functional\n{row['sequence']}\n")
        
        # Non-functional lncRNAs
        nonfunctional_path = os.path.join(output_dir, f'{model_name}_nonfunctional_lncrnas.fa')
        with open(nonfunctional_path, 'w') as f:
            for _, row in df.iterrows():
                if row[pred_col] == 0:
                    f.write(f">{row['sequence_id']}_nonfunctional\n{row['sequence']}\n")
    
    return df

def generate_statistics(df, output_dir='predictions'):
    """Generate prediction statistics"""
    
    print(f"\n📊 PREDICTION STATISTICS")
    print("="*60)
    
    total_sequences = len(df)
    
    # Statistics for each model
    models = ['rna_fm', 'splicebert', 'consensus']
    model_names = ['RNA-FM', 'SpliceBERT-MS1024', 'Consensus']
    
    stats = {}
    
    for model, model_name in zip(models, model_names):
        pred_col = f'{model}_prediction' if model != 'consensus' else 'consensus_prediction'
        
        functional_count = (df[pred_col] == 1).sum()
        nonfunctional_count = (df[pred_col] == 0).sum()
        functional_pct = (functional_count / total_sequences) * 100
        nonfunctional_pct = (nonfunctional_count / total_sequences) * 100
        
        stats[model] = {
            'functional_count': functional_count,
            'nonfunctional_count': nonfunctional_count,
            'functional_percentage': functional_pct,
            'nonfunctional_percentage': nonfunctional_pct
        }
        
        print(f"\n🔹 {model_name}:")
        print(f"   Functional lncRNAs:     {functional_count:,} ({functional_pct:.1f}%)")
        print(f"   Non-functional lncRNAs: {nonfunctional_count:,} ({nonfunctional_pct:.1f}%)")
    
    # Model agreement analysis
    agreement = (df['rna_fm_prediction'] == df['splicebert_prediction']).sum()
    agreement_pct = (agreement / total_sequences) * 100
    
    print(f"\n🤝 MODEL AGREEMENT:")
    print(f"   Agreements: {agreement:,} ({agreement_pct:.1f}%)")
    print(f"   Disagreements: {total_sequences - agreement:,} ({100 - agreement_pct:.1f}%)")
    
    # Save statistics
    stats_path = os.path.join(output_dir, 'prediction_statistics.json')
    with open(stats_path, 'w') as f:
        json.dump(stats, f, indent=2)
    
    print(f"\n✅ Statistics saved: {stats_path}")
    
    return stats

def main():
    """Main prediction function"""
    
    print("🧬 lncRNA Function Prediction Pipeline")
    print("="*60)
    print(f"Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Configuration
    fasta_path = 'data/human.lncRNA_longest.95243.fa'
    models = {
        'rna-fm': './outputs/ft/lncrna-function/lncRNA_function/rna-fm/seed_666/checkpoint-500',
        'splicebert-ms1024': './outputs/ft/lncrna-function/lncRNA_function/splicebert-ms1024/seed_666/checkpoint-1500'
    }
    
    # Load sequences
    sequence_ids, sequences = load_unlabeled_fasta(fasta_path)
    
    # Predict with both models
    predictions_dict = {}
    
    for model_type, model_path in models.items():
        try:
            predictions, probabilities = load_model_and_predict(
                model_type, model_path, sequences, batch_size=4
            )
            predictions_dict[model_type] = {
                'predictions': predictions,
                'probabilities': probabilities
            }
        except Exception as e:
            print(f"❌ Error with {model_type}: {e}")
            continue
    
    if len(predictions_dict) == 2:
        # Save results
        df = save_predictions(sequence_ids, sequences, predictions_dict)
        
        # Generate statistics
        stats = generate_statistics(df)
        
        print(f"\n🎉 PREDICTION COMPLETED!")
        print(f"End time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"Total sequences processed: {len(sequences):,}")
        print(f"Results saved in: ./predictions/")
        
    else:
        print("❌ Failed to get predictions from both models")

if __name__ == "__main__":
    main()
