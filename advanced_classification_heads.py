#!/usr/bin/env python3
"""
Advanced classification heads for frozen pre-trained models
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from sklearn.model_selection import StratifiedKFold
from sklearn.metrics import accuracy_score, f1_score, roc_auc_score
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset

class AttentionClassificationHead(nn.Module):
    """Attention-based classification head."""
    
    def __init__(self, input_dim, hidden_dim=256, num_classes=2, dropout=0.1):
        super().__init__()
        
        self.attention = nn.MultiheadAttention(
            embed_dim=input_dim,
            num_heads=8,
            dropout=dropout,
            batch_first=True
        )
        
        self.layer_norm = nn.LayerNorm(input_dim)
        
        self.classifier = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 2, num_classes)
        )
    
    def forward(self, x):
        # x shape: (batch_size, seq_len, input_dim) or (batch_size, input_dim)
        if x.dim() == 2:
            x = x.unsqueeze(1)  # Add sequence dimension
        
        # Self-attention
        attn_output, _ = self.attention(x, x, x)
        attn_output = self.layer_norm(attn_output + x)
        
        # Global average pooling
        pooled = attn_output.mean(dim=1)
        
        # Classification
        logits = self.classifier(pooled)
        return logits

class ResidualMLPHead(nn.Module):
    """Residual MLP classification head."""
    
    def __init__(self, input_dim, hidden_dims=[512, 256, 128], num_classes=2, dropout=0.1):
        super().__init__()
        
        self.input_projection = nn.Linear(input_dim, hidden_dims[0])
        
        self.residual_blocks = nn.ModuleList()
        for i in range(len(hidden_dims) - 1):
            self.residual_blocks.append(
                ResidualBlock(hidden_dims[i], hidden_dims[i+1], dropout)
            )
        
        self.classifier = nn.Linear(hidden_dims[-1], num_classes)
        self.dropout = nn.Dropout(dropout)
    
    def forward(self, x):
        x = self.input_projection(x)
        x = F.relu(x)
        
        for block in self.residual_blocks:
            x = block(x)
        
        x = self.dropout(x)
        logits = self.classifier(x)
        return logits

class ResidualBlock(nn.Module):
    """Residual block for MLP."""
    
    def __init__(self, input_dim, output_dim, dropout=0.1):
        super().__init__()
        
        self.linear1 = nn.Linear(input_dim, output_dim)
        self.linear2 = nn.Linear(output_dim, output_dim)
        self.dropout = nn.Dropout(dropout)
        self.layer_norm = nn.LayerNorm(output_dim)
        
        # Projection for residual connection if dimensions don't match
        self.projection = nn.Linear(input_dim, output_dim) if input_dim != output_dim else None
    
    def forward(self, x):
        residual = x
        
        x = F.relu(self.linear1(x))
        x = self.dropout(x)
        x = self.linear2(x)
        
        # Residual connection
        if self.projection is not None:
            residual = self.projection(residual)
        
        x = self.layer_norm(x + residual)
        x = F.relu(x)
        
        return x

class EnsembleClassificationHead(nn.Module):
    """Ensemble of multiple classification heads."""
    
    def __init__(self, input_dim, num_classes=2):
        super().__init__()
        
        # Multiple different heads
        self.heads = nn.ModuleList([
            # Simple MLP
            nn.Sequential(
                nn.Linear(input_dim, 256),
                nn.ReLU(),
                nn.Dropout(0.1),
                nn.Linear(256, 128),
                nn.ReLU(),
                nn.Dropout(0.1),
                nn.Linear(128, num_classes)
            ),
            
            # Deeper MLP
            nn.Sequential(
                nn.Linear(input_dim, 512),
                nn.ReLU(),
                nn.Dropout(0.1),
                nn.Linear(512, 256),
                nn.ReLU(),
                nn.Dropout(0.1),
                nn.Linear(256, 128),
                nn.ReLU(),
                nn.Dropout(0.1),
                nn.Linear(128, num_classes)
            ),
            
            # Wide MLP
            nn.Sequential(
                nn.Linear(input_dim, 1024),
                nn.ReLU(),
                nn.Dropout(0.2),
                nn.Linear(1024, num_classes)
            )
        ])
        
        # Learnable weights for ensemble
        self.ensemble_weights = nn.Parameter(torch.ones(len(self.heads)))
    
    def forward(self, x):
        outputs = []
        for head in self.heads:
            outputs.append(head(x))
        
        # Weighted ensemble
        weights = F.softmax(self.ensemble_weights, dim=0)
        ensemble_output = sum(w * out for w, out in zip(weights, outputs))
        
        return ensemble_output

class AdvancedClassifierTrainer:
    """Train advanced classification heads on frozen features."""
    
    def __init__(self, device='cuda'):
        self.device = device
        
    def train_classifier(self, features, labels, classifier_type='attention', 
                        epochs=100, batch_size=32, lr=1e-3):
        """Train a specific classifier type."""
        
        # Convert to tensors
        X = torch.FloatTensor(features)
        y = torch.LongTensor(labels)
        
        # Create dataset
        dataset = TensorDataset(X, y)
        
        # Cross-validation
        cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
        cv_results = []
        
        for fold, (train_idx, val_idx) in enumerate(cv.split(features, labels)):
            print(f"  Fold {fold + 1}/5...")
            
            # Split data
            train_dataset = TensorDataset(X[train_idx], y[train_idx])
            val_dataset = TensorDataset(X[val_idx], y[val_idx])
            
            train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
            val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
            
            # Create model
            input_dim = features.shape[1]
            if classifier_type == 'attention':
                model = AttentionClassificationHead(input_dim)
            elif classifier_type == 'residual_mlp':
                model = ResidualMLPHead(input_dim)
            elif classifier_type == 'ensemble':
                model = EnsembleClassificationHead(input_dim)
            else:
                raise ValueError(f"Unknown classifier type: {classifier_type}")
            
            model.to(self.device)
            
            # Training setup
            optimizer = optim.AdamW(model.parameters(), lr=lr, weight_decay=1e-4)
            scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=epochs)
            criterion = nn.CrossEntropyLoss()
            
            # Training loop
            best_val_f1 = 0
            patience = 20
            patience_counter = 0
            
            for epoch in range(epochs):
                # Training
                model.train()
                train_loss = 0
                for batch_x, batch_y in train_loader:
                    batch_x, batch_y = batch_x.to(self.device), batch_y.to(self.device)
                    
                    optimizer.zero_grad()
                    outputs = model(batch_x)
                    loss = criterion(outputs, batch_y)
                    loss.backward()
                    optimizer.step()
                    
                    train_loss += loss.item()
                
                # Validation
                model.eval()
                val_preds = []
                val_probs = []
                val_true = []
                
                with torch.no_grad():
                    for batch_x, batch_y in val_loader:
                        batch_x, batch_y = batch_x.to(self.device), batch_y.to(self.device)
                        outputs = model(batch_x)
                        probs = F.softmax(outputs, dim=1)
                        preds = torch.argmax(outputs, dim=1)
                        
                        val_preds.extend(preds.cpu().numpy())
                        val_probs.extend(probs[:, 1].cpu().numpy())  # Probability of positive class
                        val_true.extend(batch_y.cpu().numpy())
                
                # Calculate metrics
                val_acc = accuracy_score(val_true, val_preds)
                val_f1 = f1_score(val_true, val_preds, average='weighted')
                val_auc = roc_auc_score(val_true, val_probs)
                
                # Early stopping
                if val_f1 > best_val_f1:
                    best_val_f1 = val_f1
                    patience_counter = 0
                else:
                    patience_counter += 1
                
                if patience_counter >= patience:
                    break
                
                scheduler.step()
            
            # Store fold results
            cv_results.append({
                'accuracy': val_acc,
                'f1': val_f1,
                'auc': val_auc
            })
        
        # Calculate average results
        avg_results = {}
        for metric in ['accuracy', 'f1', 'auc']:
            values = [result[metric] for result in cv_results]
            avg_results[metric] = {
                'mean': np.mean(values),
                'std': np.std(values),
                'values': values
            }
        
        return avg_results

def main():
    """Test advanced classification heads."""
    print("🧠 Advanced Classification Heads for Frozen Models")
    print("=" * 60)
    
    # Load pre-extracted features (you would run frozen_pretrain_classification.py first)
    # For demo, create synthetic features
    print("📊 Loading features...")
    
    # Simulate features (replace with actual feature loading)
    np.random.seed(42)
    n_samples = 1134
    feature_dim = 768  # Typical transformer dimension
    
    features = np.random.randn(n_samples, feature_dim)
    labels = np.random.randint(0, 2, n_samples)
    
    print(f"   Features shape: {features.shape}")
    print(f"   Labels distribution: {np.bincount(labels)}")
    
    # Test different classification heads
    trainer = AdvancedClassifierTrainer()
    
    classifiers_to_test = [
        'attention',
        'residual_mlp', 
        'ensemble'
    ]
    
    results = {}
    
    for classifier_type in classifiers_to_test:
        print(f"\n🔄 Testing {classifier_type.replace('_', ' ').title()} Classifier...")
        
        try:
            result = trainer.train_classifier(
                features, labels, 
                classifier_type=classifier_type,
                epochs=50,  # Reduced for demo
                batch_size=32,
                lr=1e-3
            )
            
            results[classifier_type] = result
            
            print(f"   Accuracy: {result['accuracy']['mean']:.4f} ± {result['accuracy']['std']:.4f}")
            print(f"   F1 Score: {result['f1']['mean']:.4f} ± {result['f1']['std']:.4f}")
            print(f"   ROC AUC:  {result['auc']['mean']:.4f} ± {result['auc']['std']:.4f}")
            
            if result['f1']['mean'] >= 0.90:
                print(f"   🎯 TARGET ACHIEVED: {result['f1']['mean']:.1%} ≥ 90%")
            
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    # Find best classifier
    if results:
        best_classifier = max(results.items(), key=lambda x: x[1]['f1']['mean'])
        print(f"\n🏆 Best Classifier: {best_classifier[0].replace('_', ' ').title()}")
        print(f"   F1 Score: {best_classifier[1]['f1']['mean']:.4f}")
        
        if best_classifier[1]['f1']['mean'] >= 0.90:
            print(f"   🎉 SUCCESS: 90%+ target achieved!")

if __name__ == "__main__":
    main()
