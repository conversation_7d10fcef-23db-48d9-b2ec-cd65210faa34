#!/usr/bin/env python3
"""
Quick test script to verify the lncRNA function prediction setup works
"""

import sys
import numpy as np
import time

def test_imports():
    """Test if all required packages are available."""
    print("🔍 Testing package imports...")
    
    required_packages = [
        ('numpy', 'np'),
        ('pandas', 'pd'),
        ('sklearn', None),
        ('xgboost', 'xgb'),
        ('lightgbm', 'lgb')
    ]
    
    missing = []
    
    for package, alias in required_packages:
        try:
            if alias:
                exec(f"import {package} as {alias}")
            else:
                exec(f"import {package}")
            print(f"   ✅ {package}")
        except ImportError:
            print(f"   ❌ {package} - MISSING")
            missing.append(package)
    
    if missing:
        print(f"\n⚠️  Missing packages: {missing}")
        print("Install with: pip install " + " ".join(missing))
        return False
    else:
        print("✅ All packages available")
        return True

def test_data_loading():
    """Test data loading functionality."""
    print("\n📂 Testing data loading...")
    
    try:
        sys.path.append('downstream')
        from downstream.train_lncrna_function_cv import load_fasta_data
        
        data_file = "data/567_hum_label.fa"
        sequences, labels = load_fasta_data(data_file)
        
        print(f"   ✅ Data loaded successfully")
        print(f"   📊 Sequences: {len(sequences)}")
        print(f"   📊 Labels: {dict(zip(*np.unique(labels, return_counts=True)))}")
        
        return True, sequences, labels
        
    except Exception as e:
        print(f"   ❌ Error loading data: {e}")
        return False, None, None

def test_feature_extraction(sequences):
    """Test feature extraction on a small subset."""
    print("\n🔧 Testing feature extraction...")

    try:
        # Test on first 10 sequences
        test_sequences = sequences[:10]

        # Simple feature extraction test (basic sequence features)
        import numpy as np
        features = []
        for seq in test_sequences:
            seq_len = len(seq)
            gc_content = (seq.count('G') + seq.count('C')) / seq_len
            features.append([seq_len, gc_content])

        features = np.array(features)

        print(f"   ✅ Feature extraction successful")
        print(f"   📊 Feature matrix shape: {features.shape}")
        print(f"   📊 Features per sequence: {features.shape[1]}")

        return True, features

    except Exception as e:
        print(f"   ❌ Error in feature extraction: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def test_classifier_setup():
    """Test classifier setup."""
    print("\n🤖 Testing classifier setup...")

    try:
        from sklearn.ensemble import RandomForestClassifier
        from sklearn.linear_model import LogisticRegression

        # Test basic classifier setup
        classifiers = {
            'RandomForest': RandomForestClassifier(n_estimators=10, random_state=42),
            'LogisticRegression': LogisticRegression(random_state=42)
        }

        print(f"   ✅ Classifier suite created")
        print(f"   📊 Number of classifiers: {len(classifiers)}")

        for name in classifiers.keys():
            print(f"      • {name}")

        return True

    except Exception as e:
        print(f"   ❌ Error in classifier setup: {e}")
        return False

def test_quick_training(features, labels):
    """Test quick training on subset."""
    print("\n⚡ Testing quick training...")
    
    try:
        from sklearn.ensemble import RandomForestClassifier
        from sklearn.model_selection import cross_val_score
        from sklearn.preprocessing import StandardScaler
        
        # Use subset of data and features
        n_samples = min(len(labels), features.shape[0])
        n_features = min(50, features.shape[1])

        X_subset = features[:n_samples, :n_features]
        y_subset = labels[:n_samples]
        
        # Scale features
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X_subset)
        
        # Quick training
        clf = RandomForestClassifier(n_estimators=10, random_state=42)
        scores = cross_val_score(clf, X_scaled, y_subset, cv=3, scoring='f1_weighted')
        
        print(f"   ✅ Quick training successful")
        print(f"   📊 Test F1 score: {scores.mean():.4f} ± {scores.std():.4f}")
        print(f"   📊 Data subset: {n_samples} samples, {n_features} features")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error in quick training: {e}")
        return False

def estimate_full_runtime():
    """Estimate full runtime based on quick tests."""
    print("\n⏱️  Runtime Estimation...")
    
    # Based on our tests and typical performance
    estimates = {
        'Feature extraction': '2-5 minutes',
        'Individual classifiers': '10-20 minutes', 
        'Ensemble creation': '5-10 minutes',
        'Total estimated time': '15-35 minutes'
    }
    
    for task, time_est in estimates.items():
        print(f"   {task}: {time_est}")
    
    print(f"\n💡 Recommendations:")
    print(f"   • Run during low-usage hours")
    print(f"   • Ensure stable power/connection")
    print(f"   • Monitor progress via log files")

def main():
    """Run all quick tests."""
    print("🧪 Quick Test Suite for lncRNA Function Prediction")
    print("=" * 55)
    
    start_time = time.time()
    all_passed = True
    
    # Test 1: Package imports
    if not test_imports():
        all_passed = False
        print("\n❌ Package import test failed. Please install missing packages.")
        return
    
    # Test 2: Data loading
    success, sequences, labels = test_data_loading()
    if not success:
        all_passed = False
        print("\n❌ Data loading test failed. Check data file path.")
        return
    
    # Test 3: Feature extraction
    success, features = test_feature_extraction(sequences)
    if not success:
        all_passed = False
        print("\n❌ Feature extraction test failed.")
        return
    
    # Test 4: Classifier setup
    if not test_classifier_setup():
        all_passed = False
        print("\n❌ Classifier setup test failed.")
        return
    
    # Test 5: Quick training
    if not test_quick_training(features, labels):
        all_passed = False
        print("\n❌ Quick training test failed.")
        return
    
    # Runtime estimation
    estimate_full_runtime()
    
    # Final assessment
    elapsed_time = time.time() - start_time
    
    print(f"\n🏁 Quick Test Results")
    print("=" * 30)
    print(f"⏱️  Test time: {elapsed_time:.1f} seconds")
    
    if all_passed:
        print("✅ ALL TESTS PASSED!")
        print("\n🚀 Ready to run lncRNA function prediction:")
        print("   python downstream/train_lncrna_function_cv.py")
        print("   OR")
        print("   bash scripts/lncrna_function_cv/run_all_cv.sh")
    else:
        print("❌ SOME TESTS FAILED!")
        print("Please fix the issues before running the full solution.")

if __name__ == "__main__":
    main()
