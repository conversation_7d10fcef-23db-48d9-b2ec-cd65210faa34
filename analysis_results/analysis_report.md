# lncRNA Function Prediction - Cross-Validation Results Analysis
======================================================================

## Dataset Information
- Total sequences: 1134
- Label distribution: {'0': 567, '1': 567}
- Sequence length: 242-205012 bp
- Mean length: 2131.8 ± 4567.2 bp

## Cross-Validation Setup
- Number of folds: 5
- CV seed: 42

## Results Comparison

| Metric | RNA-FM | SpliceBERT-MS1024 | Difference |
|--------|--------|-------------------|------------|
| Accuracy | 0.7416 ± 0.0147 | 0.7601 ± 0.0122 | +0.0185 |
| F1 | 0.7303 ± 0.0163 | 0.7565 ± 0.0138 | +0.0262 |
| Precision | 0.7902 ± 0.0162 | 0.7766 ± 0.0154 | -0.0135 |
| Recall | 0.7416 ± 0.0147 | 0.7601 ± 0.0122 | +0.0185 |

## Key Findings
- **Accuracy**: SpliceBERT-MS1024 performs better by 0.0185
- **F1**: SpliceBERT-MS1024 performs better by 0.0262

## Files Generated
- `metrics_comparison.png`: Box plots comparing model performance
- `fold_wise_comparison.png`: Heatmaps showing performance by fold
- `comparison_table.csv`: Detailed comparison table
- `analysis_report.md`: This report