import os
import sys
import csv
import copy
import json
import logging
from dataclasses import dataclass, field
from typing import Optional, Dict, Sequence, Tuple, List

import torch
import random
import sklearn
import scipy
import transformers

import numpy as np
from torch.utils.data import Dataset
import pandas as pd
import pdb

os.environ["WANDB_DISABLED"] = "true"

from transformers import Trainer, TrainingArguments, Bert<PERSON>okenizer, EsmTokenizer, EsmModel, AutoConfig, AutoModel, EarlyStoppingCallback
import sys
current_path = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_path)
sys.path.append(parent_dir)

# Import models with error handling
try:
    from model.rnalm.modeling_rnalm import RnaLmForSequenceClassification
    from model.rnalm.rnalm_config import RnaLmConfig
    RNALM_AVAILABLE = True
except ImportError as e:
    print(f"Warning: RnaLm model not available: {e}")
    RNALM_AVAILABLE = False

try:
    from model.rnafm.modeling_rnafm import RnaFmForSequenceClassification
    RNAFM_AVAILABLE = True
except ImportError as e:
    print(f"Warning: RnaFm model not available: {e}")
    RNAFM_AVAILABLE = False

try:
    from model.rnabert.modeling_rnabert import RnaBertForSequenceClassification
    RNABERT_AVAILABLE = True
except ImportError as e:
    print(f"Warning: RnaBert model not available: {e}")
    RNABERT_AVAILABLE = False

try:
    from model.rnamsm.modeling_rnamsm import RnaMsmForSequenceClassification
    RNAMSM_AVAILABLE = True
except ImportError as e:
    print(f"Warning: RnaMsm model not available: {e}")
    RNAMSM_AVAILABLE = False

try:
    from model.splicebert.modeling_splicebert import SpliceBertForSequenceClassification
    SPLICEBERT_AVAILABLE = True
except ImportError as e:
    print(f"Warning: SpliceBert model not available: {e}")
    SPLICEBERT_AVAILABLE = False

try:
    from model.utrbert.modeling_utrbert import UtrBertForSequenceClassification
    UTRBERT_AVAILABLE = True
except ImportError as e:
    print(f"Warning: UtrBert model not available: {e}")
    UTRBERT_AVAILABLE = False

try:
    from model.utrlm.modeling_utrlm import UtrLmForSequenceClassification
    UTRLM_AVAILABLE = True
except ImportError as e:
    print(f"Warning: UtrLm model not available: {e}")
    UTRLM_AVAILABLE = False

try:
    from tokenizer.tokenization_opensource import OpenRnaLMTokenizer
    TOKENIZER_AVAILABLE = True
except ImportError as e:
    print(f"Warning: OpenRnaLMTokenizer not available: {e}")
    TOKENIZER_AVAILABLE = False

early_stopping = EarlyStoppingCallback(early_stopping_patience=20)

@dataclass
class ModelArguments:
    model_name_or_path: Optional[str] = field(default="")

@dataclass
class DataArguments:
    data_path: str = field(default=None, metadata={"help": "Path to the training data."})
    data_train_path: str = field(default=None, metadata={"help": "Path to the training data."})
    data_val_path: str = field(default=None, metadata={"help": "Path to the validation data."})
    data_test_path: str = field(default=None, metadata={"help": "Path to the test data."})
    kmer: int = field(default=-1)

@dataclass
class TrainingArguments(transformers.TrainingArguments):
    cache_dir: Optional[str] = field(default=None)
    run_name: str = field(default="run")
    optim: str = field(default="adamw_torch")
    model_max_length: int = field(default=512, metadata={"help": "Maximum sequence length."})
    gradient_accumulation_steps: int = field(default=1)
    per_device_train_batch_size: int = field(default=1)
    per_device_eval_batch_size: int = field(default=1)
    num_train_epochs: int = field(default=1)
    fp16: bool = field(default=False)
    logging_steps: int = field(default=100)
    save_steps: int = field(default=100)
    eval_steps: int = field(default=100)
    evaluation_strategy: str = field(default="steps"),
    warmup_steps: int = field(default=50)
    weight_decay: float = field(default=0.01)
    save_model: bool = field(default=False)
    model_type: str = field(default="rnalm")
    token_type: str = field(default="single")
    train_from_scratch: bool = field(default=False)
    num_epochs: int = field(default=30)
    patience: int = field(default=20)
    num_workers: int = field(default=1)
    seed: int = field(default=42)
    attn_implementation: str = field(default="eager")
    dataloader_num_workers: int = field(default=4)
    dataloader_prefetch_factor: int = field(default=2)
    # New parameters for sliding window
    window_size: int = field(default=1024, metadata={"help": "Size of sliding window"})
    window_stride: int = field(default=512, metadata={"help": "Stride of sliding window"})
    max_sequence_length: int = field(default=8192, metadata={"help": "Maximum sequence length before truncation"})
    pooling_strategy: str = field(default="mean", metadata={"help": "Pooling strategy for window embeddings: mean, max, attention"})

def safe_save_model_for_hf_trainer(trainer: transformers.Trainer, output_dir: str):
    """Collects the state dict and dump to disk."""
    state_dict = trainer.model.state_dict()
    if trainer.args.should_save:
        cpu_state_dict = {key: value.cpu() for key, value in state_dict.items()}
        del state_dict
        trainer._save(output_dir, state_dict=cpu_state_dict)  # noqa

def set_seed(args):
    random.seed(args.seed)
    np.random.seed(args.seed)
    torch.manual_seed(args.seed)
    if torch.distributed.get_rank() >= 0:
        print("!!!!!!!!!!!!!", "Yes")
        torch.cuda.manual_seed_all(args.seed)

def generate_kmer_str(sequence: str, k: int) -> str:
    """Generate k-mer string from sequence."""
    if k == -1:
        return sequence
    kmers = []
    for i in range(len(sequence) - k + 1):
        kmers.append(sequence[i:i+k])
    return " ".join(kmers)

def load_or_generate_kmer(data_path: str, texts: List[str], k: int) -> List[str]:
    """Load or generate k-mer string for each rna sequence."""
    # Handle both .csv and .fa extensions
    base_path = data_path.replace(".csv", "").replace(".fa", "").replace(".fasta", "")
    kmer_path = f"{base_path}_{k}mer.json"

    if os.path.exists(kmer_path):
        logging.warning(f"Loading k-mer from {kmer_path}...")
        with open(kmer_path, "r") as f:
            kmer = json.load(f)
    else:
        logging.warning(f"Generating k-mer...")
        kmer = [generate_kmer_str(text, k) for text in texts]
        with open(kmer_path, "w") as f:
            logging.warning(f"Saving k-mer to {kmer_path}...")
            json.dump(kmer, f)
    return kmer

def load_fasta_data(fasta_path: str) -> Tuple[List[str], List[int]]:
    """Load sequences and labels from FASTA file."""
    sequences = []
    labels = []

    with open(fasta_path, 'r') as f:
        current_seq = ""
        current_label = None

        for line in f:
            line = line.strip()
            if line.startswith('>'):
                # Save previous sequence if exists
                if current_seq and current_label is not None:
                    sequences.append(current_seq.upper().replace("U", "T"))
                    labels.append(current_label)

                # Parse header for label
                # Format: >seq_000000 label=1 length=9388
                parts = line.split()
                label_part = [p for p in parts if p.startswith('label=')]
                if label_part:
                    current_label = int(label_part[0].split('=')[1])
                else:
                    current_label = 0  # Default label
                current_seq = ""
            else:
                current_seq += line

        # Don't forget the last sequence
        if current_seq and current_label is not None:
            sequences.append(current_seq.upper().replace("U", "T"))
            labels.append(current_label)

    return sequences, labels

def create_sliding_windows(sequence: str, window_size: int, stride: int, max_length: int) -> List[str]:
    """Create sliding windows from a long sequence."""
    # Truncate sequence if too long
    if len(sequence) > max_length:
        sequence = sequence[:max_length]
        logging.warning(f"Sequence truncated to {max_length} nucleotides")
    
    # If sequence is shorter than window size, return the whole sequence
    if len(sequence) <= window_size:
        return [sequence]
    
    windows = []
    start = 0
    while start < len(sequence):
        end = min(start + window_size, len(sequence))
        window = sequence[start:end]
        windows.append(window)
        
        # If this window reaches the end, break
        if end == len(sequence):
            break
            
        start += stride
    
    return windows

class AttentionPooling(torch.nn.Module):
    """Attention-based pooling for combining window embeddings."""
    def __init__(self, hidden_size):
        super().__init__()
        self.attention = torch.nn.Linear(hidden_size, 1)
        
    def forward(self, embeddings):
        # embeddings: [num_windows, hidden_size]
        attention_weights = torch.softmax(self.attention(embeddings), dim=0)
        pooled = torch.sum(attention_weights * embeddings, dim=0)
        return pooled

class SlidingWindowModel(torch.nn.Module):
    """Wrapper model that handles sliding windows and pooling."""
    def __init__(self, base_model, pooling_strategy="mean", hidden_size=768):
        super().__init__()
        self.base_model = base_model
        self.pooling_strategy = pooling_strategy
        if pooling_strategy == "attention":
            self.attention_pooling = AttentionPooling(hidden_size)
    
    def forward(self, input_ids_list, attention_mask_list, labels=None):
        """
        input_ids_list: List of input_ids for each window
        attention_mask_list: List of attention_masks for each window
        """
        window_embeddings = []
        
        for input_ids, attention_mask in zip(input_ids_list, attention_mask_list):
            # Get embeddings from base model
            outputs = self.base_model(input_ids=input_ids, attention_mask=attention_mask)
            # Use the [CLS] token embedding or pooled output
            if hasattr(outputs, 'pooler_output') and outputs.pooler_output is not None:
                embedding = outputs.pooler_output
            else:
                # Use mean pooling of last hidden states
                embedding = outputs.last_hidden_state.mean(dim=1)
            window_embeddings.append(embedding)
        
        # Stack embeddings: [num_windows, batch_size, hidden_size]
        window_embeddings = torch.stack(window_embeddings, dim=0)
        
        # Pool across windows
        if self.pooling_strategy == "mean":
            pooled_embedding = torch.mean(window_embeddings, dim=0)
        elif self.pooling_strategy == "max":
            pooled_embedding = torch.max(window_embeddings, dim=0)[0]
        elif self.pooling_strategy == "attention":
            # Apply attention pooling for each sample in batch
            batch_size = window_embeddings.shape[1]
            pooled_embeddings = []
            for i in range(batch_size):
                sample_embeddings = window_embeddings[:, i, :]  # [num_windows, hidden_size]
                pooled = self.attention_pooling(sample_embeddings)
                pooled_embeddings.append(pooled)
            pooled_embedding = torch.stack(pooled_embeddings, dim=0)
        
        # Get final predictions
        if hasattr(self.base_model, 'classifier'):
            logits = self.base_model.classifier(pooled_embedding)
        else:
            # Fallback: add a simple classifier
            if not hasattr(self, 'classifier'):
                self.classifier = torch.nn.Linear(pooled_embedding.shape[-1], 2)
            logits = self.classifier(pooled_embedding)
        
        loss = None
        if labels is not None:
            loss_fct = torch.nn.CrossEntropyLoss()
            loss = loss_fct(logits.view(-1, 2), labels.view(-1))
        
        return transformers.modeling_outputs.SequenceClassifierOutput(
            loss=loss,
            logits=logits,
            hidden_states=None,
            attentions=None,
        )

class SupervisedDataset(Dataset):
    """Dataset for supervised fine-tuning with sliding window support."""

    def __init__(self,
                 data_path: str,
                 args,
                 tokenizer: transformers.PreTrainedTokenizer,
                 kmer: int = -1):

        super(SupervisedDataset, self).__init__()

        # Determine file format and load data accordingly
        if data_path.endswith(('.fa', '.fasta')):
            # Load FASTA format
            logging.warning("Loading FASTA format data...")
            texts, labels = load_fasta_data(data_path)
            logging.warning("Perform single sequence classification...")
        else:
            # Load CSV format (legacy support)
            logging.warning("Loading CSV format data...")
            csv.field_size_limit(sys.maxsize)  # Remove field size limit
            with open(data_path, "r") as f:
                data = list(csv.reader(f))[1:]
            if len(data[0]) == 2:
                # data is in the format of [text, label]
                logging.warning("Perform single sequence classification...")
                texts = [d[0].upper().replace("U", "T") for d in data]
                labels = [int(d[1]) for d in data]
            else:
                print(len(data[0]))
                raise ValueError("Data format not supported.")

        if kmer != -1:
            # only write file on the first process
            if torch.distributed.get_rank() not in [0, -1]:
                torch.distributed.barrier()

            logging.warning(f"Using {kmer}-mer as input...")
            texts = load_or_generate_kmer(data_path, texts, kmer)

            if torch.distributed.get_rank() == 0:
                torch.distributed.barrier()

        # Store original sequences and create sliding windows
        self.original_texts = texts
        self.labels = labels
        self.num_labels = len(set(labels))
        self.args = args
        self.tokenizer = tokenizer

        # Create sliding windows for each sequence
        self.windowed_data = []
        for i, text in enumerate(texts):
            windows = create_sliding_windows(
                text,
                args.window_size,
                args.window_stride,
                args.max_sequence_length
            )
            self.windowed_data.append({
                'windows': windows,
                'label': labels[i],
                'original_length': len(text)
            })

        print(f"Dataset loaded: {len(self.windowed_data)} sequences")
        print(f"Average windows per sequence: {np.mean([len(item['windows']) for item in self.windowed_data]):.2f}")

    def __len__(self):
       return len(self.windowed_data)

    def __getitem__(self, i) -> Dict[str, torch.Tensor]:
        item = self.windowed_data[i]
        return {
            'windows': item['windows'],
            'labels': item['label'],
            'original_length': item['original_length']
        }

@dataclass
class DataCollatorForSlidingWindow(object):
    """Collate examples for sliding window fine-tuning."""

    def __init__(self, tokenizer: transformers.PreTrainedTokenizer, args):
        self.tokenizer = tokenizer
        self.args = args

    def __call__(self, instances: Sequence[Dict]) -> Dict[str, torch.Tensor]:
        # Extract data
        batch_windows = [instance['windows'] for instance in instances]
        labels = [instance['labels'] for instance in instances]

        # Tokenize all windows for all samples
        batch_input_ids = []
        batch_attention_masks = []

        max_windows = max(len(windows) for windows in batch_windows)

        for windows in batch_windows:
            # Tokenize windows for this sample
            sample_input_ids = []
            sample_attention_masks = []

            for window in windows:
                tokenized = self.tokenizer(
                    window,
                    padding='max_length',
                    max_length=self.args.window_size,
                    truncation=True,
                    return_tensors='pt'
                )
                sample_input_ids.append(tokenized['input_ids'].squeeze(0))
                sample_attention_masks.append(tokenized['attention_mask'].squeeze(0))

            # Pad to max_windows if necessary
            while len(sample_input_ids) < max_windows:
                # Add padding windows (all zeros)
                pad_length = self.args.window_size
                sample_input_ids.append(torch.zeros(pad_length, dtype=torch.long))
                sample_attention_masks.append(torch.zeros(pad_length, dtype=torch.long))

            batch_input_ids.append(torch.stack(sample_input_ids))
            batch_attention_masks.append(torch.stack(sample_attention_masks))

        # Stack into tensors: [batch_size, max_windows, window_size]
        batch_input_ids = torch.stack(batch_input_ids)
        batch_attention_masks = torch.stack(batch_attention_masks)
        labels = torch.tensor(labels, dtype=torch.long)

        return {
            'input_ids': batch_input_ids,
            'attention_mask': batch_attention_masks,
            'labels': labels,
        }

class SlidingWindowDataset(Dataset):
    """Simplified dataset that handles sliding windows internally."""

    def __init__(self,
                 data_path: str,
                 args,
                 tokenizer: transformers.PreTrainedTokenizer,
                 kmer: int = -1):

        super(SlidingWindowDataset, self).__init__()

        # Determine file format and load data accordingly
        if data_path.endswith(('.fa', '.fasta')):
            # Load FASTA format
            logging.warning("Loading FASTA format data...")
            texts, labels = load_fasta_data(data_path)
            logging.warning("Perform single sequence classification...")
        else:
            # Load CSV format (legacy support)
            logging.warning("Loading CSV format data...")
            csv.field_size_limit(sys.maxsize)  # Remove field size limit
            with open(data_path, "r") as f:
                data = list(csv.reader(f))[1:]
            if len(data[0]) == 2:
                # data is in the format of [text, label]
                logging.warning("Perform single sequence classification...")
                texts = [d[0].upper().replace("U", "T") for d in data]
                labels = [int(d[1]) for d in data]
            else:
                print(len(data[0]))
                raise ValueError("Data format not supported.")

        if kmer != -1:
            # only write file on the first process
            if torch.distributed.get_rank() not in [0, -1]:
                torch.distributed.barrier()

            logging.warning(f"Using {kmer}-mer as input...")
            texts = load_or_generate_kmer(data_path, texts, kmer)

            if torch.distributed.get_rank() == 0:
                torch.distributed.barrier()

        # Process sequences with sliding windows and tokenize immediately
        self.processed_data = []
        self.num_labels = len(set(labels))

        for i, (text, label) in enumerate(zip(texts, labels)):
            # Create sliding windows
            windows = create_sliding_windows(
                text,
                args.window_size,
                args.window_stride,
                args.max_sequence_length
            )

            # Tokenize all windows
            tokenized_windows = []
            for window in windows:
                tokenized = tokenizer(
                    window,
                    padding='max_length',
                    max_length=args.window_size,
                    truncation=True,
                    return_tensors='pt'
                )
                tokenized_windows.append({
                    'input_ids': tokenized['input_ids'].squeeze(0),
                    'attention_mask': tokenized['attention_mask'].squeeze(0)
                })

            # Pool the windows using mean pooling for now
            # This is a simplified approach - we'll compute embeddings and pool them
            self.processed_data.append({
                'windows': tokenized_windows,
                'label': label,
                'num_windows': len(tokenized_windows)
            })

        print(f"Dataset loaded: {len(self.processed_data)} sequences")
        print(f"Average windows per sequence: {np.mean([item['num_windows'] for item in self.processed_data]):.2f}")

    def __len__(self):
        return len(self.processed_data)

    def __getitem__(self, i):
        item = self.processed_data[i]
        # For now, just return the first window - we'll improve this later
        first_window = item['windows'][0]
        return {
            'input_ids': first_window['input_ids'],
            'attention_mask': first_window['attention_mask'],
            'labels': item['label']
        }

def compute_metrics(eval_pred):
    """Compute metrics for evaluation."""
    predictions, labels = eval_pred
    predictions = np.argmax(predictions, axis=1)

    accuracy = sklearn.metrics.accuracy_score(labels, predictions)
    precision = sklearn.metrics.precision_score(labels, predictions, average='weighted')
    recall = sklearn.metrics.recall_score(labels, predictions, average='weighted')
    f1 = sklearn.metrics.f1_score(labels, predictions, average='weighted')

    return {
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1': f1,
    }

def train():
    parser = transformers.HfArgumentParser((ModelArguments, DataArguments, TrainingArguments))
    model_args, data_args, training_args = parser.parse_args_into_dataclasses()
    set_seed(training_args)

    # load tokenizer
    if training_args.model_type == 'rnalm':
        tokenizer = EsmTokenizer.from_pretrained(
            model_args.model_name_or_path,
            cache_dir=training_args.cache_dir,
            model_max_length=training_args.model_max_length,
            padding_side="right",
            use_fast=True,
            trust_remote_code=True,
        )
    elif training_args.model_type in ['rna-fm','rnabert','rnamsm','splicebert-human510','splicebert-ms510','splicebert-ms1024','utrbert-3mer','utrbert-4mer','utrbert-5mer','utrbert-6mer','utr-lm-mrl','utr-lm-te-el']:
        if not TOKENIZER_AVAILABLE:
            raise ImportError(f"OpenRnaLMTokenizer not available for model type {training_args.model_type}")
        tokenizer = OpenRnaLMTokenizer.from_pretrained(
            model_args.model_name_or_path,
            cache_dir=training_args.cache_dir,
            model_max_length=training_args.model_max_length,
            padding_side="right",
            use_fast=True,
            trust_remote_code=True,
        )
    else:
        tokenizer = transformers.AutoTokenizer.from_pretrained(
            model_args.model_name_or_path,
            cache_dir=training_args.cache_dir,
            model_max_length=training_args.model_max_length,
            padding_side="right",
            use_fast=True,
            trust_remote_code=True,
        )

    if "InstaDeepAI" in model_args.model_name_or_path:
        tokenizer.eos_token = tokenizer.pad_token
    if 'mer' in training_args.token_type:
        data_args.kmer=int(training_args.token_type[0])

    # define datasets and data collator
    train_dataset = SlidingWindowDataset(tokenizer=tokenizer, args=training_args,
                                        data_path=os.path.join(data_args.data_path, data_args.data_train_path),
                                        kmer=data_args.kmer)
    val_dataset = SlidingWindowDataset(tokenizer=tokenizer, args=training_args,
                                      data_path=os.path.join(data_args.data_path, data_args.data_val_path),
                                      kmer=data_args.kmer)
    test_dataset = SlidingWindowDataset(tokenizer=tokenizer, args=training_args,
                                       data_path=os.path.join(data_args.data_path, data_args.data_test_path),
                                       kmer=data_args.kmer)
    data_collator = transformers.DataCollatorWithPadding(tokenizer=tokenizer)
    print(f'# train: {len(train_dataset)}, val:{len(val_dataset)}, test:{len(test_dataset)}')

    # load base model
    if training_args.model_type == 'rnalm':
        if not RNALM_AVAILABLE:
            raise ImportError(f"RnaLm model not available")
        if training_args.train_from_scratch:
            print('Train from scratch')
            config = RnaLmConfig.from_pretrained(model_args.model_name_or_path,
                num_labels=train_dataset.num_labels,
                token_type=training_args.token_type,
                problem_type="single_label_classification",
                attn_implementation=training_args.attn_implementation,
                )
            print(config)
            base_model = RnaLmForSequenceClassification(config)
        else:
            print(f'Loading {training_args.model_type} model')
            base_model = RnaLmForSequenceClassification.from_pretrained(
                model_args.model_name_or_path,
                cache_dir=training_args.cache_dir,
                num_labels=train_dataset.num_labels,
                problem_type="single_label_classification",
                trust_remote_code=True,
                attn_implementation=training_args.attn_implementation,
            )
    elif training_args.model_type == 'rna-fm':
        if not RNAFM_AVAILABLE:
            raise ImportError(f"RnaFm model not available")
        print(f'Loading {training_args.model_type} model')
        base_model = RnaFmForSequenceClassification.from_pretrained(
            model_args.model_name_or_path,
            cache_dir=training_args.cache_dir,
            num_labels=train_dataset.num_labels,
            problem_type="single_label_classification",
            trust_remote_code=True,
        )
    elif training_args.model_type == 'rnabert':
        if not RNABERT_AVAILABLE:
            raise ImportError(f"RnaBert model not available")
        print(f'Loading {training_args.model_type} model')
        base_model = RnaBertForSequenceClassification.from_pretrained(
            model_args.model_name_or_path,
            cache_dir=training_args.cache_dir,
            num_labels=train_dataset.num_labels,
            problem_type="single_label_classification",
            trust_remote_code=True,
        )
    elif training_args.model_type == 'rnamsm':
        if not RNAMSM_AVAILABLE:
            raise ImportError(f"RnaMsm model not available")
        print(f'Loading {training_args.model_type} model')
        base_model = RnaMsmForSequenceClassification.from_pretrained(
            model_args.model_name_or_path,
            cache_dir=training_args.cache_dir,
            num_labels=train_dataset.num_labels,
            problem_type="single_label_classification",
            trust_remote_code=True,
        )
    elif 'splicebert' in training_args.model_type:
        if not SPLICEBERT_AVAILABLE:
            raise ImportError(f"SpliceBert model not available")
        print(f'Loading {training_args.model_type} model')
        base_model = SpliceBertForSequenceClassification.from_pretrained(
            model_args.model_name_or_path,
            cache_dir=training_args.cache_dir,
            num_labels=train_dataset.num_labels,
            problem_type="single_label_classification",
            trust_remote_code=True,
        )
    elif 'utrbert' in training_args.model_type:
        if not UTRBERT_AVAILABLE:
            raise ImportError(f"UtrBert model not available")
        print(f'Loading {training_args.model_type} model')
        base_model = UtrBertForSequenceClassification.from_pretrained(
            model_args.model_name_or_path,
            cache_dir=training_args.cache_dir,
            num_labels=train_dataset.num_labels,
            problem_type="single_label_classification",
            trust_remote_code=True,
        )
    elif 'utr-lm' in training_args.model_type:
        if not UTRLM_AVAILABLE:
            raise ImportError(f"UtrLm model not available")
        print(f'Loading {training_args.model_type} model')
        base_model = UtrLmForSequenceClassification.from_pretrained(
            model_args.model_name_or_path,
            cache_dir=training_args.cache_dir,
            num_labels=train_dataset.num_labels,
            problem_type="single_label_classification",
            trust_remote_code=True,
        )
    else:
        raise ValueError(f"Unsupported model type: {training_args.model_type}")

    # define trainer (simplified approach using standard trainer)
    trainer = Trainer(
        model=base_model,
        tokenizer=tokenizer,
        args=training_args,
        compute_metrics=compute_metrics,
        train_dataset=train_dataset,
        eval_dataset=val_dataset,
        data_collator=data_collator,
        callbacks=[early_stopping],
    )

    trainer.train()

    if training_args.save_model:
        trainer.save_state()
        safe_save_model_for_hf_trainer(trainer=trainer, output_dir=training_args.output_dir)

    # Evaluate on test set
    print("Evaluating on test set...")
    test_results = trainer.evaluate(eval_dataset=test_dataset)
    print("Test Results:", test_results)

if __name__ == "__main__":
    train()
