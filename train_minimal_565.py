#!/usr/bin/env python3
"""
Minimal training script for 565_hum_label.fa with extreme memory optimization
"""

import os
import sys
import torch
import logging
from dataclasses import dataclass, field
from typing import Optional
from transformers import Trainer, TrainingArguments, HfArgumentParser
from sklearn.metrics import accuracy_score, precision_recall_fscore_support

# Add paths
sys.path.append('.')
sys.path.append('downstream')

# Import custom modules
from sliding_window_dataset import load_fasta_data

# Import models and tokenizers
try:
    from tokenizer.tokenization_opensource import OpenRnaLMTokenizer
    TOKENIZER_AVAILABLE = True
except ImportError:
    TOKENIZER_AVAILABLE = False

try:
    from model.rnafm.modeling_rnafm import RnaFmForSequenceClassification
    RNAFM_AVAILABLE = True
except ImportError:
    RNAFM_AVAILABLE = False

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class ModelArguments:
    model_name_or_path: str = field(metadata={"help": "Path to pretrained model"})

@dataclass
class DataArguments:
    data_path: str = field(metadata={"help": "Path to the data directory"})
    data_train_path: str = field(metadata={"help": "Path to the training data file"})
    data_val_path: str = field(metadata={"help": "Path to the validation data file"})

class MinimalDataset(torch.utils.data.Dataset):
    """Minimal dataset that processes sequences without sliding windows."""
    
    def __init__(self, sequences, labels, tokenizer, max_length=512):
        self.sequences = sequences
        self.labels = labels
        self.tokenizer = tokenizer
        self.max_length = max_length
    
    def __len__(self):
        return len(self.sequences)
    
    def __getitem__(self, idx):
        sequence = self.sequences[idx]
        label = self.labels[idx]
        
        # Truncate sequence if too long
        if len(sequence) > self.max_length:
            sequence = sequence[:self.max_length]
        
        # Tokenize
        encoding = self.tokenizer(
            sequence,
            truncation=True,
            padding='max_length',
            max_length=self.max_length,
            return_tensors='pt'
        )
        
        return {
            'input_ids': encoding['input_ids'].squeeze(),
            'attention_mask': encoding['attention_mask'].squeeze(),
            'labels': torch.tensor(label, dtype=torch.long)
        }

def compute_metrics(eval_pred):
    """Compute metrics for evaluation."""
    predictions, labels = eval_pred
    predictions = predictions.argmax(axis=1)
    
    accuracy = accuracy_score(labels, predictions)
    precision, recall, f1, _ = precision_recall_fscore_support(labels, predictions, average='binary')
    
    return {
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1': f1
    }

def main():
    """Main training function."""
    parser = HfArgumentParser((ModelArguments, DataArguments, TrainingArguments))
    model_args, data_args, training_args = parser.parse_args_into_dataclasses()
    
    logger.info("🧬 Starting minimal training for RNA-FM")
    logger.info(f"Model path: {model_args.model_name_or_path}")
    logger.info(f"Output directory: {training_args.output_dir}")
    
    # Load tokenizer
    logger.info("Loading tokenizer...")
    tokenizer = OpenRnaLMTokenizer.from_pretrained(
        "./checkpoint/opensource/rna-fm",
        model_max_length=512,
        padding_side="right",
        use_fast=True,
        trust_remote_code=True,
    )
    
    # Load datasets
    logger.info("Loading datasets...")
    train_sequences, train_labels = load_fasta_data(
        os.path.join(data_args.data_path, data_args.data_train_path)
    )
    val_sequences, val_labels = load_fasta_data(
        os.path.join(data_args.data_path, data_args.data_val_path)
    )
    
    logger.info(f"Training set: {len(train_sequences)} sequences")
    logger.info(f"Validation set: {len(val_sequences)} sequences")
    
    # Create minimal datasets (no sliding windows)
    train_dataset = MinimalDataset(train_sequences, train_labels, tokenizer, max_length=512)
    val_dataset = MinimalDataset(val_sequences, val_labels, tokenizer, max_length=512)
    
    # Load model
    logger.info("Loading RNA-FM model...")
    model = RnaFmForSequenceClassification.from_pretrained(
        model_args.model_name_or_path,
        num_labels=2,
        problem_type="single_label_classification",
        trust_remote_code=True,
    )
    
    # Count parameters
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    logger.info(f"Model parameters: {total_params:,} total, {trainable_params:,} trainable")
    
    # Initialize trainer
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=train_dataset,
        eval_dataset=val_dataset,
        tokenizer=tokenizer,
        compute_metrics=compute_metrics,
    )
    
    # Training
    logger.info("Starting training...")
    train_result = trainer.train()
    
    # Save model
    logger.info(f"Saving model to {training_args.output_dir}")
    trainer.save_model()
    tokenizer.save_pretrained(training_args.output_dir)
    
    # Evaluation
    logger.info("Running final evaluation...")
    eval_result = trainer.evaluate()
    
    # Print results
    logger.info("Training completed!")
    logger.info(f"Training loss: {train_result.training_loss:.4f}")
    logger.info("Evaluation results:")
    for key, value in eval_result.items():
        logger.info(f"  {key}: {value:.4f}")
    
    # Save summary
    summary_path = os.path.join(training_args.output_dir, "training_summary.txt")
    with open(summary_path, 'w') as f:
        f.write(f"Minimal Training Results - RNA-FM\n")
        f.write("=" * 40 + "\n\n")
        f.write(f"Model: {model_args.model_name_or_path}\n")
        f.write(f"Training data: {data_args.data_train_path}\n")
        f.write(f"Validation data: {data_args.data_val_path}\n")
        f.write(f"Training sequences: {len(train_sequences)}\n")
        f.write(f"Validation sequences: {len(val_sequences)}\n")
        f.write(f"Max sequence length: 512\n")
        f.write(f"Training loss: {train_result.training_loss:.4f}\n")
        f.write("\nEvaluation metrics:\n")
        for key, value in eval_result.items():
            f.write(f"  {key}: {value:.4f}\n")
    
    logger.info(f"Training summary saved to: {summary_path}")

if __name__ == "__main__":
    main()
