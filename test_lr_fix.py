#!/usr/bin/env python3
"""
Test script to verify learning rate fix with CPU training.
"""

import os
import sys
import torch
import logging
from dataclasses import dataclass, field
from typing import Optional
from transformers import Trainer, TrainingArguments, HfArgumentParser

# Force CPU usage
os.environ["CUDA_VISIBLE_DEVICES"] = ""

# Add paths
sys.path.append('.')
sys.path.append('downstream')

# Import custom modules
from sliding_window_dataset import SlidingWindowDataset, load_fasta_data
from sequence_level_model import SequenceLevelAggregationModel, SequenceLevelDataCollator

# Import models and tokenizers
try:
    from tokenizer.tokenization_opensource import OpenRnaLMTokenizer
    TOKENIZER_AVAILABLE = True
except ImportError:
    TOKENIZER_AVAILABLE = False
    print("Warning: OpenRnaLMTokenizer not available")

try:
    from model.rnafm.modeling_rnafm import RnaFmForSequenceClassification
    RNAFM_AVAILABLE = True
except ImportError:
    RNAFM_AVAILABLE = False
    print("Warning: RnaFm model not available")

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def load_tokenizer(model_type: str, model_path: str):
    """Load the appropriate tokenizer."""
    if model_type == 'rna-fm':
        tokenizer_path = './checkpoint/opensource/rna-fm/'
    elif model_type == 'splicebert-ms1024':
        tokenizer_path = './checkpoint/opensource/splicebert-ms1024/'
    else:
        tokenizer_path = model_path

    tokenizer = OpenRnaLMTokenizer.from_pretrained(
        tokenizer_path,
        model_max_length=512,  # Smaller for CPU test
        padding_side="right",
        use_fast=True,
        trust_remote_code=True,
    )
    return tokenizer

def load_model(model_type: str, model_path: str, num_labels: int = 2):
    """Load the appropriate model."""
    if model_type == 'rna-fm':
        if not RNAFM_AVAILABLE:
            raise ImportError("RnaFm model not available")
        model = RnaFmForSequenceClassification.from_pretrained(
            model_path,
            num_labels=num_labels,
            problem_type="single_label_classification",
            trust_remote_code=True,
        )
    else:
        raise ValueError(f"Unsupported model type: {model_type}")
    
    return model

class CustomTrainer(Trainer):
    """Custom trainer to log learning rate."""
    
    def log(self, logs):
        """Override log method to capture learning rate."""
        if 'learning_rate' in logs:
            print(f"Step {self.state.global_step}: LR = {logs['learning_rate']:.2e}, Loss = {logs.get('loss', 'N/A')}")
        super().log(logs)

def main():
    """Test learning rate fix with CPU training."""
    
    # Test parameters
    model_type = "rna-fm"
    model_path = "./checkpoint/opensource/rna-fm"
    data_path = "data/cv_splits_565"
    train_file = "fold_0_train.fa"
    val_file = "fold_0_val.fa"
    
    logger.info("🧪 Learning Rate Fix Test (CPU)")
    logger.info("=" * 50)
    
    # Load tokenizer
    logger.info("Loading tokenizer...")
    tokenizer = load_tokenizer(model_type, model_path)
    
    # Load datasets (very small subset for CPU test)
    logger.info("Loading datasets...")
    train_sequences, train_labels = load_fasta_data(os.path.join(data_path, train_file))
    val_sequences, val_labels = load_fasta_data(os.path.join(data_path, val_file))
    
    # Use only first 3 samples for CPU test
    train_sequences = train_sequences[:3]
    train_labels = train_labels[:3]
    val_sequences = val_sequences[:2]
    val_labels = val_labels[:2]
    
    logger.info(f"Training set: {len(train_sequences)} sequences")
    logger.info(f"Validation set: {len(val_sequences)} sequences")
    
    # Create datasets with smaller window size for CPU
    train_dataset = SlidingWindowDataset(
        sequences=train_sequences,
        labels=train_labels,
        tokenizer=tokenizer,
        window_size=512,  # Smaller for CPU
        window_stride=256,
        max_length=1024   # Much smaller for CPU
    )
    
    val_dataset = SlidingWindowDataset(
        sequences=val_sequences,
        labels=val_labels,
        tokenizer=tokenizer,
        window_size=512,
        window_stride=256,
        max_length=1024
    )
    
    # Load base model
    logger.info(f"Loading {model_type} model...")
    base_model = load_model(model_type, model_path, num_labels=2)
    
    # Wrap with sequence-level aggregation model
    model = SequenceLevelAggregationModel(
        base_model=base_model,
        pooling_strategy="mean",
        num_labels=2,
        dropout_rate=0.1
    )
    
    # Training arguments - same as the fixed script
    training_args = TrainingArguments(
        output_dir="./test_lr_output",
        num_train_epochs=1,
        per_device_train_batch_size=1,
        per_device_eval_batch_size=1,
        gradient_accumulation_steps=2,
        learning_rate=2e-5,
        warmup_steps=5,
        lr_scheduler_type="linear",  # This was added in the fix
        max_grad_norm=1.0,           # This was added in the fix
        logging_steps=1,
        eval_steps=3,
        evaluation_strategy="steps",
        save_steps=10,
        save_strategy="steps",
        load_best_model_at_end=False,
        dataloader_num_workers=0,
        seed=42,
        overwrite_output_dir=True,
        report_to=None,
        # Note: no fp16 (this was removed in the fix)
    )
    
    # Data collator
    data_collator = SequenceLevelDataCollator(tokenizer=tokenizer)
    
    # Initialize custom trainer to log learning rate
    trainer = CustomTrainer(
        model=model,
        args=training_args,
        train_dataset=train_dataset,
        eval_dataset=val_dataset,
        tokenizer=tokenizer,
        data_collator=data_collator,
    )
    
    # Test training
    logger.info("Starting learning rate test...")
    try:
        train_result = trainer.train()
        logger.info("✅ Learning rate test completed successfully!")
        logger.info(f"Final training loss: {train_result.training_loss:.4f}")
        
    except Exception as e:
        logger.error(f"❌ Learning rate test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
